"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { User, Mail, MapPin, Coins, Check, Lock } from "lucide-react";

export interface StartupContactDetails {
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  startup: string;
  website?: string;
  hq?: string;
  startupId: string;
}

interface StartupContactCardProps {
  contact: StartupContactDetails;
  onSelect: (selectedContact: StartupContactDetails) => void;
  requiresToken?: boolean;
  showTokenCost?: boolean;
  isSelected?: boolean;
  isUnlocked?: boolean;
  firstContactSelected?: boolean;
}

const StartupContactCard: React.FC<StartupContactCardProps> = ({
  contact,
  onSelect,
  requiresToken = false,
  showTokenCost = false,
  isSelected = false,
  isUnlocked = false,
  firstContactSelected = false,
}) => {
  const [acknowledged, setAcknowledged] = useState(false);
  const hasName = contact.firstName || contact.lastName;

  const handleSelect = () => {
    if (!isUnlocked && requiresToken) {
      // If contact is locked and requires token, trigger unlock
      onSelect(contact);
      setAcknowledged(true);
      setTimeout(() => setAcknowledged(false), 2000);
    } else {
      // If contact is unlocked or doesn't require token, just select
      onSelect(contact);
    }
  };

  const getButtonText = () => {
    if (isSelected) {
      return (
        <div className="flex items-center">
          <Mail className="h-4 w-4 mr-1.5" />
          Contact Again
        </div>
      );
    }
    if (acknowledged) {
      return (
        <div className="flex items-center">
          <Coins className="h-4 w-4 mr-1.5" />
          Token Deducted
        </div>
      );
    }
    if (requiresToken || (firstContactSelected && !isUnlocked)) {
      return (
        <div className="flex items-center">
          <Lock className="h-4 w-4 mr-1.5" />
          Unlock (1 Token)
        </div>
      );
    }
    return (
      <div className="flex items-center">
        <Mail className="h-4 w-4 mr-1.5" />
        Contact
      </div>
    );
  };

  return (
    <Card className={`border transition-colors ${
      isSelected 
        ? "border-green-400 bg-green-50" 
        : "border-blue-200 hover:border-blue-400"
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center shrink-0 border border-blue-100">
            {hasName ? (
              <span className="text-blue-700 font-medium">
                {contact.firstName?.[0]}{contact.lastName?.[0]}
              </span>
            ) : (
              <Mail className="h-5 w-5 text-blue-600" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="font-medium text-blue-900">
              {hasName ? `${contact.firstName} ${contact.lastName}` : "Contact"}
            </div>
            {contact.role && (
              <p className="text-sm text-gray-600 truncate">{contact.role}</p>
            )}
            <div className="text-sm text-blue-600">{contact.email}</div>
            <div className="mt-2">
              <Button
                className={`w-full ${
                  isSelected 
                    ? "bg-green-600 hover:bg-green-700" 
                    : "bg-blue-600 hover:bg-blue-700"
                } text-white`}
                size="sm"
                onClick={handleSelect}
                disabled={acknowledged}
              >
                {getButtonText()}
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StartupContactCard;