#!/usr/bin/env node

/**
 * Test script to verify the Stripe webhook endpoint is accessible
 * Run this script to check if your webhook endpoint is responding correctly
 * 
 * Usage: node scripts/test-webhook-endpoint.js [URL]
 * Example: node scripts/test-webhook-endpoint.js http://localhost:3000
 */

const https = require('https');
const http = require('http');

const baseUrl = process.argv[2] || 'http://localhost:3000';
const webhookUrl = `${baseUrl}/api/webhooks/stripe`;

console.log('🧪 Testing Stripe Webhook Endpoint...\n');
console.log(`📍 Testing URL: ${webhookUrl}\n`);

// Test GET request (should work)
function testGet() {
  return new Promise((resolve, reject) => {
    const protocol = webhookUrl.startsWith('https') ? https : http;
    
    console.log('1️⃣ Testing GET request...');
    
    const req = protocol.get(webhookUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode}`);
        console.log(`   Response: ${data}`);
        
        if (res.statusCode === 200) {
          console.log('   ✅ GET request successful\n');
          resolve(true);
        } else {
          console.log('   ❌ GET request failed\n');
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`   ❌ GET request error: ${error.message}\n`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('   ❌ GET request timeout\n');
      req.destroy();
      resolve(false);
    });
  });
}

// Test POST request (should fail with 400 due to missing signature, but not 405)
function testPost() {
  return new Promise((resolve, reject) => {
    const protocol = webhookUrl.startsWith('https') ? https : http;
    const url = new URL(webhookUrl);
    
    console.log('2️⃣ Testing POST request (without signature)...');
    
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': 2
      }
    };
    
    const req = protocol.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`   Status: ${res.statusCode}`);
        console.log(`   Response: ${data}`);
        
        if (res.statusCode === 400) {
          console.log('   ✅ POST request correctly rejected (missing signature)\n');
          resolve(true);
        } else if (res.statusCode === 405) {
          console.log('   ❌ POST request failed with 405 Method Not Allowed\n');
          console.log('   🔧 This indicates a middleware or routing issue\n');
          resolve(false);
        } else {
          console.log(`   ⚠️  Unexpected status code: ${res.statusCode}\n`);
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`   ❌ POST request error: ${error.message}\n`);
      resolve(false);
    });
    
    req.setTimeout(5000, () => {
      console.log('   ❌ POST request timeout\n');
      req.destroy();
      resolve(false);
    });
    
    // Send empty JSON body
    req.write('{}');
    req.end();
  });
}

async function runTests() {
  const getResult = await testGet();
  const postResult = await testPost();
  
  console.log('📊 Test Results:');
  console.log(`   GET request:  ${getResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   POST request: ${postResult ? '✅ PASS' : '❌ FAIL'}`);
  
  if (getResult && postResult) {
    console.log('\n🎉 Webhook endpoint is working correctly!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Update your Stripe webhook URL to point to this endpoint');
    console.log('   2. Make sure your environment variables are set correctly');
    console.log('   3. Test a real subscription purchase');
  } else {
    console.log('\n❌ Webhook endpoint has issues!');
    console.log('\n🔧 Troubleshooting:');
    if (!getResult) {
      console.log('   • Check if your development server is running');
      console.log('   • Verify the URL is correct');
      console.log('   • Check for any middleware blocking the endpoint');
    }
    if (!postResult) {
      console.log('   • Check middleware configuration');
      console.log('   • Verify the route file exists and exports POST method');
      console.log('   • Check for any CORS or security headers blocking requests');
    }
  }
  
  console.log('\n🔗 Useful Commands:');
  console.log('   • Start dev server: npm run dev');
  console.log('   • Check logs: Check your terminal where the dev server is running');
  console.log('   • Test in browser: Visit the GET URL in your browser');
}
