"use client";
import React, { useState, useEffect, FormEvent, ChangeEvent } from 'react';
import { motion } from 'framer-motion';
import { Check, UserPlus } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { MultiStepLoader as Loader } from '@/components/ui/multi-step-loader';

interface OutreachPlannerProps {
    internshipType: string;
    timeline: string;
    planTitle: string;
}

const OutreachPlanner = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [outreachPlannerFormData, setoutreachPlannerFormData] = useState<OutreachPlannerProps>({
        internshipType: '',
        timeline: '',
        planTitle: '',
    });

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        setIsLoading(true);
        console.log(outreachPlannerFormData);
    }

    const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setoutreachPlannerFormData({ ...outreachPlannerFormData, [name]: value });
    }

    return (
        <div className="min-h-screen flex flex-col bg-gradient-to-b from-purple-500 to-blue-200 font-sans">
            <Navbar />
            {/* <Loader loadingStates={loadingStates} loading={isLoading} duration={1200} /> */}

            <main className="flex-grow container mx-auto px-4 py-12">
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                    className="text-center mb-12"
                >
                    <h1 className="text-6xl font-bold text-white mb-4 pt-24">
                        Your Outreach Planner
                    </h1>
                    <p className="text-xl text-white opacity-80">
                        Figure out how you&apos;re going to get the internship you&apos;ve been dreaming about. We&apos;ll handle the rest.
                    </p>
                </motion.div>

                <div className="grid md:grid-cols-2 gap-12 pt-12">
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        className="bg-white rounded-lg shadow-xl p-8 mt-0 my-auto"
                    >
                        <h2 className="text-3xl font-semibold mb-6 text-blue-900">
                            Our Outreach Planner Will Help You:
                        </h2>
                        <ul className="space-y-4">
                            {[
                                'Organize your outreach efforts with multiple different opportunities',
                                'Automatically research and send emails on behalf of you',
                                'Help you keep track of successful leads',
                                'Provide you access to our internal network of emails and connections',
                            ].map((item, index) => (
                                <motion.li
                                    key={index}
                                    className="flex items-center text-blue-700"
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                                >
                                    <Check className="w-5 h-5 mr-3 text-blue-500 flex-shrink-0" />
                                    {item}
                                </motion.li>
                            ))}
                        </ul>
                    </motion.div>

                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                        className="bg-white rounded-lg shadow-xl p-8"
                    >
                        <div>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <h2 className="text-3xl font-semibold mb-6 text-blue-900">
                                    Generate Your Outreach Plan
                                </h2>

                                <div>
                                    <label
                                        htmlFor="planTitle"
                                        className="block text-sm font-medium text-blue-700 mb-1"
                                    >
                                        Plan Title
                                    </label>
                                    <input
                                        id="planTitle"
                                        name="planTitle"
                                        value={outreachPlannerFormData.planTitle}
                                        onChange={handleInputChange}
                                        required
                                        placeholder="Give your plan a title"
                                        className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label
                                            htmlFor="internshipType"
                                            className="block text-sm font-medium text-blue-700 mb-1"
                                        >
                                            Internship Type
                                        </label>
                                        <select
                                            id="internshipType"
                                            name="internshipType"
                                            value={outreachPlannerFormData.internshipType}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="">Select an internship type</option>
                                            <option value="clinical">Clinical</option>
                                            <option value="research">Research</option>
                                            <option value="industry">Industry</option>
                                            <option value="publicHealth">Public Health</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label
                                            htmlFor="timeline"
                                            className="block text-sm font-medium text-blue-700 mb-1"
                                        >
                                            Timeline
                                        </label>
                                        <select
                                            id="timeline"
                                            name="timeline"
                                            value={outreachPlannerFormData.timeline}
                                            onChange={handleInputChange}
                                            required
                                            className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        >
                                            <option value="">Select an internship type</option>
                                            <option value="Summer 2025">Summer 2025</option>
                                            <option value="School Year">School Year</option>
                                            <option value="Year Long">Year Long</option>
                                        </select>
                                    </div>
                                </div>

                                <button
                                    type="submit"
                                    disabled={isLoading}
                                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 rounded-md focus:outline-none focus:ring-4 focus:ring-blue-400 disabled:opacity-50"
                                >
                                    <UserPlus className="inline-block w-5 h-5 mr-2" />
                                    {isLoading ? 'Creating Plan...' : 'Create Your Plan'}
                                </button>
                            </form>

                        </div>
                    </motion.div>
                </div>
            </main>
            <Footer className="bg-transparent text-white p-8 transition-all duration-300 ease-in-out mt-auto pt-24" />
        </div>
    );
}

export default OutreachPlanner;