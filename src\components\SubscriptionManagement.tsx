'use client';

import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, ArrowRight, Clock, Shield, Rocket } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { useRouter } from 'next/navigation';

const SubscriptionManagement = () => {
  const { planType, status, currentPeriodEnd, isLoading, refresh } = useSubscription();
  const router = useRouter();

  const handleManageSubscription = async () => {
    if (planType === 'free') {
      router.push('/pricing');
      return;
    }

    try {
      const response = await fetch('/api/create-portal-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      if (data.url) {
        window.location.href = data.url;
      } else {
        console.error('No URL returned from portal session');
        router.push('/pricing');
      }
    } catch (error) {
      console.error('Error creating portal session:', error);
      router.push('/pricing');
    }
  };

  // Refresh subscription data when component mounts
  useEffect(() => {
    // Initial data load
    const initialLoad = async () => {
      try {
        // Verify subscription directly with Stripe
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/verify-subscription?t=${timestamp}`);
        if (response.ok) {
          // This will update the database with the correct subscription data
          const data = await response.json();
          console.log('Subscription verification result:', data);
        }
      } catch (error) {
        console.error('Error verifying subscription:', error);
      }
    };

    // Run initial load
    initialLoad();

    // Set up a refresh interval - but don't call refresh() directly in the effect
    const interval = setInterval(() => {
      // Use a timestamp to prevent caching
      const timestamp = new Date().getTime();
      fetch(`/api/verify-subscription?t=${timestamp}`)
        .then(response => response.json())
        .then(data => {
          console.log('Periodic subscription check:', data);
          if (data.verified) {
            // Only refresh the UI if we have verified data
            refresh();
          }
        })
        .catch(error => {
          console.error('Error in periodic subscription check:', error);
        });
    }, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, []); // Remove refresh from dependencies

  const getPlanDetails = () => {
    switch (planType) {
      case 'basic_monthly':
        return {
          name: 'Basic Monthly',
          price: '$4.99/month',
          icon: Shield,
          color: 'bg-blue-500',
        };
      case 'basic_yearly':
        return {
          name: 'Basic Yearly',
          price: '$47.99/year',
          icon: Shield,
          color: 'bg-blue-500',
        };
      case 'premium_monthly':
        return {
          name: 'Premium Monthly',
          price: '$9.99/month',
          icon: Rocket,
          color: 'bg-blue-600',
        };
      case 'premium_yearly':
        return {
          name: 'Premium Yearly',
          price: '$95.99/year',
          icon: Rocket,
          color: 'bg-blue-600',
        };
      default:
        return {
          name: 'Free',
          price: '$0',
          icon: Clock,
          color: 'bg-blue-400',
        };
    }
  };

  const plan = getPlanDetails();
  const PlanIcon = plan.icon;
  const isActive = status === 'active' || status === 'trialing';
  const formattedEndDate = currentPeriodEnd
    ? new Date(currentPeriodEnd).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : null;

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">Loading subscription information...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h3 className="text-lg font-medium">Subscription Management</h3>
        <Button
          variant="outline"
          className="text-blue-600 border-blue-600 hover:bg-blue-50"
          onClick={() => router.push('/pricing')}
        >
          View Plans
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>

      <Card className="overflow-hidden">
        <div className={`${plan.color} p-6 text-white`}>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center">
              <PlanIcon className="h-5 w-5 text-white" />
            </div>
            <div>
              <h4 className="text-xl font-medium">{plan.name}</h4>
              <p className="text-sm opacity-90">{plan.price}</p>
            </div>
          </div>

          <div className="flex items-center gap-2 mt-4">
            <Badge className="bg-white/20 hover:bg-white/30 text-white">
              {isActive ? 'Active' : 'Inactive'}
            </Badge>
            {formattedEndDate && (
              <span className="text-sm opacity-80">
                {isActive ? 'Renews' : 'Expires'} on {formattedEndDate}
              </span>
            )}
          </div>
        </div>

        <div className="p-6">
          <h5 className="text-sm font-medium text-gray-500 mb-4">Plan Features</h5>

          <div className="space-y-3">
            {planType === 'free' && (
              <>
                <Feature text="1 AI Generated Cold Call Script" />
                <Feature text="5 Tokens per month" />
                <Feature text="30 Displayed Nonmed ECs" />
                <Feature text="30 Displayed Medical ECs" />
                <Feature text="Quick Apply to up to 5 jobs" />
              </>
            )}

            {(planType === 'basic_monthly' || planType === 'basic_yearly') && (
              <>
                <Feature text="3 AI Generated Cold Call Scripts per month" />
                <Feature text="15 Tokens per month" />
                <Feature text="60 Displayed Nonmed ECs" />
                <Feature text="60 Displayed Medical ECs" />
                <Feature text="Quick Apply to 30 jobs per month" />
                <Feature text="AI Resume Scorer (2x/month)" />
                <Feature text="Early Access to Job Postings (24h)" />
              </>
            )}

            {(planType === 'premium_monthly' || planType === 'premium_yearly') && (
              <>
                <Feature text="10 AI Generated Cold Call Scripts per month" />
                <Feature text="50 Tokens per month" />
                <Feature text="All Displayed Medical & Nonmed ECs" />
                <Feature text="Apply to unlimited jobs" />
                <Feature text="Custom Profile Banner" comingSoon />
                <Feature text="Priority Applicant Status" comingSoon />
                <Feature text="AI Resume Scorer (5x/month)" comingSoon />
                <Feature text="AI Job & Extracurricular Matching" comingSoon />
              </>
            )}
          </div>

          <div className="mt-6 pt-6 border-t border-gray-100">
            <Button 
              onClick={handleManageSubscription}
              disabled={isLoading}
            >
              {isLoading ? 'Loading...' : planType === 'free' ? 'Upgrade Plan' : 'Manage Billing'}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

interface FeatureProps {
  text: string;
  comingSoon?: boolean;
}

const Feature = ({ text, comingSoon }: FeatureProps) => (
  <div className="flex items-start">
    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
    <span className="text-gray-700 text-sm">
      {text}
      {comingSoon && (
        <span className="ml-1 text-xs text-blue-500">(Coming Soon)</span>
      )}
    </span>
  </div>
);

export default SubscriptionManagement;
