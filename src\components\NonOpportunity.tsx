'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import {
  MapPinIcon,
  CalendarIcon,
  TagIcon,
  UsersIcon,
  GraduationCapIcon,
  DollarSignIcon,
  XIcon,
  StarIcon,
  BuildingIcon,
  ExternalLinkIcon,
  SparklesIcon,
  Bookmark,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '../../supabase/supabaseClient';
import { Opportunity } from './types/NonMedOpportunity';
import { motion } from 'framer-motion';

interface OpportunityCardProps {
  opportunity: Opportunity;
}

const OpportunityCard: React.FC<OpportunityCardProps> = ({ opportunity }) => {
  const [selectedOpportunity, setSelectedOpportunity] = useState<Opportunity | null>(null);
  
  // For expanding location display if there are multiple
  const [showAllLocations, setShowAllLocations] = useState(false);
  
  // Track bookmarked IDs fetched from Firestore
  const [bookmarkedIds, setBookmarkedIds] = useState<string[]>([]);
  
  // Get logged-in user from context
  const { user } = useAuth();

  /* ---------------------------------------------
      1) FETCH USER'S BOOKMARKS ON MOUNT
  --------------------------------------------- */
  useEffect(() => {
    if (!user) return;
    const fetchBookmarks = async () => {
      const { data: bookmarkRows, error } = await supabase
        .from('bookmarks')
        .select('opportunity_id')
        .eq('user_id', user.id);
      if (error) {
        console.error('Error fetching bookmarks:', error);
      } else if (bookmarkRows) {
        setBookmarkedIds(bookmarkRows.map((row: any) => row.opportunity_id));
      }
    };
    fetchBookmarks();
  }, [user]);
  
  /* ---------------------------------------------
      2) TOGGLE BOOKMARK
  --------------------------------------------- */
  const toggleBookmark = async (e: React.MouseEvent, opportunityId: string) => {
    e.stopPropagation();
    if (!user) {
      alert('Please log in to bookmark items.');
      return;
    }
  
    try {
      const { data: existingBookmark, error: fetchError } = await supabase
        .from('bookmarks')
        .select('id')
        .eq('user_id', user.id)
        .eq('opportunity_id', opportunityId)
        .maybeSingle();
  
      if (fetchError) throw fetchError;
  
      if (existingBookmark) {
        const { error: removeError } = await supabase
          .from('bookmarks')
          .delete()
          .eq('user_id', user.id)
          .eq('opportunity_id', opportunityId);
        if (removeError) throw removeError;
  
        setBookmarkedIds(prev => prev.filter(id => id !== opportunityId));
      } else {
        const { error: addError } = await supabase
          .from('bookmarks')
          .insert({
            user_id: user.id,
            opportunity_id: opportunityId,
          });
        if (addError) throw addError;
  
        setBookmarkedIds(prev => [...prev, opportunityId]);
      }
    } catch (err) {
      console.error('Error toggling bookmark:', err);
    }
  };
  
  /* ---------------------------------------------
      3) HELPER FUNCTIONS
  --------------------------------------------- */
  const getTypeColor = (typeString: string): string => {
    const colors: { [key: string]: string } = {
      Internship: 'bg-indigo-100 text-indigo-700',
      Fellowship: 'bg-indigo-100 text-indigo-700',
      Research: 'bg-green-100 text-green-700',
      Volunteer: 'bg-orange-100 text-orange-700',
      Job: 'bg-pink-100 text-pink-700',
      Competition: 'bg-red-100 text-red-700',
      'School Club': 'bg-yellow-100 text-yellow-700',
      'Summer Program': 'bg-teal-100 text-teal-700',
      'Nonprofit Organization': 'bg-purple-100 text-purple-700',
      'Educational Program': 'bg-cyan-100 text-cyan-700',
      'Research Program': 'bg-green-100 text-green-700',
      'Research Internship Program': 'bg-green-100 text-green-700',
      Apprenticeship: 'bg-orange-100 text-orange-700',
      Course: 'bg-gray-100 text-gray-700',
      STEM: 'bg-emerald-100 text-emerald-700',
    };
  
    const defaultColor = 'bg-indigo-50 text-indigo-700';
    const types = typeString.split(',').map((t) => t.trim());
    return colors[types[0]] || defaultColor;
  };
  
  // Competitiveness badge styling
  const getCompetitivenessColor = (level?: string): string => {
    if (!level || !level.includes('/')) {
      // fallback for missing or malformed data
      return 'bg-green-500 text-white';
    }
    const rating = parseInt(level.split('/')[0]);
    if (rating >= 4) return 'bg-red-500 text-white';
    if (rating >= 3) return 'bg-yellow-500 text-white';
    return 'bg-green-500 text-white';
  };
  
  // Distinguish email vs phone
  function isEmail(contact: string) {
    return contact.includes('@');
  }
  
  function isPhone(contact: string) {
    return /^\+?[0-9\s\-\(\)]+$/.test(contact.trim());
  }
  
  function getContactHref(contact: string) {
    if (isEmail(contact)) return `mailto:${contact.trim()}`;
    if (isPhone(contact)) return `tel:${contact.trim()}`;
    // fallback
    return `mailto:${contact.trim()}`;
  }
  
  function getContactLabel(contact: string) {
    if (isEmail(contact)) return 'Email Program';
    if (isPhone(contact)) return 'Call Program';
    return 'Contact Program';
  }
  
  // Single location-badge block for the main card
  const LocationBadges = ({ locations }: { locations: string[] }) => {
    const maxVisible = showAllLocations ? locations.length : 1;
    return (
      <div className="flex gap-1 flex-wrap">
        {locations.slice(0, maxVisible).map((loc, i) => (
          <Badge
            key={i}
            className="bg-indigo-50 text-indigo-700 border-none px-2 py-0.5 rounded-full text-xs font-light flex items-center gap-1"
          >
            <MapPinIcon className="w-3 h-3" />
            {loc}
          </Badge>
        ))}
        {locations.length > 1 && (
          <button
            className="text-xs text-indigo-500 hover:text-indigo-700 font-light ml-1"
            onClick={(e) => {
              e.stopPropagation();
              setShowAllLocations((prev) => !prev);
            }}
          >
            {showAllLocations ? 'Show less' : `+${locations.length - 1} more`}
          </button>
        )}
      </div>
    );
  };
  
  /* ---------------------------------------------
      4) MODAL COMPONENT
  --------------------------------------------- */
  const Modal = ({
    opp,
    onClose,
  }: {
    opp: Opportunity;
    onClose: () => void;
  }) => {
    if (!opp) return null;
    return createPortal(
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto shadow-xl"
        >
          {/* Header */}
          <div className="p-4 sm:p-6 flex justify-between items-start border-b border-indigo-50">
            <div className="flex items-center gap-3">
              <div className="hidden sm:flex w-10 h-10 bg-indigo-500 rounded-full items-center justify-center shadow-sm">
                <BuildingIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-light text-indigo-800 mb-2">{opp.title}</h2>
                <Badge
                  className={`px-2 py-1 rounded-full text-xs font-light ${getTypeColor(
                    opp.opportunityType
                  )}`}
                >
                  <TagIcon className="w-3 h-3 mr-1" />
                  {opp.opportunityType}
                </Badge>
              </div>
            </div>
            <button 
              onClick={onClose} 
              className="p-2 hover:bg-indigo-50 rounded-full transition-colors"
            >
              <XIcon className="w-5 h-5 text-indigo-400" />
            </button>
          </div>
          
          {/* Body */}
          <div className="px-4 sm:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6">
            {/* Two-column layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              {/* Left: Eligibility */}
              <div className="bg-indigo-50/50 p-3 sm:p-4 rounded-lg">
                <h4 className="text-indigo-600 font-light mb-3">Eligibility</h4>
                <div className="space-y-3 text-indigo-700 text-sm font-light">
                  <div className="flex items-center gap-2">
                    <UsersIcon className="w-4 h-4 text-indigo-400" />
                    <span>Ages: {opp.age}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <GraduationCapIcon className="w-4 h-4 text-indigo-400" />
                    <span>Grades: {opp.grades}</span>
                  </div>
                </div>
              </div>
              
              {/* Right: Program Details */}
              <div className="bg-indigo-50/50 p-3 sm:p-4 rounded-lg">
                <h4 className="text-indigo-600 font-light mb-3">Program Details</h4>
                <div className="space-y-3 text-indigo-700 text-sm font-light">
                  <div className="flex items-center gap-2">
                    <DollarSignIcon className="w-4 h-4 text-indigo-400" />
                    <span>Compensation: {opp.compensation}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TagIcon className="w-4 h-4 text-indigo-400" />
                    <span>Type: {opp.opportunityType}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <StarIcon className="w-4 h-4 text-indigo-400" />
                    <span>Competitiveness: {opp.competitiveness}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSignIcon className="w-4 h-4 text-indigo-400" />
                    <span>Program Fee: {opp.fee}</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* About */}
            <div className="bg-indigo-50/50 p-4 rounded-lg">
              <h4 className="text-indigo-600 font-light mb-3">About this Opportunity</h4>
              <p className="text-indigo-700 text-sm font-light leading-relaxed">{opp.description}</p>
            </div>
            
            {/* Locations */}
            <div className="bg-indigo-50/50 p-4 rounded-lg">
              <h4 className="text-indigo-600 font-light mb-3">Locations</h4>
              <div className="flex flex-wrap gap-2">
                {opp.location.map((loc, i) => (
                  <Badge
                    key={i}
                    className="flex items-center gap-1 bg-indigo-100 text-indigo-700 border-none px-2 py-1 rounded-full text-xs font-light"
                  >
                    <MapPinIcon className="w-3 h-3" />
                    {loc}
                  </Badge>
                ))}
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 pt-2">
              {opp.website && (
                <a
                  href={opp.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-5 py-2.5 bg-indigo-500 text-white rounded-full hover:bg-indigo-600 transition-colors text-sm font-light"
                >
                  Visit Website
                  <ExternalLinkIcon className="w-4 h-4 ml-2" />
                </a>
              )}
              
              {opp.contact && (
                <a
                  href={getContactHref(opp.contact)}
                  className="inline-flex items-center px-5 py-2.5 bg-indigo-50 text-indigo-600 rounded-full hover:bg-indigo-100 transition-colors text-sm font-light"
                >
                  {getContactLabel(opp.contact)}
                </a>
              )}
            </div>
          </div>
        </motion.div>
      </div>,
      document.body
    );
  };

  /* ---------------------------------------------
      5) RENDER A SINGLE CARD + MODAL
  --------------------------------------------- */
  // Check if currently bookmarked
  if (!opportunity) {
    return (
      <div className="p-4 text-center text-gray-500">
        No opportunity data available.
      </div>
    );
  }
  const isBookmarked = bookmarkedIds.includes(opportunity.id);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-indigo-50 mb-6"
        onClick={() => setSelectedOpportunity(opportunity)}
      >
        <div className="p-4 sm:p-6">
          {/* Top section: Title + Location + Bookmark */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-0 mb-4">
            <div className="flex items-start sm:items-center gap-3">
              <div className="hidden sm:flex w-10 h-10 bg-indigo-500 rounded-full items-center justify-center shadow-sm">
                <BuildingIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-light text-lg sm:text-xl text-indigo-800 mb-2">
                  {opportunity.title}
                </h3>
                <LocationBadges locations={opportunity.location} />
              </div>
            </div>
            
            {/* Right side: Competitiveness badge, bookmark */}
            <div className="flex flex-row sm:flex-col items-center sm:items-end justify-between sm:justify-start w-full sm:w-auto gap-2">
              {opportunity.sponsor && (
                <span className="inline-block bg-yellow-400 text-white text-xs font-bold px-2 py-1 rounded-full uppercase">
                  Sponsored
                </span>
              )}
              <Badge
                className={`px-2 py-0.5 sm:py-1 rounded-full text-xs font-light ${getCompetitivenessColor(
                  opportunity.competitiveness
                )}`}
              >
                {opportunity.competitiveness} Competitive
              </Badge>
              <button
                onClick={(e) => toggleBookmark(e, opportunity.id)}
                className="p-1.5 hover:bg-indigo-50 rounded-full transition-colors"
                aria-label="Bookmark this opportunity"
              >
                <Bookmark
                  className={`w-4 h-4 ${
                    isBookmarked ? 'fill-current text-indigo-500' : 'text-indigo-300'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Quick Info Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4">
            {[
              { icon: TagIcon, label: opportunity.opportunityType, color: 'text-indigo-500' },
              { icon: UsersIcon, label: opportunity.age, color: 'text-indigo-500' },
              { icon: CalendarIcon, label: `Due: ${opportunity.submissionDate}`, color: 'text-indigo-500' },
              { icon: DollarSignIcon, label: opportunity.compensation, color: 'text-indigo-500' },
            ].map((item, i) => (
              <div
                key={i}
                className="bg-indigo-50/50 p-2 rounded-lg flex items-center gap-2 hover:bg-indigo-100/50 transition-colors"
              >
                <item.icon className={`w-4 h-4 ${item.color}`} />
                <span className="text-xs font-light text-indigo-700 truncate">
                  {item.label}
                </span>
              </div>
            ))}
          </div>

          {/* Description */}
          <p className="text-indigo-600 text-sm leading-relaxed mb-4 line-clamp-2 font-light">
            {opportunity.description}
          </p>

          {/* Action Button */}
          <div className="flex justify-between items-center">
            <button
              className={`
                px-4 py-2 text-white rounded-full transition-all
                ${
                  opportunity.sponsor
                    ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600'
                    : 'bg-indigo-500 hover:bg-indigo-600'
                }
                text-sm font-light flex items-center gap-2
              `}
              onClick={(e) => {
                e.stopPropagation();
                setSelectedOpportunity(opportunity);
              }}
            >
              {opportunity.sponsor ? (
                <>
                  <SparklesIcon className="w-3.5 h-3.5" />
                  Explore Featured Opportunity
                </>
              ) : (
                <>
                  <SparklesIcon className="w-3.5 h-3.5" />
                  Learn More
                </>
              )}
            </button>

            {opportunity.sponsor && (
              <Badge className="bg-yellow-100 text-yellow-800 hidden md:flex items-center gap-1">
                <StarIcon className="w-3 h-3" />
                Premium Listing
              </Badge>
            )}
          </div>
        </div>
      </motion.div>

      {/* Modal */}
      {selectedOpportunity && (
        <Modal opp={selectedOpportunity} onClose={() => setSelectedOpportunity(null)} />
      )}
    </>
  );
};

export default OpportunityCard;