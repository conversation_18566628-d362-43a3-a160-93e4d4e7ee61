import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, XCircle, FileText, School, Hospital, Calendar, Mail, X } from 'lucide-react';

interface ConsultantApplication {
  id: string;
  fullName: string;
  email: string;
  isMedicalConsultant: boolean;
  isCollegeConsultant: boolean;
  collegesAcceptedTo: string;
  medSchoolsAcceptedTo: string;
  resumeURL: string;
  status: string;
  appliedAt: any;
  isPublished?: boolean;        // <-- new field
}

interface ConsultantApplicationCardProps {
  application: ConsultantApplication;
  onApprove: (id: string) => void;
  onDeny: (id: string) => void;
}

const ConsultantApplicationCard: React.FC<ConsultantApplicationCardProps> = ({ application, onApprove, onDeny }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const handleCardClick = () => {
    setIsExpanded(true);
  };

  const renderField = (icon: React.ReactNode, label: string, value: string, isLarge: boolean) => (
    <div className="flex items-center text-gray-600 mb-2">
      {icon}
      <span className="text-sm font-medium mr-2">{label}:</span>
      <span className={`text-sm ${!isLarge ? 'truncate' : ''} flex-grow`} title={value}>
        {value || 'N/A'}
      </span>
    </div>
  );

  const ActionButtons = () => (
    <div className="flex justify-end items-center gap-2 mt-2">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onApprove(application.id);
        }}
        className="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-full transition-colors duration-200 text-sm"
      >
        <CheckCircle className="w-4 h-4 mr-1" />
        Approve
      </button>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDeny(application.id);
        }}
        className="flex items-center justify-center bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-full transition-colors duration-200 text-sm"
      >
        <XCircle className="w-4 h-4 mr-1" />
        Deny
      </button>
    </div>
  );

  const CardContent = ({ isLarge = false }: { isLarge?: boolean }) => (
    <>
      <div className={`bg-gradient-to-r from-blue-500 to-purple-500 p-4 text-white ${isLarge ? 'rounded-t-lg' : 'rounded-t'}`}>
        <div className="flex items-center space-x-4">
          <div className={`rounded-full bg-white text-blue-500 flex items-center justify-center font-bold text-lg flex-shrink-0 ${isLarge ? 'w-16 h-16 text-2xl' : 'w-12 h-12'}`}>
            {getInitials(application.fullName)}
          </div>
          <div className="flex-grow min-w-0">
            <h2 className={`font-bold ${isLarge ? 'text-2xl' : 'text-xl'} truncate`} title={application.fullName}>{application.fullName}</h2>
            <div className="flex flex-wrap gap-2 mt-1">
              {application.isMedicalConsultant && (
                <span className="bg-white/20 text-white text-xs font-medium px-2 py-1 rounded-full">Medical</span>
              )}
              {application.isCollegeConsultant && (
                <span className="bg-white/20 text-white text-xs font-medium px-2 py-1 rounded-full">College</span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className={`p-4 space-y-3 ${isLarge ? 'text-base' : 'text-sm'}`}>
        {renderField(<Mail className={`${isLarge ? 'w-6 h-6' : 'w-5 h-5'} mr-2 text-blue-500 flex-shrink-0`} />, "Email", application.email, isLarge)}
        {renderField(<Calendar className={`${isLarge ? 'w-6 h-6' : 'w-5 h-5'} mr-2 text-blue-500 flex-shrink-0`} />, "Applied", application.appliedAt.toDate().toLocaleDateString(), isLarge)}
        {renderField(<School className={`${isLarge ? 'w-6 h-6' : 'w-5 h-5'} mr-2 text-blue-500 flex-shrink-0`} />, "Colleges", application.collegesAcceptedTo, isLarge)}
        <div className="flex items-center text-gray-600 mb-2">
          <Hospital className={`${isLarge ? 'w-6 h-6' : 'w-5 h-5'} mr-2 text-blue-500 flex-shrink-0`} />
          <span className="text-sm font-medium mr-2">Med Schools:</span>
          <span className={`text-sm ${!isLarge ? 'truncate' : ''} flex-grow`} title={application.medSchoolsAcceptedTo}>
            {application.medSchoolsAcceptedTo || 'N/A'}
          </span>
        </div>
        <div className="flex items-center text-gray-600">
          <FileText className={`${isLarge ? 'w-6 h-6' : 'w-5 h-5'} mr-2 text-blue-500 flex-shrink-0`} />
          <a
            href={application.resumeURL}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
            onClick={(e) => e.stopPropagation()}
          >
            View Resume
          </a>
        </div>
        <ActionButtons />
      </div>
    </>
  );

  return (
    <>
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-lg shadow-md overflow-hidden w-full max-w-md mx-auto cursor-pointer relative"
        onClick={handleCardClick}
      >
        <CardContent />
      </motion.div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setIsExpanded(false)}
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-lg overflow-hidden max-w-2xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                <CardContent isLarge />
                <button 
                  onClick={() => setIsExpanded(false)} 
                  className="absolute top-2 right-2 text-white hover:text-gray-200 transition-colors duration-200"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ConsultantApplicationCard;