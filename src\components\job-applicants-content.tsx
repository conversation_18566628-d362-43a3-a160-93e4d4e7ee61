"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Trash,
  Briefcase,
  Calendar,
  FileText,
  ChevronDown,
  ChevronUp,
  User,
  DollarSign,
  ClipboardList,
  MessageSquare,
  Clock,
  CheckCircle2,
  AlertCircle,
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"

interface JobPosting {
  id: string
  title: string
  description: string
  compensation: string
  postedAt: number
  customQuestions?: string[]
}

interface Applicant {
  id: string
  name: string
  resumeUrl: string
  appliedAt: number
  customAnswers?: string[]
}

interface JobApplicantsContentProps {
  jobPostingsLoading: boolean
  jobPostings: JobPosting[]
  jobApplicantsMap: Record<string, Applicant[]>
  handleDeletePosting: (postingId: string) => Promise<void>
}

export default function JobApplicantsContent({
  jobPostingsLoading,
  jobPostings,
  jobApplicantsMap,
  handleDeletePosting,
}: JobApplicantsContentProps) {
  const [expandedPostings, setExpandedPostings] = useState<Record<string, boolean>>({})
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null)

  const toggleExpand = (postingId: string) => {
    setExpandedPostings((prev) => ({
      ...prev,
      [postingId]: !prev[postingId],
    }))
  }

  const handleDelete = async (postingId: string) => {
    if (confirmDelete === postingId) {
      await handleDeletePosting(postingId)
      setConfirmDelete(null)
    } else {
      setConfirmDelete(postingId)
      // Auto-reset after 3 seconds
      setTimeout(() => setConfirmDelete(null), 3000)
    }
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    })
  }

  const formatTimeAgo = (timestamp: number) => {
    const now = new Date().getTime()
    const diff = now - timestamp

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor(diff / (1000 * 60 * 60))

    if (days > 0) {
      return `${days} ${days === 1 ? "day" : "days"} ago`
    } else if (hours > 0) {
      return `${hours} ${hours === 1 ? "hour" : "hours"} ago`
    } else {
      return "Just now"
    }
  }

  if (jobPostingsLoading) {
    return (
      <div className="flex justify-center items-center py-16">
        <div className="relative w-20 h-20">
          <div className="absolute top-0 left-0 w-full h-full border-4 border-gray-100 rounded-full"></div>
          <div className="absolute top-0 left-0 w-full h-full border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div>
          <p className="absolute inset-0 flex items-center justify-center text-xs font-light">Loading</p>
        </div>
      </div>
    )
  }

  if (jobPostings.length === 0) {
    return (
      <Card className="overflow-hidden border-none shadow-lg">
        <CardContent className="p-0">
          <div className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-12 flex flex-col items-center justify-center text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6">
              <Briefcase className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-3">No Job Postings Yet</h3>
            <p className="text-muted-foreground max-w-md mb-6">
              You haven&apos;t posted any jobs yet. Create job postings to start receiving applications.
            </p>
            <Link href="/job-posting" passHref>
              <Button variant="outline" className="gap-2">
                <Briefcase className="w-4 h-4" />
                Create Your First Job Posting
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Job Postings</h2>
          <p className="text-muted-foreground">Manage your job postings and applicants</p>
        </div>
        <Badge variant="outline" className="px-3 py-1 bg-primary/5 border-primary/20">
          <Briefcase className="w-3.5 h-3.5 mr-1.5" />
          {jobPostings.length} Active {jobPostings.length === 1 ? "Posting" : "Postings"}
        </Badge>
      </div>

      {jobPostings.map((posting) => {
        const applicants = jobApplicantsMap[posting.id] || []
        const isExpanded = expandedPostings[posting.id]
        const isConfirmingDelete = confirmDelete === posting.id

        return (
          <motion.div
            key={posting.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="overflow-hidden border border-slate-200 dark:border-slate-800 shadow-md hover:shadow-lg transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-6">
                <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
                  <div className="space-y-3 flex-1">
                    <div className="flex flex-wrap items-center gap-2">
                      <CardTitle className="text-xl font-bold">{posting.title}</CardTitle>
                      <Badge className="bg-primary/90 hover:bg-primary text-white">
                        {applicants.length} {applicants.length === 1 ? "Applicant" : "Applicants"}
                      </Badge>
                    </div>

                    <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1.5 text-slate-400" />
                        <span>Posted {formatDate(posting.postedAt)}</span>
                      </div>

                      <div className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1.5 text-emerald-500" />
                        <span className="font-light text-emerald-600 dark:text-emerald-400">
                          {posting.compensation}
                        </span>
                      </div>

                      {posting.customQuestions && posting.customQuestions.length > 0 && (
                        <div className="flex items-center">
                          <ClipboardList className="w-4 h-4 mr-1.5 text-slate-400" />
                          <span>
                            {posting.customQuestions.length} Custom{" "}
                            {posting.customQuestions.length === 1 ? "Question" : "Questions"}
                          </span>
                        </div>
                      )}
                    </div>

                    <p className="text-sm md:text-base">{posting.description}</p>
                  </div>

                  <div className="flex items-center gap-2 md:self-start">
                    <Button
                      onClick={() => toggleExpand(posting.id)}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1.5 border-slate-300 dark:border-slate-700"
                    >
                      {isExpanded ? (
                        <>
                          <ChevronUp className="w-4 h-4" />
                          <span>Hide Applicants</span>
                        </>
                      ) : (
                        <>
                          <ChevronDown className="w-4 h-4" />
                          <span>View Applicants</span>
                        </>
                      )}
                    </Button>

                    <Button
                      onClick={() => handleDelete(posting.id)}
                      variant={isConfirmingDelete ? "destructive" : "outline"}
                      size="sm"
                      className={`flex items-center gap-1.5 ${!isConfirmingDelete ? "border-red-200 text-red-600 hover:bg-red-50 dark:border-red-900 dark:text-red-400 dark:hover:bg-red-950" : ""}`}
                    >
                      <Trash className="w-4 h-4" />
                      <span>{isConfirmingDelete ? "Confirm" : "Delete"}</span>
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <CardContent className="p-6 pt-4">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold text-lg">Applicants</h3>
                          <Badge variant="outline" className="bg-slate-100 dark:bg-slate-800">
                            {applicants.length}
                          </Badge>
                        </div>

                        {applicants.length > 0 && (
                          <div className="text-sm text-muted-foreground">
                            <span className="font-light">Last application:</span>{" "}
                            {formatTimeAgo(Math.max(...applicants.map((a) => a.appliedAt)))}
                          </div>
                        )}
                      </div>

                      <Separator className="mb-6" />

                      {applicants.length > 0 ? (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                          {applicants.map((applicant) => (
                            <Card
                              key={applicant.id}
                              className="overflow-hidden border border-slate-200 dark:border-slate-800 hover:border-primary/30 transition-colors duration-200"
                            >
                              <CardHeader className="p-4 bg-slate-50 dark:bg-slate-900 flex flex-row items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                                    <User className="w-5 h-5 text-primary" />
                                  </div>
                                  <div>
                                    <h4 className="font-light">{applicant.name}</h4>
                                    <div className="flex items-center text-xs text-muted-foreground">
                                      <Clock className="w-3 h-3 mr-1" />
                                      Applied {formatTimeAgo(applicant.appliedAt)}
                                    </div>
                                  </div>
                                </div>
                                <a
                                  href={applicant.resumeUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center gap-1.5 text-primary hover:text-primary/80 text-sm font-light transition-colors bg-primary/5 hover:bg-primary/10 px-3 py-1.5 rounded-full"
                                >
                                  <FileText className="w-3.5 h-3.5" />
                                  View Resume
                                </a>
                              </CardHeader>

                              {posting.customQuestions &&
                                posting.customQuestions.length > 0 &&
                                applicant.customAnswers &&
                                applicant.customAnswers.length > 0 && (
                                  <CardContent className="p-4">
                                    <div className="flex items-center gap-1.5 mb-3 pb-2 border-b">
                                      <MessageSquare className="w-4 h-4 text-slate-400" />
                                      <h5 className="text-sm font-medium">Application Responses</h5>
                                    </div>
                                    <div className="space-y-3">
                                      {posting.customQuestions.map((question, index) => {
                                        const answer = (applicant.customAnswers ?? [])[index]
                                        const hasAnswer = answer && answer.trim() !== ""

                                        return (
                                          <div
                                            key={index}
                                            className="rounded-md bg-slate-50 dark:bg-slate-900 p-3 border border-slate-200 dark:border-slate-800"
                                          >
                                            <div className="flex items-start gap-2">
                                              <div className="mt-0.5">
                                                {hasAnswer ? (
                                                  <CheckCircle2 className="w-4 h-4 text-emerald-500" />
                                                ) : (
                                                  <AlertCircle className="w-4 h-4 text-amber-500" />
                                                )}
                                              </div>
                                              <div className="flex-1">
                                                <p className="text-xs font-light text-slate-500 dark:text-slate-400 mb-1.5">
                                                  {question}
                                                </p>
                                                <p className="text-sm">
                                                  {hasAnswer ? (
                                                    answer
                                                  ) : (
                                                    <span className="text-amber-500 dark:text-amber-400 italic">
                                                      No answer provided
                                                    </span>
                                                  )}
                                                </p>
                                              </div>
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  </CardContent>
                                )}
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center py-10 text-center bg-slate-50 dark:bg-slate-900 rounded-lg border border-dashed border-slate-200 dark:border-slate-800">
                          <div className="w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-full flex items-center justify-center mb-4">
                            <User className="w-8 h-8 text-slate-400" />
                          </div>
                          <h4 className="text-lg font-light mb-2">No applicants yet</h4>
                          <p className="text-sm text-muted-foreground max-w-md mb-6">
                            When candidates apply for this position, they&apos;ll appear here. Consider sharing this job
                            posting to attract more applicants.
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </motion.div>
                )}
              </AnimatePresence>
            </Card>
          </motion.div>
        )
      })}
    </div>
  )
}

