import React, { useState } from "react";
import { X } from "lucide-react";
import { supabase } from "../../supabase/supabaseClient";
import { useAuth } from "@/context/AuthContext";

interface ReportModalProps {
  openingId: string;
  clinicName: string;
  isOpen: boolean;
  onClose: () => void;
}

const ReportModal: React.FC<ReportModalProps> = ({ openingId, clinicName, isOpen, onClose }) => {
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [reportType, setReportType] = useState<string>("");
  const [feedback, setFeedback] = useState("");
  const { user } = useAuth();

  const reportTypes = [
    "Inappropriate Content",
    "Spam",
    "Misleading Information",
    "Scam",
    "Discriminatory Content",
    "Other"
  ];

  const handleClose = () => {
    onClose();
    setReportType("");
    setFeedback("");
    setHasSubmitted(false);
  };

  const handleSubmit = async () => {
    if (!reportType) {
      alert("Please select a report type");
      return;
    }

    try {
      const { error } = await supabase.from("reports").insert([
        {
          opening_id: openingId,
          clinic_name: clinicName,
          report_type: reportType,
          details: feedback,
          reported_by: user?.id || "anonymous",
          reported_at: new Date(),
          status: "pending",
          resolved_at: null,
          resolved_by: null,
          resolution: null,
        },
      ]);

      if (error) {
        throw error;
      }

      setHasSubmitted(true);

      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch (error: any) {
      console.error("Error submitting report:", error);
      alert("There was an error submitting your report. Please try again.");
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleClose}
    >
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {hasSubmitted ? "Report Submitted" : "Report Opening"}
          </h2>
          <button onClick={handleClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        {!hasSubmitted ? (
          <>
            <div className="mb-4">
              <p className="text-gray-600 mb-2">What type of issue are you reporting?</p>
              <div className="grid grid-cols-2 gap-2">
                {reportTypes.map((type) => (
                  <button
                    key={type}
                    onClick={() => setReportType(type)}
                    className={`p-2 rounded-md ${
                      reportType === type
                        ? "bg-red-500 text-white"
                        : "bg-gray-100 hover:bg-gray-200 text-gray-700"
                    } transition-colors text-sm`}
                  >
                    {type}
                  </button>
                ))}
              </div>
            </div>

            <div className="mb-4">
              <p className="text-gray-600 mb-2">Additional Details</p>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md h-24 resize-none focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Please provide more information about the issue..."
              />
            </div>

            <button
              onClick={handleSubmit}
              disabled={!reportType}
              className={`w-full py-2 rounded-md ${
                reportType
                  ? "bg-red-500 hover:bg-red-600 text-white"
                  : "bg-gray-200 text-gray-500 cursor-not-allowed"
              } transition-colors`}
            >
              Submit Report
            </button>
          </>
        ) : (
          <div className="text-center">
            <div className="mb-4 text-green-500">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="text-gray-600">
              Thank you for your report. We will review it and take appropriate action.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportModal;
