"use client";
import React, { useState, ChangeEvent, FormEvent } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import Navbar from "@/components/Navbar";
import AdNavbar from "@/components/NavbarAd";

import Footer from "@/components/Footer";
import { getFirestore, collection, addDoc } from "firebase/firestore";
import { useToast } from "@/hooks/use-toast"
import { getApp } from "firebase/app";

const app = getApp();
const db = getFirestore(app);

interface FormData {
  name: string;
  email: string;
  year: string;
  status: string;
}

export default function MedSchoolConsulting() {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    year: "",
    status: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const submissionData = {
        ...formData,
        timestamp: new Date().toISOString(),
      };

      await addDoc(collection(db, "medical_school_consulting_waitlist"), submissionData);

      toast({
        title: "Successfully joined waitlist!",
        description: "We'll contact you soon with more information.",
      });

      setFormData({
        name: "",
        email: "",
        year: "",
        status: ""
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Error",
        description: "There was an error submitting your information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-300 to-indigo-100">
      <Navbar />
      {/* <div className="relative bottom-0 left-0 right-0 h-12 sm:h-24 bg-gradient-to-t from-transparent to-blue-300"></div> */}
      <main className="flex-grow py-4 sm:py-8 mt-32">
        <div className="container mx-auto px-4 sm:px-32">
          {/* Page Header */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-8 w-full max-w-7xl mx-auto">
          <h1 
            className="text-3xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-b from-blue-500 to-blue-400 
            mb-4 sm:mb-0 h-auto sm:h-24leading-tight sm:leading-none text-center sm:text-left">
            Medical School Consulting
          </h1>

            {/* <Link href="/" className="w-full sm:w-auto">
              <Button
                variant="outline"
                className="flex items-center justify-center space-x-2 text-blue-500 border-blue-300 hover:bg-blue-50 transition-all duration-300 w-full sm:max-w-xs"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Home</span>
              </Button>
            </Link> */}
          </div>

          {/* Main Content */}
          <div className="grid md:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {/* Description Section */}
            <Card className="p-6 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <h2 className="text-2xl font-bold text-blue-600">Your Path to Medical School</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700">
                    Our specialized medical school consulting service is designed to maximize your chances of admission to top medical schools. We provide comprehensive guidance throughout your pre-medical journey.
                  </p>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold text-blue-500">Application Support</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>AMCAS/AACOMAS application strategy</li>
                        <li>Personal statement development</li>
                        <li>Secondary essay review</li>
                        <li>Activity descriptions optimization</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-blue-500">Interview Preparation</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>MMI practice sessions</li>
                        <li>Traditional interview prep</li>
                        <li>Mock interviews with feedback</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-blue-500">Pre-Med Planning</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>MCAT study planning</li>
                        <li>Clinical experience guidance</li>
                        <li>Research opportunity planning</li>
                        <li>Extracurricular activity strategy</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Form */}
            <Card className="p-6 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <h2 className="text-2xl font-bold text-blue-600">Join the Medical School Consulting Waitlist</h2>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="Enter your full name"
                      className="w-full"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      className="w-full"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
                      Application Year
                    </label>
                    <select
                      id="year"
                      className="w-full p-2 border rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                      value={formData.year}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Select application year</option>
                      <option value="2024">2024</option>
                      <option value="2025">2025</option>
                      <option value="2026">2026</option>
                      <option value="later">2027 or later</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                      Current Status
                    </label>
                    <select
                      id="status"
                      className="w-full p-2 border rounded-md border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                      value={formData.status}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Select your current status</option>
                      <option value="pre-med">Current Pre-Med Student</option>
                      <option value="gap">Gap Year</option>
                      <option value="reapplicant">Reapplicant</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white transition-colors duration-300"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Join Waitlist"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <div className="relative bottom-0 left-0 right-0 h-12 sm:h-24 bg-gradient-to-b from-transparent to-indigo-100"></div>
      <Footer className="bg-indigo-100"/>
    </div>
  );
}