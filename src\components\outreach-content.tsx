"use client"

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react"
import { supabase } from "../../supabase/supabaseClient"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

// UI components
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area"
import { <PERSON><PERSON><PERSON>, Too<PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Icons
import {
  Search,
  GraduationCap,
  Building,
  Rocket,
  Mail,
  Filter,
  Sparkles,
  X,
  Plus,
  Send,
  Paperclip,
  File,
  Loader2,
  MapPin,
  AlertCircle,
  Lock,
  Bold,
  Italic,
  Underline,
  Trash2,
  RefreshCw,
} from "lucide-react"
import { motion } from "framer-motion"
import ResearchCard from "./ResearchCard"
import StartupCard, { type StartupContact } from "./StartupCard"
import ClinicCard from "./ClinicCard"

// Import static contacts & interest options (used for research)
import { interestOptions } from "../data/staticContacts"
// Import researchers data from JSON
// import researchersData from "../../public/professors_super_context.json"

import { useAuth } from "@/context/AuthContext"
import { TokenDisplay } from "./TokenDisplay"
import { professorKeywordMapping } from "../data/professorKeyWordMapping";
import OutreachOnboardingPopup from "./OutreachOnboardingPopup"

// Wrap single researcher object in an array if needed
// const researchers = Array.isArray(researchersData) ? researchersData : [researchersData]

interface EmailDraft {
  recipient: string
  subject: string
  message: string
  attachments?: File[]
}
interface OutreachContentProps {
  googleAccessToken?: string | null
}

interface ProfessorContact {
  openalex_id: string  // This serves as the primary key
  topics: string[]
  institution: string
  papers: any[]
  name: string
  email: string
  embedding?: number[]
}

export default function OutreachContent({ googleAccessToken }: OutreachContentProps) {
  const { toast } = useToast()
  // Router is currently unused but may be needed for future navigation
  const router = useRouter()

  // Authentication state
  const [googleAccessTokenState, setGoogleAccessTokenState] = useState<string>("")
  const [isAuthenticating, setIsAuthenticating] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [professors, setProfessors] = useState<ProfessorContact[]>([])

  // Filters
  const [workType, setWorkType] = useState<"research" | "startup" | "clinic">("research")
  const [interests, setInterests] = useState<string[]>([])
  const [customInterest, setCustomInterest] = useState("")
  const [searchQuery, setSearchQuery] = useState("")

  // NEW: Clinic-specific state
  const [clinicZip, setClinicZip] = useState("")
  // geo & banner state
const [userCoords, setUserCoords]     = useState<{ lat: number; lng: number } | null>(null);
const [locationSource, setLocationSource] = useState<"default" | "ip">("default");

  const [clinicContacts, setClinicContacts] = useState<any[]>([])

  // Email dialog state
  const [selectedContact, setSelectedContact] = useState<any>(null)
  const [emailDraft, setEmailDraft] = useState<EmailDraft>({
    recipient: "",
    subject: "",
    message: "",
  })
  const [emailDialogOpen, setEmailDialogOpen] = useState(false)

  // User profile tokens
  const [profileData, setProfileData] = useState<{ tokens: number } | null>(null)


  // Unused state but kept for potential future use
  const [allProfileData, setAllProfileData] = useState<any[]>([])

  // Track unlocked contacts (by id) – for both startups and research/clinic
  const [unlockedContacts, setUnlockedContacts] = useState<string[]>([])

  // Track free startup selection per startup (by startup id)
  const [freeStartupSelections, setFreeStartupSelections] = useState<{ [key: string]: string | null }>({})
  const [hasUsedFreeSelection, setHasUsedFreeSelection] = useState<{ [key: string]: boolean }>({})
  // State for startup contacts fetched from DB
  const [startupContacts, setStartupContacts] = useState<StartupContact[]>([])

  // New state to hold fetched unlocked clinic data
  const [unlockedClinicData, setUnlockedClinicData] = useState<any[]>([])

  const [activeTab, setActiveTab] = useState<"browse" | "unlocked">("browse")

  // Infinite scroll state
  const [visibleEntries, setVisibleEntries] = useState(30)
  const loaderRef = useRef(null)

  const [showRemoveListingPopup, setShowRemoveListingPopup] = useState(false)

  const [isMatchScoreLoading, setIsMatchScoreLoading] = useState(false)
  const [isClinicLoading, setIsClinicLoading] = useState(false)
  const [researchScores, setResearchScores] = useState<Record<string, number>>({});
  const hasFetchedResearchScores = useRef(false);
  const [showOnboarding, setShowOnboarding] = useState<boolean>(false);

  const [scheduledAt,   setScheduledAt] = useState<string>(""); // ISO string from <input type="datetime-local">
  const [isScheduling,  setIsScheduling] = useState(false);

  // 1) pagination state
const [page, setPage] = useState(1)
const itemsPerPage = 30 // Changed from 50 to 30 as requested

  type StartupWithScore = StartupContact & { matchScore?: number };
  const { fetchGmailToken } = useAuth();


  const messageRef = useRef<HTMLDivElement>(null)

  // Suggestion system
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [savedSelection, setSavedSelection] = useState<Range | null>(null);

  const SuggestionBubble = ({ text, onClick }: { text: string; onClick: () => void }) => {
    const [isHovered, setIsHovered] = useState(false);

    // Custom click handler to prevent hover state change during click
    const handleClick = (e: React.MouseEvent) => {
      e.preventDefault();
      // Keep hover state as is during click
      onClick();
    };

    return (
      <div className="mb-2">
        <motion.button
          onClick={handleClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className="rounded-full w-full bg-blue-50 border border-blue-200 px-3 py-1.5 text-xs text-blue-800 hover:bg-blue-100 flex items-center gap-1.5 relative"
          animate={{
            borderRadius: isHovered ? 8 : 20,
            backgroundColor: "rgb(239 246 255)",
            boxShadow: isHovered ? "0 1px 2px rgba(0, 0, 0, 0.05)" : "none"
          }}
          transition={{
            duration: 0.2,
            ease: "easeInOut"
          }}
        >
          <div className="flex-shrink-0 flex items-center justify-center w-4 h-4">
            <Plus className="h-4 w-4 text-blue-500" />
          </div>
          <motion.div
            className="overflow-hidden text-left flex-1"
            animate={{
              height: isHovered ? "auto" : "1.2em"
            }}
            transition={{
              duration: 0.25,
              ease: [0.4, 0, 0.2, 1]
            }}
          >
            <span className={isHovered ? "text-blue-900" : "text-blue-800 truncate block"}>
              {text}
            </span>
          </motion.div>
        </motion.button>
      </div>
    );
  };

  const generateSuggestions = useCallback(async () => {


    try {
      const response = await fetch("/api/suggestions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          selectedContact,
          workType,
        }),
      });

      if (!response.ok) {
        console.error("Failed to fetch suggestions:", response.statusText);
        return [];
      }

      // Parse the JSON response
      const data = await response.json();
      
      // Extract the suggestions array from the response
      return data.suggestions || [];
    } catch (error) {
      console.error("Error generating suggestions:", error);
      return [];
    }
  }, [selectedContact, workType]);

  // Save current text selection
  const saveSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      setSavedSelection(range.cloneRange());
    }
  };

  // Insert suggestion at cursor position
  const insertSuggestion = (text: string) => {
    if (!messageRef.current) return;

    // Focus the editor
    messageRef.current.focus();

    // If we have a saved selection, restore it
    if (savedSelection) {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(savedSelection);
      }
    }

    // Insert the text at cursor position
    document.execCommand('insertText', false, text + ' ');

    // Save the new cursor position
    saveSelection();

    // Update the email draft with the new content
    if (messageRef.current) {
      setEmailDraft((prev) => ({
        ...prev,
        message: messageRef.current!.innerHTML,
      }));
    }

    // Update formatting state
    updateFormattingState();
  };

  useEffect(() => {
    if (!selectedContact) {
      setSuggestions([]);
      return;
    }

    // Handle the Promise returned by the async function
    generateSuggestions().then(suggestionResults => {
      setSuggestions(suggestionResults);
    });
  }, [selectedContact, generateSuggestions]);

  // Helper to check if a given topic matches a selected interest.
  // It checks direct equality and whether the mapping applies in either direction.
  const topicMatchesInterest = (topic: string, interest: string): boolean => {
    const lowerTopic = topic.toLowerCase();
    const lowerInterest = interest.toLowerCase();
    if (lowerTopic === lowerInterest) return true;
    if (professorKeywordMapping[lowerInterest]?.includes(lowerTopic)) return true;
    if (professorKeywordMapping[lowerTopic]?.includes(lowerInterest)) return true;
    return false;
  };

  const startupTags = ["EdTech", "SaaS", "Software", "B2C", "Social Networking", "Legal", "YC",
    "DeepTech", "AgriTech", "FinTech", "Infrastucture", "Engineering", "Space", "Sales",
    "Content", "Analytics", "Recruiting", "Real Estate", "Aviation", "Government", "Logistics",
    "Marketing"]; // example tags


  useEffect(() => {
    if (messageRef.current && emailDraft.message !== messageRef.current.innerHTML) {
      messageRef.current.innerHTML = emailDraft.message;
      // Save initial selection at the end of content
      setTimeout(() => {
        if (messageRef.current) {
          messageRef.current.focus();
          const selection = window.getSelection();
          if (selection) {
            const range = document.createRange();
            range.selectNodeContents(messageRef.current);
            range.collapse(false); // Collapse to end
            selection.removeAllRanges();
            selection.addRange(range);
            saveSelection();
          }
        }
      }, 0);
    }
  }, [emailDraft.message]);

  const handleMessageInput = () => {
    if (messageRef.current) {
      setEmailDraft((prev) => ({
        ...prev,
        message: messageRef.current!.innerHTML,
      }))
    }
  }

  // ---------------------------------------------
// IP geolocation → runs once per session
// ---------------------------------------------
useEffect(() => {
  if (workType !== "clinic" || clinicZip || locationSource !== "default") return;

  (async () => {
    try {
      // 45 ms avg / no key / HTTPS
      const ipInfo = await fetch("https://ipapi.co/json/").then(r => r.json());  // :contentReference[oaicite:0]{index=0}
      if (ipInfo?.latitude && ipInfo?.longitude) {
        setUserCoords({ lat: ipInfo.latitude, lng: ipInfo.longitude });
        setLocationSource("ip");
      }
    } catch (err) {
      console.warn("IP geolocation failed:", err);
    }
  })();
}, [workType, clinicZip, locationSource]);

useEffect(() => {
  if (!userCoords || clinicZip || workType !== "clinic") return;

  (async () => {
    setIsClinicLoading(true);
    try {
      const { lat, lng } = userCoords;
      const res  = await fetch(`/api/clinics?lat=${lat}&lng=${lng}&radius=25`);
      const data = await res.json();
      setClinicContacts(Array.isArray(data) ? data : []);   // prevent crash
    } catch (err) {
      console.error("Clinic fetch by IP failed:", err);
    } finally {
      setIsClinicLoading(false);
    }
  })();
}, [userCoords, clinicZip, workType]);


  const [formatting, setFormatting] = useState({ bold: false, italic: false, underline: false })

  const handleFormat = (cmd: string) => {
    document.execCommand(cmd, false)
    document.getElementById("message")?.focus()
    updateFormattingState()
  }

  const updateFormattingState = () => {
    setFormatting({
      bold: document.queryCommandState("bold"),
      italic: document.queryCommandState("italic"),
      underline: document.queryCommandState("underline"),
    })
  }

  useEffect(() => {
    document.getElementById("message")?.addEventListener("keyup", updateFormattingState)
    document.getElementById("message")?.addEventListener("mouseup", updateFormattingState)

    return () => {
      document.getElementById("message")?.removeEventListener("keyup", updateFormattingState)
      document.getElementById("message")?.removeEventListener("mouseup", updateFormattingState)
    }
  }, [])

  useEffect(() => {
    // Show onboarding if it hasn't been completed yet
    if (!localStorage.getItem("outreachOnboardingComplete")) {
      setShowOnboarding(true);
    }
  }, []);
 

  // Inside your OutreachContent component...
  const [sortedStartupContacts, setSortedStartupContacts] = useState<any[]>([])

  const { user, loading } = useAuth()
 
 
  const startupScores = useMemo(() => {
            const scores: Record<string, number> = {};
            sortedStartupContacts.forEach((startup) => {
              scores[startup.id] = startup.matchScore;
            });
            return scores;
          }, [sortedStartupContacts]);

// 1️⃣ Fetch professors on mount
useEffect(() => {
  supabase
    .from<"professors_bio", ProfessorContact>("professors_bio")
    .select("*")
    .then(({ data, error }) => {
      if (error) return console.error("Error loading professors:", error)
      console.log("→ fetched professors:", data)
      setProfessors(data!)
    })
}, [])

// 2️⃣ Seed sortedResearchContacts whenever your professors state changes
useEffect(() => {
  if (!user || loading) return;
  setIsMatchScoreLoading(true);

  (async () => {
    try {
      // 1) pick the right "filtered" list up-front
      const filtered =
        workType === "startup"
          ? getFilteredStartupContacts()
          : workType === "research"
          ? getFilteredContacts()
          : [];

      // 2) if there's nothing to score, skip the RPC entirely
      
      if (filtered.length === 0) {
        if (workType === "research") setSortedResearchContacts([]);
        else if (workType === "startup") setSortedStartupContacts([]);
        setIsMatchScoreLoading(false);
        return;
      }

      // 3) ask your new route for exactly filtered.length hits
      const res = await fetch("/api/match", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ p_limit: filtered.length }),
      });
      if (!res.ok) throw new Error(`match returned ${res.status}`);

      const { startups: apiStartups, professors: apiProfessors } = await res.json();

      if (workType === "startup") {
        const merged = filtered.map(c => {
          const hit = apiStartups.find((s: any) => s.id === c.id);
          return { ...c, matchScore: hit?.match_score ?? 0 };
        });
        merged.sort((a, b) => b.matchScore - a.matchScore);
        setSortedStartupContacts(merged);
      } else if (workType === "research") {
        console.log("filtered professors:", filtered.length);
        console.log("raw rpc hits:", apiProfessors.length);

        const merged = filtered.map(c => {
          const hit = apiProfessors.find((p: any) => p.openalex_id === c.openalex_id);
          return { ...c, matchScore: hit?.match_score ?? 0 };
        });
        merged.sort((a, b) => b.matchScore - a.matchScore);
        setSortedResearchContacts(merged);
      }
    } catch (err) {
      console.error("Match fetch failed – using fallback:", err);
      if (workType === "startup") {
        setSortedStartupContacts(
          getFilteredStartupContacts().map(c => ({ ...c, matchScore: 0 }))
        );
      } else {
        setSortedResearchContacts(
          getFilteredContacts().map(c => ({ ...c, matchScore: 0 }))
        );
      }
    } finally {
      setIsMatchScoreLoading(false);
    }
  })();
}, [
  user,
  loading,
  workType,
  searchQuery,
  interests,
  startupContacts,
  professors,
]);
 
  // Assuming you have a similar array for research contacts:
  const [sortedResearchContacts, setSortedResearchContacts] = useState<
    ((typeof professors)[number] & { matchScore: number })[]
  >([])

  // Effect to fetch clinic details for unlocked clinic IDs
  useEffect(() => {
    // Filter unlockedContacts to only clinic IDs (adjust this check as needed).
    // For example, if clinic IDs are numeric:
    const clinicIds = unlockedContacts.filter((id) => !isNaN(Number(id)))
    if (clinicIds.length > 0) {
      fetch(`/api/clinics?ids=${clinicIds.join(",")}`)
        .then((res) => res.json())
        .then((data) => setUnlockedClinicData(data))
        .catch((error) => console.error("Error fetching unlocked clinics:", error))
    }
  }, [unlockedContacts])

  // Fetch unlocked listings on mount
  useEffect(() => {
    const fetchUnlockedListings = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession()
      if (!session?.user) return

      const { data, error } = await supabase
        .from("user_unlocks")
        .select("listing_id, selected_contact_email")
        .eq("user_id", session.user.id)

      if (error) {
        console.error("Error fetching unlocked listings:", error)
      } else {
        const unlockedIds = data.map((entry) => String(entry.listing_id))
        setUnlockedContacts(unlockedIds)
        
        // Create a map of startup IDs to their selected contact emails
        const startupSelections = data.reduce((acc, entry) => {
          if (entry.selected_contact_email) {
            acc[entry.listing_id] = entry.selected_contact_email
          }
          return acc
        }, {} as { [key: string]: string | null })
        
        setFreeStartupSelections(startupSelections)
        setHasUsedFreeSelection(
          data.reduce((acc, entry) => {
            acc[entry.listing_id] = !!entry.selected_contact_email
            return acc
          }, {} as { [key: string]: boolean })
        )
      }
    }

    fetchUnlockedListings()
  }, [])

  // Fetch Gmail token on mount
  useEffect(() => {
    async function fetchGmailToken() {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()
        if (!session?.user) return
        const { data: tokenRecord, error } = await supabase
          .from("user_oauth_tokens2")
          .select("*")
          .eq("user_id", session.user.id)
          .eq("provider", "google")
          .maybeSingle()
        if (error) {
          console.error("Error fetching Google token record:", error)
          return
        }
        if (tokenRecord?.access_token) {
          setGoogleAccessTokenState(tokenRecord.access_token)
        }
      } catch (error) {
        console.error("Error fetching Google token:", error)
      }
    }
    fetchGmailToken()
  }, [])

  // Fetch user profile tokens on mount
  useEffect(() => {
    async function fetchUserProfile() {
      try {
        const {
          data: { session },
        } = await supabase.auth.getSession()
        if (!session?.user) return
        const { data, error } = await supabase.from("profiles").select("tokens").eq("id", session.user.id).maybeSingle()
        if (error) {
          console.error("Error fetching profile tokens:", error)
        } else if (data) {
          setProfileData(data)
        }
      } catch (error) {
        console.error("Unexpected error fetching profile:", error)
      }
    }
    fetchUserProfile()
  }, [])

  // Fetch startup contacts when workType is "startup"
  useEffect(() => {
    async function fetchStartupContacts() {
      const { data, error, status } = await supabase.from("startup_contacts").select("*")
      console.log("Status:", status)
      if (error) {
        console.error("Error fetching startup contacts:", error)
      } else {
        const validData = data.filter((d: StartupContact) => d.startup)
        setStartupContacts(validData)
      }
    }
    fetchStartupContacts()
  }, [])

  // NEW: Fetch clinic contacts from API based on ZIP code
  const fetchClinicContacts = async () => {
    if (!clinicZip) return
    setIsClinicLoading(true)
    try {
      const response = await fetch(`/api/clinics?zipcode=${clinicZip}`)
      const clinics = await response.json()
      setClinicContacts(clinics)
    } catch (error) {
      console.error("Error fetching clinics:", error)
      toast({
        title: "Error",
        description: "Failed to fetch clinics. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsClinicLoading(false)
    }
  }

  // Infinite scroll observer
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setVisibleEntries((prev) => prev + 30)
        }
      },
      { threshold: 1.0 },
    )

    if (loaderRef.current) {
      observer.observe(loaderRef.current)
    }

    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current)
      }
    }
  }, [])

  // Filtering for research and clinic contacts
const getFilteredContacts = () => {
  if (workType === "research") {
    return professors
      .filter((contact) => {
        // 1) parse topics into a real array
        const topicsArr: string[] = Array.isArray(contact.topics)
          ? contact.topics
          : typeof contact.topics === "string"
          ? JSON.parse(contact.topics)
          : [];

        // 2) interest‐based filtering
        if (interests.length > 0) {
          const matchesInterest = interests.some((selectedInterest) =>
            topicsArr.some((topic) => topicMatchesInterest(topic, selectedInterest))
          );
          if (!matchesInterest) return false;
        }

        return true;
      })
      .filter((contact) => {
        // 3) text search
        if (!searchQuery) return true;
        const q = searchQuery.toLowerCase();
        const topicsArr: string[] = Array.isArray(contact.topics)
          ? contact.topics
          : typeof contact.topics === "string"
          ? JSON.parse(contact.topics)
          : [];

        return (
          contact.name.toLowerCase().includes(q) ||
          contact.institution?.toLowerCase().includes(q) ||
          topicsArr.some((topic) => topic.toLowerCase().includes(q))
        );
      })
      .filter((contact) => {
        // 4) remove unlocked
        return !unlockedContacts.includes(contact.openalex_id);
      });

  } else if (workType === "clinic") {
    let filtered = clinicContacts;

    if (searchQuery) {
      const q = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (c) =>
          c.name.toLowerCase().includes(q) ||
          c.address.toLowerCase().includes(q) ||
          c.city.toLowerCase().includes(q)
      );
    }

    // Remove unlocked, then sort by distance
    return filtered
      .filter((c) => !unlockedContacts.includes(String(c.id || c.email)))
      .sort((a, b) => (a.distance ?? 0) - (b.distance ?? 0));
  }

  return [];
};


  // Filter startup contacts using search and interests
  const getFilteredStartupContacts = () => {
    if (workType !== "startup") return []
    let filtered = startupContacts
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (contact) =>
          (contact.startup && contact.startup.toLowerCase().includes(query)) ||
          (contact.website && contact.website.toLowerCase().includes(query)) ||
          (contact.hq && contact.hq.toLowerCase().includes(query)) ||
          (contact.company_description && contact.company_description.toLowerCase().includes(query)) ||
          (contact.first_contact_first_name && contact.first_contact_first_name.toLowerCase().includes(query)) ||
          (contact.first_contact_last_name && contact.first_contact_last_name.toLowerCase().includes(query)) ||
          (contact.second_contact_first_name && contact.second_contact_first_name.toLowerCase().includes(query)) ||
          (contact.second_contact_last_name && contact.second_contact_last_name.toLowerCase().includes(query)) ||
          (contact.first_contact_role && contact.first_contact_role.toLowerCase().includes(query)) ||
          (contact.second_contact_role && contact.second_contact_role.toLowerCase().includes(query)) ||
          (contact.tags && contact.tags.toLowerCase().includes(query)),
      )
    }
    if (interests.length > 0) {
      filtered = filtered.filter((contact) => {
        if (contact.tags) {
          const contactTags = contact.tags
            .toLowerCase()
            .split(",")
            .map((tag) => tag.trim())
          return interests.some((interest) => contactTags.includes(interest.toLowerCase()))
        }
        return false
      })
    }
    // Remove unlocked startup listings.
    filtered = filtered.filter((contact) => !unlockedContacts.includes(String(contact.id)))
    return filtered
  }

  // Toggle interest chips
  const toggleInterest = (interestId: string) => {
    setInterests((prev) => (prev.includes(interestId) ? prev.filter((i) => i !== interestId) : [...prev, interestId]))
  }

  // Function to add custom interest - currently unused but kept for future implementation
  const addCustomInterest = () => {
    if (customInterest.trim() && !interests.includes(customInterest.trim().toLowerCase())) {
      setInterests((prev) => [...prev, customInterest.trim().toLowerCase()])
      setCustomInterest("")
    }
  }

  // Handle contact selection for email dialog
  const handleSelectContact = (contact: any) => {
    // Use the contact's type if available; otherwise fallback to the global workType.
    const contactType = contact.type || workType;

    // Set the contact and open email dialog
    setSelectedContact(contact);
    setEmailDraft({
      recipient: contact.email,
      subject: contactType === "startup" 
        ? "Inquiry about your startup"
        : contactType === "research"
          ? "Inquiry about your research"
          : "Inquiry about your clinic",
      message: "",
    });
    setEmailDialogOpen(true);
  };


  // Update token count in DB and local state
  const updateTokenCount = async (newTokenCount: number) => {
    try {
      const {
        data: { session },
      } = await supabase.auth.getSession()
      if (session?.user) {
        const { error } = await supabase
          .from("profiles")
          .update({ tokens: newTokenCount, updated_at: new Date().toISOString() })
          .eq("id", session.user.id)
        if (error) {
          console.error("Error updating token count:", error)
        } else {
          setProfileData((prev) => (prev ? { ...prev, tokens: newTokenCount } : prev))
        }
      }
    } catch (err) {
      console.error("Unexpected error updating token count:", err)
    }
  }

  // Handle unlocking a contact's details for startups, research, or clinics
  const handleUnlockContact = async (contact: any, listingType: "startup" | "research" | "clinic") => {
    const {
      data: { session },
    } = await supabase.auth.getSession()
    if (!session?.user) return

    // Use a different identifier for clinics vs research/startup
    const listingId =
      listingType === "startup"
        ? String(contact.id)
        : listingType === "research"
          ? contact.openalex_id
          : String(contact.id || contact.email)

    // Ensure we have profile data
    if (!profileData) return

    // Check if the contact is already unlocked
    if (unlockedContacts.includes(listingId)) return

    // For startups, check if this is the first contact (free) or subsequent contact (requires token)
    if (listingType === "startup") {
      const startupId = contact.id
      const isFirstContact = !hasUsedFreeSelection[startupId]
      
      if (!isFirstContact && profileData.tokens <= 0) {
        toast({
          title: "Insufficient Tokens",
          description: "You don't have enough tokens to unlock additional contacts from this startup.",
          variant: "destructive",
        })
        return
      }

      try {
        // Only deduct token if it's not the first contact
        if (!isFirstContact) {
          const newTokenCount = profileData.tokens - 1
          const { error: updateError } = await supabase
            .from("profiles")
            .update({ tokens: newTokenCount, updated_at: new Date().toISOString() })
            .eq("id", session.user.id)

          if (updateError) {
            throw new Error("Failed to update token count")
          }
          setProfileData((prev) => (prev ? { ...prev, tokens: newTokenCount } : prev))
        }

        // Insert into user_unlocks table with the selected contact email
        const { error: unlockError } = await supabase.from("user_unlocks").insert([
          {
            user_id: session.user.id,
            listing_type: listingType,
            listing_id: listingId,
            selected_contact_email: contact.first_contact_email, // Store which contact was selected
          },
        ])

        if (unlockError) {
          throw new Error(`Failed to unlock ${listingType} listing`)
        }

        // Update local state
        setUnlockedContacts((prev) => [...prev, listingId])
        if (isFirstContact) {
          setFreeStartupSelections((prev) => ({ ...prev, [startupId]: contact.first_contact_email }))
          setHasUsedFreeSelection((prev) => ({ ...prev, [startupId]: true }))
        }

        toast({
          title: "Success",
          description: isFirstContact 
            ? "First contact unlocked for free!" 
            : "Additional contact unlocked successfully!",
          variant: "default",
        })
      } catch (error) {
        console.error(`Error unlocking ${listingType} listing:`, error)
        toast({
          title: "Error",
          description: `Failed to unlock ${listingType} listing. Please try again.`,
          variant: "destructive",
        })
      }
    } else {
      // Handle research and clinic unlocks (existing logic)
      if (profileData.tokens <= 0) {
        toast({
          title: "Insufficient Tokens",
          description: "You don't have enough tokens to unlock this listing.",
          variant: "destructive",
        })
        return
      }

      try {
        // Deduct a token
        const newTokenCount = profileData.tokens - 1
        const { error: updateError } = await supabase
          .from("profiles")
          .update({ tokens: newTokenCount, updated_at: new Date().toISOString() })
          .eq("id", session.user.id)

        if (updateError) {
          throw new Error("Failed to update token count")
        }

        // Insert into user_unlocks table
        const { error: unlockError } = await supabase.from("user_unlocks").insert([
          {
            user_id: session.user.id,
            listing_type: listingType,
            listing_id: listingId,
          },
        ])

        if (unlockError) {
          throw new Error(`Failed to unlock ${listingType} listing`)
        }

        // Update local state
        setProfileData((prev) => (prev ? { ...prev, tokens: newTokenCount } : prev))
        setUnlockedContacts((prev) => [...prev, listingId])

        toast({
          title: "Success",
          description: `Unlocked ${listingType} listing successfully!`,
          variant: "default",
        })
      } catch (error) {
        console.error(`Error unlocking ${listingType} listing:`, error)
        toast({
          title: "Error",
          description: `Failed to unlock ${listingType} listing. Please try again.`,
          variant: "destructive",
        })
      }
    }
  }

  // Function to get all unlocked contacts
  const getAllUnlockedContacts = () => {
    // Filter research contacts using their unique openalex_id.
    const unlockedResearch = professors.filter((contact) =>
      unlockedContacts.includes(contact.openalex_id)
    );
 
    // Filter and map startup contacts by merging the lookup match score.
    const unlockedStartups = startupContacts
      .filter((contact) => unlockedContacts.includes(String(contact.id)))
      .map((contact) => ({
        ...contact,
        type: "startup",
        startupId: contact.id,
        matchScore: startupScores[contact.id] ?? 0, // Merge the match score from our lookup
      }));
 
    // Use unlocked clinics as-is.
    const unlockedClinics = unlockedClinicData;
 
    return [
      ...unlockedResearch.map((c) => ({ ...c, type: "research" })),
      ...unlockedStartups,
      ...unlockedClinics.map((c) => ({ ...c, type: "clinic" })),
    ];
  };
 

  // Initiate Google OAuth if no Gmail token
  const initiateGoogleAuth = () => {
    setIsAuthenticating(true)
    const randomState = Math.random().toString(36).substring(2)
    const currentPath = window.location.pathname
    sessionStorage.setItem("pending_email", JSON.stringify(emailDraft))
    sessionStorage.setItem("email_dialog_open", "true")

    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
    const redirectUri = `${window.location.origin}/api/auth/google/gmail/callback`

    if (!clientId) {
      console.error("Google Client ID is missing!")
      toast({
        title: "Configuration Error",
        description: "Google OAuth is not properly configured.",
        variant: "destructive",
      })
      setIsAuthenticating(false)
      return
    }

    const authUrl =
      "https://accounts.google.com/o/oauth2/v2/auth?" +
      `client_id=${encodeURIComponent(clientId)}` +
      `&redirect_uri=${encodeURIComponent(redirectUri)}` +
      `&response_type=code` +
      `&scope=${encodeURIComponent("https://www.googleapis.com/auth/gmail.send")}` +
      `&state=${encodeURIComponent(randomState + "|" + currentPath)}` +
      `&access_type=offline` +
      `&prompt=consent`

    window.location.href = authUrl
  }

  // Helper: Call refresh endpoint to get a valid Gmail token
  async function getValidGmailToken(): Promise<string> {
    const response = await fetch("/api/auth/google/refresh")
    if (response.status === 404) {
      console.error("No token record found. Initiating Google connection...")
      initiateGoogleAuth()
      throw new Error("No token record found")
    }
    const data = await response.json()
    if (data.error) {
      throw new Error(data.error)
    }
    return data.access_token
  }

const BUCKET = "email-attachments";
  // uploads every File and returns [{name, path, mime}]
const uploadAttachments = async (files: File[], userId: string) => {
  return Promise.all(
    files.map(async (file) => {
      const path = `${userId}/${crypto.randomUUID()}_${file.name}`;
      const { error } = await supabase
        .storage
        .from(BUCKET)          // bucket name
        .upload(path, file, { upsert: false });

      if (error) throw error;
      return { name: file.name, path, mimeType: file.type,};
})
  );
};


 // --- adjusted handleSendEmail ---------------------------------------------
const handleSendEmail = async () => {
  setIsSending(true);

  try {
    /* 1️⃣  Always grab a fresh Gmail access-token from AuthContext */
    const gmailToken = await fetchGmailToken();   // throws "reauth" if user must reconnect

    /* 2️⃣  Make sure the Supabase session itself is still valid */
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to send emails.",
        variant: "destructive",
      });
      return;
    }

/* 3️⃣  Build Gmail-ready attachments – DO NOT upload */
let attachmentsData: { filename: string; mimeType: string; content: string }[] = [];

if (emailDraft.attachments?.length) {
  attachmentsData = await Promise.all(
    emailDraft.attachments.map(async (file) => {
      const buf = await file.arrayBuffer();
      return {
        filename: file.name,
        mimeType: file.type || "application/octet-stream",
        content: Buffer.from(buf).toString("base64"),
      };
    })
  );
}

/* 4️⃣  Send immediately */
const res = await fetch("/api/outreachtool", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${session.access_token}`,
  },
  body: JSON.stringify({
    recipient:  emailDraft.recipient,
    subject:    emailDraft.subject,
    message:    emailDraft.message,
    googleAccessToken: gmailToken,
    attachments: attachmentsData,       // ← base-64 array as the API expects
  }),
});



    const result = await res.json();

    if (result.success) {
      setSelectedContact(null);
      setEmailDialogOpen(false);
      toast({ title: "Success!", description: "Your message has been sent.", variant: "default" });
    } else {
      throw new Error(result.error || "Server returned failure");
    }
  }
  /* --------------------------------------------------------------------- */
  catch (err: any) {
    if (err.message === "reauth") {
      toast({
        title: "Gmail connection expired",
        description: "Please reconnect your Google account.",
        variant: "destructive",
      });
      return;                            // user will complete OAuth and retry
    }

    console.error("Error sending email:", err);
    toast({
      title: "Email sending failed",
      description: "Failed to send email. Please try again.",
      variant: "destructive",
    });
  }
  /* --------------------------------------------------------------------- */
  finally {
    setIsSending(false);
  }
};


async function handleScheduleSend() {
  if (!scheduledAt) return;
 
  // Validate that the selected time is in the future
  const selectedDate = new Date(scheduledAt);
  const now = new Date();
 
  if (selectedDate <= now) {
    toast({
      title: "Invalid Schedule Time",
      description: "Please select a future date and time.",
      variant: "destructive",
    });
    return;
  }
 
  setIsScheduling(true);

  try {
    // 1️⃣  grab the current session
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error("no-session");
    

    // 2️⃣  call the route with the Bearer token
    // 2️⃣  call the route with the Bearer token
const res = await fetch("/api/schedule-email", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${session.access_token}`,
  },
  body: JSON.stringify({
    to:      [emailDraft.recipient],
    cc:      [],
    bcc:     [],
    subject: emailDraft.subject,
    body:    emailDraft.message,
    sendAt:  new Date(scheduledAt).toISOString(),
    attachments: emailDraft.attachments?.length
      ? await uploadAttachments(emailDraft.attachments, session.user.id)
      : undefined,
  }),
});


    if (!res.ok) throw new Error(await res.text());

    toast({ title: "Scheduled!", description: "We'll send it at your chosen time." });
    setEmailDialogOpen(false);
  } catch (err: any) {
    if (err.message === "no-session") {
      toast({ title: "Auth required", description: "Please log in again.", variant: "destructive" });
    } else {
      toast({ title: "Error", description: "Failed to schedule email.", variant: "destructive" });
      console.error(err);
    }
  } finally {
    setIsScheduling(false);
  }
}



  // Determine contacts to display based on workType
  let filteredContacts: any[] = []
  if (workType === "research") {
    filteredContacts = getFilteredContacts()
  } else if (workType === "clinic") {
    filteredContacts = getFilteredContacts()
  }

  const uniqueFilteredContacts = filteredContacts.filter((contact, index, self) =>
    index === self.findIndex((c) => c.openalex_id === contact.openalex_id)
  );

  const visibleUniqueContacts = uniqueFilteredContacts.slice(0, visibleEntries);

// For research
const researchScoresLookup: Record<string, number> = {};
Object.keys(researchScores).forEach((openalexId) => {
  researchScoresLookup[openalexId] = researchScores[openalexId];
});

const uniqueResearchContacts = getAllUnlockedContacts()
  .filter(contact => contact.type === "research")
  .filter((contact, index, self) =>
    index === self.findIndex(c => c.openalex_id === contact.openalex_id)
  );

  const uniqueStartupContacts = getAllUnlockedContacts()
  .filter(contact => contact.type === "startup")
  .filter((contact, index, self) =>
    index === self.findIndex(c => c.id === contact.id)
  );



// For startups, use filteredStartupContacts
const filteredStartupContacts = workType === "startup" ? getFilteredStartupContacts() : []


const startupResults: StartupWithScore[] =
sortedStartupContacts.length
  ? sortedStartupContacts               // already has matchScore
  : filteredStartupContacts.map((c) =>  // add an undefined score
      ({ ...c, matchScore: undefined }));

  useEffect(() => {
    setPage(1)
  }, [workType, searchQuery, interests, sortedResearchContacts])

// 2) pick the right "master" list
const allResults =
  workType === 'startup'
    ? sortedStartupContacts
    : workType === 'research'
      ? sortedResearchContacts
      : filteredContacts

// 3) compute total pages & slice out current page's chunk
const totalResearchPages = Math.ceil(sortedResearchContacts.length / itemsPerPage)
const researchPageResults = sortedResearchContacts.slice(
  (page - 1) * itemsPerPage,
  page * itemsPerPage
)

const totalStartupPages = Math.ceil(startupResults.length / itemsPerPage)
const startupPageResults = startupResults.slice(
  (page - 1) * itemsPerPage,
  page * itemsPerPage
)

const totalClinicPages = Math.ceil(filteredContacts.length / itemsPerPage)
const clinicPageResults = filteredContacts.slice(
  (page - 1) * itemsPerPage,
  page * itemsPerPage
)

  return (
    <div className="min-h-screen">
      {/* Main Container */}
      <div className="container mx-auto px-4 py-8 max-w-7xl pt-16">
        {/* Header Section */}
        <div id="outreach-header" className="mb-8 mt-4">
            <div className="bg-blue-200 rounded-xl shadow-sm border border-blue-100 overflow-hidden">
              <div className="flex flex-col md:flex-row justify-between">
                <div className="p-6 mt-3">
                  <h1 className="text-3xl font-light text-blue-900 tracking-tight">Klinn Outreach</h1>
                  <p className="text-blue-600 mt-2 font-light">
                    Discover and connect with researchers, startups, and clinical opportunities
                  </p>
                </div>
                <div className="md:border-l border-blue-100 px-8 pb-3 mt-2 mb-2 md:w-96 flex items-center bg-blue-200">
                  <TokenDisplay tokens={profileData?.tokens ?? 0} bgColor="bg-blue-100" />
                </div>
              </div>
            </div>
          </div>
 
        {/* Main Content Area */}
        <div className="bg-white rounded-2xl shadow-sm border border-blue-100 overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as "browse" | "unlocked")}
            className="bg-white"
          >
            {/* Tabs Navigation */}
            <div id="outreach-tabs" className="flex flex-col sm:flex-row justify-between items-start sm:items-center px-6 pt-4 pb-2 border-b border-blue-50">
              <TabsList className="grid w-full sm:w-[400px] grid-cols-2 bg-blue-50 h-12">
                <TabsTrigger
                  value="browse"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2 font-thin">
                    <Search className="h-4 w-4" />
                    Browse
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="unlocked"
                  className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2 font-thin">
                    <Lock className="h-4 w-4" />
                    Unlocked
                  </div>
                </TabsTrigger>
              </TabsList>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowRemoveListingPopup(true)}
                className="mt-4 sm:mt-0 text-blue-600 hover:text-blue-800 hover:bg-blue-50 flex items-center gap-1.5"
              >
                <Trash2 className="h-4 w-4" />
                <span className="font-light">Remove Listing</span>
              </Button>
            </div>

            {/* Browse Listings Tab */}
            <TabsContent value="browse" className="p-6 space-y-6">
              {/* Filter Panel */}
              <Card id="filter-panel" className="border border-blue-200 overflow-hidden">
                <CardHeader className="bg-blue-600 text-white">
                  <CardTitle className="flex items-center gap-3">
                    <Filter className="h-5 w-5" />
                    <span className="font-light">Refine Your Search</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-5">
                  <div className="grid md:grid-cols-3 gap-5">
                    {/* Work Type Selector */}
                    <div>
                      <Label htmlFor="work-type" className="text-sm font-light text-blue-800 mb-2">
                        Opportunity Type
                      </Label>
                      <Select value={workType} onValueChange={(v) => setWorkType(v as typeof workType)}>
                        <SelectTrigger id="work-type" className="bg-white border-blue-200">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent className="border-blue-200">
                          <SelectItem value="research" className="hover:bg-blue-50">
                            <div className="flex items-center gap-2 text-blue-800">
                              <GraduationCap className="h-4 w-4" />
                              <span>Research</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="startup" className="hover:bg-blue-50">
                            <div className="flex items-center gap-2 text-blue-800">
                              <Rocket className="h-4 w-4" />
                              <span>Startups</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="clinic" className="hover:bg-blue-50">
                            <div className="flex items-center gap-2 text-blue-800">
                              <Building className="h-4 w-4" />
                              <span>Clinics</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Search Input */}
                    <div>
                      <Label className="text-sm font-light text-blue-800 mb-2">Search</Label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-blue-400" />
                        <Input
                          placeholder="Search by name, location, or keywords..."
                          className="pl-9 bg-white border-blue-200 focus:border-blue-400"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                      </div>
                    </div>

                    {/* Clinic ZIP Code Input */}
                    {workType === "clinic" && (
                      <div>
                        <Label htmlFor="clinicZip" className="text-sm font-light text-blue-800 mb-2">
                          ZIP Code
                        </Label>
                        <div className="relative flex">
                          <Input
                            id="clinicZip"
                            placeholder="Enter ZIP Code"
                            value={clinicZip}
                            onChange={(e) => setClinicZip(e.target.value)}
                            className="bg-white border-blue-200"
                          />
                          <Button
                            onClick={fetchClinicContacts}
                            className="ml-2 bg-blue-600 hover:bg-blue-700"
                          >
                            <Search className="h-4 w-4 mr-1" />
                            Search
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  <Separator className="bg-blue-100" />

                  {/* Interests Section */}
                  <div>
                    <Label className="mb-3 block text-sm font-light text-blue-800">Filter by Interests</Label>
                    <div className="flex flex-wrap gap-2">
                      {workType === "startup" ? (
                        startupTags.map((tag) => (
                          <Badge
                            key={tag}
                            variant={interests.includes(tag) ? "default" : "outline"}
                            className={`cursor-pointer transition-colors ${interests.includes(tag)
                              ? "bg-blue-600 text-white hover:bg-blue-700 border-blue-600"
                              : "text-blue-800 border-blue-200 hover:bg-blue-50"
                              }`}
                            onClick={() => toggleInterest(tag)}
                          >
                            {tag}
                          </Badge>
                        ))
                      ) : workType === "research" ? (
                        // Use professorKeywordMapping keys instead of static interestOptions
                        Object.keys(professorKeywordMapping).map((interest) => (
                          <Badge
                            key={interest}
                            variant={interests.includes(interest) ? "default" : "outline"}
                            className={`cursor-pointer transition-colors ${interests.includes(interest)
                              ? "bg-blue-600 text-white hover:bg-blue-700 border-blue-600"
                              : "text-blue-800 border-blue-200 hover:bg-blue-50"
                              }`}
                            onClick={() => toggleInterest(interest)}
                          >
                            {interest}
                          </Badge>
                        ))
                      ) : (
                        // Fallback (if needed) for other work types, e.g. clinics
                        interestOptions.map((interest) => (
                          <Badge
                            key={interest.id}
                            variant={interests.includes(interest.id) ? "default" : "outline"}
                            className={`cursor-pointer transition-colors ${interests.includes(interest.id)
                              ? "bg-blue-600 text-white hover:bg-blue-700 border-blue-600"
                              : "text-blue-800 border-blue-200 hover:bg-blue-50"
                              }`}
                            onClick={() => toggleInterest(interest.id)}
                          >
                            {interest.label}
                          </Badge>
                        ))
                      )}

                      {/* Render any custom interests not part of the predefined lists */}
                      {interests
                        .filter((interest) =>
                          workType === "startup"
                            ? !startupTags.includes(interest)
                            : workType === "research"
                              ? !Object.keys(professorKeywordMapping).includes(interest)
                              : !interestOptions.some((o) => o.id === interest)
                        )
                        .map((interest) => (
                          <Badge
                            key={interest}
                            variant="default"
                            className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer flex items-center gap-1 border-blue-200"
                            onClick={() => setInterests((prev) => prev.filter((i) => i !== interest))}
                          >
                            {interest}
                            <X className="h-3 w-3" />
                          </Badge>
                        ))}
                    </div>
                  </div>


                </CardContent>
              </Card>

              {/* Results Section */}
  <div id="result-container" className="space-y-4">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <h3 className="text-lg font-light text-blue-900">
          {workType === "startup"
            ? startupResults.length
            : workType === "research"
            ? sortedResearchContacts.length
            : filteredContacts.length}{" "}
          {workType === "startup"
            ? "Startups"
            : workType === "research"
            ? "Research Profiles"
            : "Clinics"}{" "}
          Found
        </h3>
        <Badge className="bg-blue-100 text-blue-800 border-blue-200">
          <Sparkles className="h-3.5 w-3.5 mr-1" />
          Sorted by relevance
        </Badge>
      </div>
    </div>

    {/* ───────── STARTUPS ───────── */}
    {workType === "startup" && (
  isMatchScoreLoading ? (
    <div className="flex flex-col items-center justify-center py-16 bg-white rounded-lg border border-blue-200">
      <Loader2 className="animate-spin h-10 w-10 text-blue-600 mb-4" />
      <p className="text-blue-700">Calculating match scores...</p>
    </div>
  ) : startupResults.length === 0 ? (
    <Card className="border-blue-200">
      <CardContent className="flex flex-col items-center justify-center py-16">
        <Search className="h-12 w-12 text-blue-300 mb-4" />
        <h3 className="text-lg font-medium text-blue-800 mb-1">No matches found</h3>
        <p className="text-blue-600 text-center max-w-md font-light">
          Try adjusting your filters or interests to find more matches.
        </p>
      </CardContent>
    </Card>
  ) : (
    <>
      <div className="grid gap-4">
        {startupPageResults.map((contact) => (
          <StartupCard
            key={contact.id}
            contact={contact}
            matchScore={contact.matchScore}
            unlocked={unlockedContacts.includes(String(contact.id))}
            onUnlock={() => handleUnlockContact(contact, "startup")}
            onSelect={handleSelectContact}
            freeSelectionUsed={Boolean(freeStartupSelections[contact.id])}
          />
        ))}
      </div>

      {/* Pagination controls for startups */}
      <div className="flex justify-center items-center gap-4 mt-6">
        <Button
          disabled={page === 1}
          onClick={() => setPage(p => Math.max(1, p - 1))}
        >
          Previous
        </Button>

        <span className="text-sm">
          Page {page} of {totalStartupPages}
        </span>

        <Button
          disabled={page === totalStartupPages}
          onClick={() => setPage(p => Math.min(totalStartupPages, p + 1))}
        >
          Next
        </Button>
      </div>
    </>
  )
)}

    {/* ───────── RESEARCH ───────── */}
    {/* ───────── RESEARCH ───────── */}
{workType === "research" && (
  isMatchScoreLoading ? (
    <div className="flex flex-col items-center justify-center py-16 bg-white rounded-lg border border-blue-200">
      <Loader2 className="animate-spin h-10 w-10 text-blue-600 mb-4" />
      <p className="text-blue-700">Calculating match scores...</p>
    </div>
  ) : sortedResearchContacts.length === 0 ? (
    <Card className="border-blue-200">
      <CardContent className="flex flex-col items-center justify-center py-16">
        <Search className="h-12 w-12 text-blue-300 mb-4" />
        <h3 className="text-lg font-medium text-blue-800 mb-1">No matches found</h3>
        <p className="text-blue-600 text-center max-w-md font-light">
          Try adjusting your filters or interests to find more matches.
        </p>
      </CardContent>
    </Card>
  ) : (
    <>
      <div className="grid gap-4">
        {researchPageResults.map(contact => (
          <ResearchCard
            key={contact.openalex_id}
            contact={contact}
            matchScore={contact.matchScore}
            unlocked={unlockedContacts.includes(contact.openalex_id)}
            onUnlock={() => handleUnlockContact(contact, "research")}
            onSelect={() => handleSelectContact(contact)}
          />
        ))}
      </div>

      {/* ← pagination controls → */}
      <div className="flex justify-center items-center gap-4 mt-6">
        <Button
          disabled={page === 1}
          onClick={() => setPage(p => Math.max(1, p - 1))}
        >
          Previous
        </Button>

        <span className="text-sm">
          Page {page} of {totalResearchPages}
        </span>

        <Button
          disabled={page === totalResearchPages}
          onClick={() => setPage(p => Math.min(totalResearchPages, p + 1))}
        >
          Next
        </Button>
      </div>
    </>
  )
)}

    {/* ───────── CLINIC ───────── */}
    {workType === "clinic" && (
  isClinicLoading ? (
    <div className="flex flex-col items-center justify-center py-16 bg-white rounded-lg border border-blue-200">
      <Loader2 className="animate-spin h-10 w-10 text-blue-600 mb-4" />
      <p className="text-blue-700">Loading clinics...</p>
    </div>
  ) : filteredContacts.length === 0 ? (
    <Card className="border-blue-200">
      <CardContent className="flex flex-col items-center justify-center py-16">
        <Search className="h-12 w-12 text-blue-300 mb-4" />
        <h3 className="text-lg font-medium text-blue-800 mb-1">No matches found</h3>
        <p className="text-blue-600 text-center max-w-md font-light">
          Try adjusting your filters or search terms to find more matches.
        </p>
      </CardContent>
    </Card>
  ) : (
    <>
      <div className="grid gap-4">
        {clinicPageResults.map((contact) => (
          <ClinicCard
            key={contact.id || contact.email}
            contact={contact}
            unlocked={unlockedContacts.includes(String(contact.id || contact.email))}
            onUnlock={() => handleUnlockContact(contact, "clinic")}
            onSelect={() => handleSelectContact(contact)}
          />
        ))}
      </div>

      {/* Pagination controls for clinics */}
      <div className="flex justify-center items-center gap-4 mt-6">
        <Button
          disabled={page === 1}
          onClick={() => setPage(p => Math.max(1, p - 1))}
        >
          Previous
        </Button>

        <span className="text-sm">
          Page {page} of {totalClinicPages}
        </span>

        <Button
          disabled={page === totalClinicPages}
          onClick={() => setPage(p => Math.min(totalClinicPages, p + 1))}
        >
          Next
        </Button>
      </div>
    </>
  )
)}
  </div>
</TabsContent>

            {/* Unlocked Listings Tab */}
            <TabsContent value="unlocked" className="p-6">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-blue-900 mb-2">Your Unlocked Contacts</h2>
                <p className="text-blue-600 font-light">Access all your unlocked research, startup, and clinic contacts</p>
              </div>

            {/* Type Toggles */}
            <div className="flex gap-4 mb-6">
              <Button
                variant={workType === "research" ? "default" : "outline"}
                onClick={() => setWorkType("research")}
                className="flex items-center gap-2"
              >
                <GraduationCap className="h-4 w-4" />
                Research
              </Button>
              <Button
                variant={workType === "startup" ? "default" : "outline"}
                onClick={() => setWorkType("startup")}
                className="flex items-center gap-2"
              >
                <Rocket className="h-4 w-4" />
                Startups
              </Button>
              <Button
                variant={workType === "clinic" ? "default" : "outline"}
                onClick={() => setWorkType("clinic")}
                className="flex items-center gap-2"
              >
                <Building className="h-4 w-4" />
                Clinics
              </Button>
            </div>

              {getAllUnlockedContacts().length === 0 ? (
                <Card className="border-blue-200">
                  <CardContent className="flex flex-col items-center justify-center py-16">
                    <Lock className="h-12 w-12 text-blue-300 mb-4" />
                    <h3 className="text-lg font-medium text-blue-800 mb-1">No unlocked contacts yet</h3>
                    <p className="text-blue-600 text-center max-w-md font-light">
                      Use your tokens to unlock contact information and they will appear here.
                    </p>
                    <Button
                      variant="outline"
                      className="mt-4 border-blue-300 text-blue-700 hover:bg-blue-50"
                      onClick={() => setActiveTab("browse")}
                    >
                      <span className="font-light">Browse Opportunities</span>
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-8">
                  {/* Research Contacts */}
                  {workType === "research" && getAllUnlockedContacts().filter((c) => c.type === "research").length > 0 && (
                    <div>
                      <div className="flex items-center gap-3 mb-4">
                        <GraduationCap className="h-6 w-6 text-blue-600" />
                        <h3 className="text-xl font-semibold text-blue-900">Research Contacts</h3>
                      </div>
                      <div className="grid gap-4">
                      {uniqueResearchContacts.map((contact) => (
                      <ResearchCard
                        key={contact.openalex_id}
                        contact={contact}
                        unlocked={unlockedContacts.includes(`${contact.openalex_id}`)}
                        onUnlock={() => handleUnlockContact(contact, "research")}
                        onSelect={() => handleSelectContact(contact)}
                        matchScore={researchScoresLookup[contact.openalex_id]}
                      />
                    ))}
                      </div>
                    </div>
                  )}

                  {/* Startup Contacts */}
                  {workType === "startup" && getAllUnlockedContacts().filter((c) => c.type === "startup").length > 0 && (
                    <div>
                      <div className="flex items-center gap-3 mb-4">
                        <Rocket className="h-6 w-6 text-blue-600" />
                        <h3 className="text-xl font-semibold text-blue-900">Startup Contacts</h3>
                      </div>
                      <div className="grid gap-4">
                      {uniqueStartupContacts.map((contact) => (
                      <StartupCard
                        key={contact.id}
                        contact={contact}
                        unlocked={true}
                        onUnlock={() => {}}
                        onSelect={() => handleSelectContact(contact)}
                        freeSelectionUsed={Boolean(hasUsedFreeSelection[contact.id])}
                        matchScore={contact.matchScore}
                        selectedContactEmail={freeStartupSelections[contact.id] ?? null}
                      />
                    ))}
                      </div>
                    </div>
                  )}

                  {/* Clinic Contacts */}
                  {workType === "clinic" && getAllUnlockedContacts().filter((c) => c.type === "clinic").length > 0 && (
                    <div>
                      <div className="flex items-center gap-3 mb-4">
                        <Building className="h-6 w-6 text-blue-600" />
                        <h3 className="text-xl font-semibold text-blue-900">Clinic Contacts</h3>
                      </div>
                      <div className="grid gap-4">
                        {getAllUnlockedContacts()
                          .filter((contact) => contact.type === "clinic")
                          .map((contact, i) => (
                            <ClinicCard
                              key={i}
                              contact={contact}
                              unlocked={true}
                              onUnlock={() => { }}
                              onSelect={() => handleSelectContact(contact)}
                            />
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        {/* Remove Listing Popup */}
        <Dialog open={showRemoveListingPopup} onOpenChange={setShowRemoveListingPopup}>
          <DialogContent className="sm:max-w-[500px] border-blue-200">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-blue-900">
                <AlertCircle className="h-5 w-5 text-blue-600" />
                Remove Your Listing
              </DialogTitle>
              <DialogDescription className="text-blue-700">
                If you are a professor or startup founder and wish to remove your contact information from our platform,
                please reach out to us.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <p className="text-sm text-blue-700 font-light">
                To request the removal of your listing, please email <strong className="text-blue-900"><EMAIL></strong> with the following
                information:
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <ul className="list-disc list-inside text-sm text-blue-800 space-y-2">
                  <li>
                    <strong>Your email must be sent from an official domain:</strong>
                    <ul className="list-disc list-inside pl-5 mt-1 space-y-1">
                      <li>
                        <strong>For professors:</strong> Use your institution&apos;s{" "}
                        <code className="bg-blue-100 px-1 rounded text-blue-800">.edu</code> email address.
                      </li>
                      <li>
                        <strong>For startup founders:</strong> Use your startup&apos;s official domain email address.
                      </li>
                    </ul>
                  </li>
                  <li>Your full name</li>
                  <li>Your position (e.g., Professor, Founder)</li>
                  <li>The name of your institution or startup</li>
                  <li>Proof of identity or affiliation (e.g., official email, website, or document)</li>
                </ul>
              </div>
              <p className="text-sm text-blue-700 font-light">We will process your request as soon as possible.</p>
            </div>
            <DialogFooter>
              <Button
                onClick={() => setShowRemoveListingPopup(false)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Email Dialog */}
        <Dialog open={emailDialogOpen} onOpenChange={setEmailDialogOpen}>
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
            <DialogContent className="sm:max-w-[600px] md:max-w-[800px] border-blue-200 max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2 text-blue-900">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <span>Compose Your Message</span>
                </DialogTitle>
                {selectedContact && (
                  <DialogDescription className="text-blue-700">
                    {workType === "research"
                      ? `Reaching out to Dr. ${selectedContact.name} (${selectedContact.institution})`
                      : workType === "startup"
                        ? `Inquiring about ${selectedContact.startup}`
                        : `Inquiring about ${selectedContact.name}`}
                  </DialogDescription>
                )}
              </DialogHeader>
              <div className="space-y-4 mt-4">
                {/* Recipient Field */}
                <div className="space-y-2">
                  <Label htmlFor="recipient" className="text-sm font-light text-blue-800">
                    Recipient
                  </Label>
                  <div className="flex items-center gap-2 border border-blue-200 rounded-md px-3 py-2 bg-white">
                    <Mail className="h-4 w-4 text-blue-400" />
                    <Input
                      id="recipient"
                      type="email"
                      placeholder="Enter email address"
                      value={emailDraft.recipient}
                      onChange={(e) => setEmailDraft((prev) => ({ ...prev, recipient: e.target.value }))}
                      className="flex-1 border-0 focus-visible:ring-0 shadow-none text-blue-900"
                    />
                  </div>
                </div>

                {/* Subject Field */}
                <div className="space-y-2">
                  <Label htmlFor="subject" className="text-sm font-light text-blue-800">
                    Subject
                  </Label>
                  <Input
                    id="subject"
                    placeholder="Enter message subject"
                    value={emailDraft.subject}
                    onChange={(e) => setEmailDraft((prev) => ({ ...prev, subject: e.target.value }))}
                    className="border-blue-200 focus:border-blue-400 text-blue-900"
                  />
                </div>

                {/* Message Field */}
                <div className="space-y-2">
                  <Label htmlFor="message" className="text-sm font-light text-blue-800">
                    Message
                  </Label>
                  <div className="flex gap-4">
                    <div className="border border-blue-200 rounded-md focus-within:ring-2 focus-within:ring-blue-500 overflow-hidden flex-grow">
                      <div className="flex gap-1 p-2 bg-blue-50 border-b border-blue-200">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant={formatting.bold ? "secondary" : "ghost"}
                                size="sm"
                                onClick={() => handleFormat("bold")}
                                className="text-blue-800 hover:bg-blue-100"
                              >
                                <Bold className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Bold</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant={formatting.italic ? "secondary" : "ghost"}
                                size="sm"
                                onClick={() => handleFormat("italic")}
                                className="text-blue-800 hover:bg-blue-100"
                              >
                                <Italic className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Italic</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>

                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                type="button"
                                variant={formatting.underline ? "secondary" : "ghost"}
                                size="sm"
                                onClick={() => handleFormat("underline")}
                                className="text-blue-800 hover:bg-blue-100"
                              >
                                <Underline className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Underline</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div
                        id="message"
                        ref={messageRef}
                        contentEditable
                        suppressContentEditableWarning={true}
                        onInput={handleMessageInput}
                        onMouseUp={saveSelection}
                        onKeyUp={saveSelection}
                        className="min-h-[200px] p-3 outline-none text-blue-900"
                      />
                    </div>

                    {/* Suggestions Panel */}
                  <div className="w-1/3 max-w-[220px]">
                      <div className="mb-2 flex items-center gap-1.5">
                        <Sparkles className="h-3.5 w-3.5 text-blue-500" />
                        <span className="text-xs text-blue-600 font-medium">Writing Suggestions</span>
                      </div>
                      <motion.div
                        className="space-y-2 overflow-y-auto max-h-[500px] pr-1"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3 }}
                      >
                        {suggestions.slice(0, 9).map((suggestion, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.05, duration: 0.2 }}
                          >
                            <SuggestionBubble
                              text={suggestion}
                              onClick={() => insertSuggestion(suggestion)}
                            />
                          </motion.div>
                        ))}
                        {suggestions.length > 9 && (
                          <motion.button
                            onClick={() => generateSuggestions().then(newSuggestions => setSuggestions(newSuggestions))}
                            className="w-full text-blue-600 text-xs hover:text-blue-800 mt-2 flex items-center justify-center gap-1"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: suggestions.length * 0.05, duration: 0.2 }}
                          >
                            <RefreshCw className="h-3 w-3" />
                            Show more suggestions
                          </motion.button>
                        )}
                      </motion.div>
                    </div>
                  </div>
                </div>

                {/* Attachment Upload */}
                <div className="space-y-2">
                  <Label className="flex items-center gap-2 text-sm font-light text-blue-800">
                    <span>Attachments</span>
                    {emailDraft.attachments && emailDraft.attachments.length > 0 && (
                      <Badge variant="secondary"
                            className="text-xs font-light bg-blue-100 text-blue-800 border-blue-200">
                        {emailDraft.attachments.length} file(s)
                      </Badge>
                    )}
                  </Label>
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor="attachment"
                      className="flex items-center gap-2 border border-dashed border-blue-300 rounded-md px-4 py-2 cursor-pointer hover:bg-blue-50 transition-colors"
                    >
                      <div className="rounded-full bg-blue-100 p-1.5">
                        <Paperclip className="h-4 w-4 text-blue-600" />
                      </div>
                      <span className="text-sm text-blue-700">
                        {emailDraft.attachments?.length ? "Add more files" : "Attach files"}
                      </span>
                    </Label>
                    <Input
                      id="attachment"
                      type="file"
                      multiple
                      className="hidden"
                      onChange={(e) => {
                        if (e.target.files) {
                          const files = Array.from(e.target.files)
                          setEmailDraft((prev) => ({
                            ...prev,
                            attachments: prev.attachments ? [...prev.attachments, ...files] : files,
                          }))
                        }
                      }}
                    />
                    {emailDraft.attachments?.length ||
                      (0 > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEmailDraft((prev) => ({ ...prev, attachments: [] }))}
                          className="text-blue-600 hover:text-blue-800 border-blue-300 hover:bg-blue-50"
                        >
                          Clear all
                        </Button>
                      ))}
                  </div>
                  {emailDraft.attachments && emailDraft.attachments.length > 0 && (
                    <ScrollArea className="mt-2 max-h-[120px]">
                      <div className="space-y-2">
                        {emailDraft.attachments.map((file, index) => (
                          <div key={index} className="flex items-center justify-between bg-blue-50 rounded-md px-3 py-2">
                            <div className="flex items-center gap-2">
                              <div className="rounded-full bg-blue-100 p-1.5">
                                <File className="h-3 w-3 text-blue-600" />
                              </div>
                              <span className="text-sm truncate max-w-[300px] text-blue-800">{file.name}</span>
                              <Badge variant="outline" className="text-xs font-light bg-white text-blue-800 border-blue-200">
                                {(file.size / 1024).toFixed(0)} KB
                              </Badge>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEmailDraft((prev) => ({
                                  ...prev,
                                  attachments: prev.attachments?.filter((_, i) => i !== index),
                                }))
                              }}
                              className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              </div>
              <DialogFooter className="mt-6">
                <Button
                  variant="outline"
                  onClick={() => setEmailDialogOpen(false)}
                  className="border-blue-300 text-blue-700 hover:bg-blue-50"
                >
                  Cancel
                </Button>

                {/* Date-time picker (local timezone) */}
                <input
                  type="datetime-local"
                  value={scheduledAt}
                  onChange={(e) => setScheduledAt(e.target.value)}
                  min={new Date(new Date().setHours(0, 0, 0, 0)).toISOString().slice(0, 16)} // Set minimum to start of today
                  className="rounded-md border border-blue-300 px-3 py-2 text-sm text-blue-800 bg-white
                            focus:border-blue-400 focus:ring-blue-400 outline-none"
                  style={{ minWidth: 190 }}
                />

                {/* Schedule send */}
                <Button
                  onClick={handleScheduleSend}
                  disabled={
                    !scheduledAt ||
                    !emailDraft.recipient ||
                    !emailDraft.subject ||
                    !emailDraft.message ||
                    isScheduling ||
                    new Date(scheduledAt) <= new Date() // Disable if selected time is in the past
                  }
                  className="gap-2 bg-blue-500 hover:bg-blue-600"
                >
                  {isScheduling ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Scheduling…
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Schedule send
                    </>
                  )}
                </Button>
                
                <Button
                  onClick={handleSendEmail}
                  disabled={
                    !emailDraft.recipient || !emailDraft.subject || !emailDraft.message || isAuthenticating || isSending
                  }
                  className="gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  {isAuthenticating ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Authenticating...
                    </>
                  ) : !googleAccessTokenState ? (
                    <>
                      <svg className="h-4 w-4" viewBox="0 0 24 24">
                        <path
                          fill="#FFFFFF"
                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                        />
                        <path
                          fill="#FFFFFF"
                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                        />
                        <path
                          fill="#FFFFFF"
                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                        />
                        <path
                          fill="#FFFFFF"
                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                        />
                      </svg>
                      Connect Google to Send
                    </>
                  ) : isSending ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Send Message
                    </>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </motion.div>
        </Dialog>
      </div>
      {showOnboarding && (
  <OutreachOnboardingPopup onComplete={() => setShowOnboarding(false)} />
)}

    </div>
  )
}
