import { Card, CardContent } from "@/components/ui/card"
import OpportunityCard from "@/components/NonOpportunity"
import OpportunityCard2 from "@/components/OpportunityCard"

interface BookmarksContentProps {
  isLoadingBookmarks: boolean
  bookmarkedOpportunities: Array<any>
}

export default function BookmarksContent({ isLoadingBookmarks, bookmarkedOpportunities }: BookmarksContentProps) {
  return (
    <div className="space-y-6">
      {/* <h2 className="text-2xl font-bold text-gray-800">Your Bookmarked Opportunities</h2> */}

      <Card>
        <CardContent className="p-6">
          {isLoadingBookmarks ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : bookmarkedOpportunities.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">You haven&apos;t bookmarked any opportunities yet.</p>
              <p className="text-sm text-gray-500">Browse opportunities and bookmark them to see them here.</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {bookmarkedOpportunities.map((opp) => {
                if ("id" in opp) {
                  return <OpportunityCard key={opp.id} opportunity={opp} />
                }
                if ("opportunity" in opp) {
                  return <OpportunityCard2 key={opp.opportunity.Name} opportunity={opp.opportunity} />
                }
                return null
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

