import { loadStripe } from '@stripe/stripe-js';

// Ensure the key is only loaded once to avoid recreating the Stripe instance on renders
let stripePromise: Promise<any> | null = null;

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || 
      'pk_test_51RB7nkRdyKY8QNLD4nq7QStlMc3xbhhc2YjYBbkC6n67acuMvWk0Oh5oNSmqGGc9IUubcQ1iHxvU3Zih15YOcx5f00lO839xK5');
  }
  return stripePromise;
}; 