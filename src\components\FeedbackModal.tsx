"use client";

import React, { useState, useEffect, Fragment } from "react";
import { MessageSquare, X, Star } from "lucide-react";
import { usePathname } from "next/navigation";
import { supabase } from "../../supabase/supabaseClient";

interface FeedbackModalProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

const FeedbackModal: React.FC<FeedbackModalProps> = ({ isOpen, setIsOpen }) => {
  const [rating, setRating] = useState<number | null>(null);
  const [feedback, setFeedback] = useState("");
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isOpen) {
      const timer = setTimeout(() => {
        setRating(null);
        setFeedback("");
        setHasSubmitted(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  useEffect(() => {
    const feedbackSubmitted = localStorage.getItem("feedbackSubmitted");
    const modalClosed = localStorage.getItem("modalClosed");

    if ((feedbackSubmitted || modalClosed) && !isOpen) return;
//add this later if need auto open
    // // auto open timer only if not manually opened
    // if (!isOpen && isMounted) { // Check isMounted
    //   const timer = setTimeout(() => {
    //     if (!localStorage.getItem("feedbackSubmitted") && !localStorage.getItem("modalClosed")) {
    //        setIsOpen(true);
    //     }
    //   }, 300000); // Open modal after 5 minutes
    //   return () => clearTimeout(timer);
    // }
  }, [isOpen, setIsOpen, isMounted]);

  const handleClose = () => {
    setIsOpen(false);
    localStorage.setItem("modalClosed", "true");
  };

  const handleSubmit = async () => {
    if (!rating) return;

    try {
      const { error } = await supabase.from("feedback").insert([
        {
          rating,
          feedback: feedback.trim() === "" ? null : feedback.trim(),
          page: pathname,
          timestamp: new Date().toISOString(),
        },
      ]);

      if (error) {
        throw error;
      }

      localStorage.setItem("feedbackSubmitted", "true");
      setHasSubmitted(true);

      setTimeout(() => {
        setIsOpen(false);
      }, 2000);
    } catch (error) {
      console.error("Error submitting feedback:", error);
      // TODO: Add error message to user here
    }
  };

  if (!isMounted) {
    return null;
  }

  return (
    <>
      {/* Overlay */}
      <div
        className={`fixed inset-0 z-40 bg-black/60 backdrop-blur-sm transition-opacity duration-300 ease-out
                    ${isOpen ? "opacity-100" : "opacity-0 pointer-events-none"}`}
        onClick={handleClose}
        aria-hidden="true"
      />

      {/* Modal */}
      <div
        className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300 ease-out
                    ${isOpen ? "opacity-100 scale-100" : "opacity-0 scale-95 pointer-events-none"}`}
      >
        <div
          className="relative w-full max-w-md overflow-hidden rounded-xl bg-white dark:bg-slate-900 shadow-xl border border-gray-200 dark:border-slate-700"
          role="dialog"
          aria-modal="true"
          aria-labelledby="feedback-modal-title"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-start justify-between p-5 border-b border-gray-200 dark:border-slate-700">
            <h2
              id="feedback-modal-title"
              className="text-lg font-semibold text-gray-900 dark:text-white"
            >
              {hasSubmitted ? "Thank You!" : "Share Your Feedback"}
            </h2>
            <button
              onClick={handleClose}
              className="p-1 text-gray-400 rounded-md hover:bg-gray-100 dark:hover:bg-slate-800 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-900"
              aria-label="Close feedback modal"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {!hasSubmitted ? (
              <>
                {/* Rating Section */}
                <div className="space-y-3">
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    How would you rate your experience?
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {[1, 2, 3, 4, 5].map((value) => (
                      <button
                        key={value}
                        onClick={() => setRating(value)}
                        className={`flex h-10 w-10 items-center justify-center rounded-lg border text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-900
                                          ${
                                            rating === value
                                              ? "bg-blue-600 border-blue-600 text-white hover:bg-blue-700" // Selected state
                                              : "bg-white dark:bg-slate-800 border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700" // Default state
                                          }`}
                      >
                        {value}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Feedback Textarea Section */}
                <div className="space-y-3">
                  <label
                    htmlFor="feedback-text"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Any additional feedback?{" "}
                    <span className="text-gray-500 dark:text-gray-400">
                      (Optional)
                    </span>
                  </label>
                  <textarea
                    id="feedback-text"
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    className="h-28 w-full resize-none rounded-md border border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-800 px-3 py-2 text-sm text-gray-900 dark:text-white placeholder:text-gray-400 dark:placeholder:text-slate-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 dark:focus:border-blue-600 dark:focus:ring-blue-600"
                    placeholder="Tell us how we can improve..."
                  />
                </div>

                {/* Submit Button */}
                <button
                  onClick={handleSubmit}
                  disabled={!rating}
                  className={`inline-flex w-full items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-slate-900
                                    ${
                                      rating
                                        ? "bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600" // Enabled state
                                        : "bg-gray-200 dark:bg-slate-700 text-gray-400 dark:text-slate-500 cursor-not-allowed" // Disabled state
                                    }`}
                >
                  Submit Feedback
                </button>
              </>
            ) : (
              <p className="text-center text-gray-700 dark:text-gray-300">
                We appreciate your feedback! Your input helps us make things
                better.
              </p>
            )}
          </div>
        </div>
      </div>

      {/*uncomment this if need a floating button*/}
      {/* <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-5 right-5 z-30 flex h-12 w-12 items-center justify-center rounded-full bg-blue-600 text-white shadow-lg transition-transform hover:scale-105 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-900"
        aria-label="Open feedback modal"
      >
        <MessageSquare size={20} />
      </button> */}
    </>
  );
};

export default FeedbackModal;
