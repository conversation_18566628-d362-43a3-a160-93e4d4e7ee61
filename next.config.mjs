/** @type {import('next').NextConfig} */
const nextConfig = {
    images: {
      remotePatterns: [
        {
          protocol: 'https',
          hostname: 'firebasestorage.googleapis.com',
          port: '',
          pathname: '/**',
        },
        {
          protocol: 'https',
          hostname: 'img.logo.dev',
          port: '',
          pathname: '/**',
        },
      ],
    },
    webpack: (config, { isServer }) => {
      // Exclude Supabase functions from webpack compilation
      config.externals = config.externals || [];
      config.externals.push(/^supabase\/functions\//);
      return config;
    },
  };

  export default nextConfig;
