// File: /app/api/auth/google/callback/route.ts
import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(req: NextRequest) {
  // Create a Supabase client for route handlers
  const supabase = createRouteHandlerClient({ cookies });
  const url = new URL(req.url);

  // 1) Get the "code" from Google
  const code = url.searchParams.get("code");
  if (!code) {
    return NextResponse.redirect(new URL("/auth?error=missing_code", req.url));
  }

  // 2) Exchange the code for a Supabase session
  const { data, error } = await supabase.auth.exchangeCodeForSession(code);
  if (error) {
    console.error("Supabase OAuth exchange error:", error);
    return NextResponse.redirect(new URL("/auth?error=exchange_failed", req.url));
  }

  const session = data?.session;
  if (!session?.user) {
    return NextResponse.redirect(new URL("/auth?error=no_session", req.url));
  }

 // 3) Store the Google token details in your DB
if (session.provider_token) {
  const lifetime  = (session as any).provider_token_expires_in ?? 3600;
  const expiresAt = new Date(Date.now() + lifetime * 1000).toISOString();

  // build payload dynamically
  const payload: any = {
    user_id:      session.user.id,
    provider:     "google",
    access_token: session.provider_token,
    expires_at:   expiresAt,
  };

  // ⬇ only set this if Google actually sent one
  if (session.provider_refresh_token) {
    payload.refresh_token = session.provider_refresh_token;
  }

  await supabase.from("user_oauth_tokens2")
                .upsert(payload, { onConflict: "user_id,provider" });
}


  // 4) Create or update the user profile including referral data.
  try {
    const fullName = session.user.user_metadata?.full_name || "";
    const nameParts = fullName.split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    // Read the referral cookie. Cookies are available in the Next.js route handler.
    const referralCookie = req.cookies.get("referral");
    const referral = referralCookie?.value || null;

    // Upsert the profile and include the referral data.
    const { error: profileError } = await supabase.from("profiles").upsert({
      id: session.user.id,
      first_name: firstName,
      last_name: lastName,
      email: session.user.email,
      role: "general",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      // Include referral if present.
      referred_by: referral,
    }, {
      onConflict: 'id',
      ignoreDuplicates: false,
    });

    if (profileError) {
      console.error("Profile creation error:", profileError);
    }
  } catch (profileErr) {
    console.error("Error creating profile:", profileErr);
  }

  // 5) Check onboarding status before redirecting.
  const { data: profile } = await supabase
    .from('profiles')
    .select('onboarding_complete')
    .eq('id', session.user.id)
    .single();

  const isOnboardingComplete = profile?.onboarding_complete;

  // Redirect based on onboarding status.
  return NextResponse.redirect(
    new URL(isOnboardingComplete ? '/profile-dashboard' : '/onboard-signup', req.url)
  );
}
