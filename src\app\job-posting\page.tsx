'use client';

import React, { useState, useEffect, FormEvent, ChangeEvent } from 'react';
import { motion } from 'framer-motion';
import { UserPlus, PlusCircle, X, ChevronRight } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../supabase/supabaseClient';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import JobBoard from '../../components/JobBoard';

interface OpeningFormData {
  clinicName: string;
  address: string;
  city: string;
  state: string;
  type: string;
  timeCommitment: string;
  availability: string;
  positionTitle: string;
  compensation: string;
  description: string;
}

const ClinicOpenings = () => {
  const { user } = useAuth();
  const router = useRouter();

  const [openingFormData, setOpeningFormData] = useState<OpeningFormData>({
    clinicName: '',
    address: '',
    city: '',
    state: '',
    type: 'internship',
    timeCommitment: '',
    availability: 'weekdays',
    positionTitle: '',
    compensation: '',
    description: '',
  });

  const [showJobBoard, setShowJobBoard] = useState(false);
  const [customQuestions, setCustomQuestions] = useState<string[]>([]);
  const [questionInput, setQuestionInput] = useState('');

  useEffect(() => {
    document.title = 'Klinn | Medical Student Opportunities';
  }, []);

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setOpeningFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      const { data, error } = await supabase
        .from('clinic_openings')
        .insert({
          clinic_name: openingFormData.clinicName,
          address: openingFormData.address,
          city: openingFormData.city,
          state: openingFormData.state,
          type: openingFormData.type,
          time_commitment: openingFormData.timeCommitment,
          availability: openingFormData.availability,
          position_title: openingFormData.positionTitle,
          compensation: openingFormData.compensation,
          description: openingFormData.description,
          custom_questions: customQuestions,
          status: 'pending',
          user_id: user?.id || null,
          created_at: new Date().toISOString(),
        })
        .maybeSingle();

      if (error) throw error;

      alert('Your clinic opening has been submitted successfully!');
      setOpeningFormData({
        clinicName: '',
        address: '',
        city: '',
        state: '',
        type: 'internship',
        timeCommitment: '',
        availability: 'weekdays',
        positionTitle: '',
        compensation: '',
        description: '',
      });
      setCustomQuestions([]);
      setQuestionInput('');
      setShowJobBoard(true);
    } catch (error: any) {
      console.error('Error submitting clinic opening:', error);
      alert('There was an error submitting your opening. Please try again.');
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-400 to-teal-200 relative overflow-x-hidden">
      <Navbar />
      <main className="flex-grow container mx-auto px-4 md:px-6 py-16">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-extralight text-white mb-2 pt-12 max-w-2xl mx-auto tracking-tight leading-none">
            Medical Student Opportunities
          </h1>
          <p className="text-base md:text-lg text-white opacity-70 font-thin tracking-wider">
            Connect talent with experience
          </p>
        </motion.div>

        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white bg-opacity-90 backdrop-blur-sm rounded-2xl shadow-sm p-8 md:p-10"
          >
            <div className="flex justify-between items-center mb-8 pb-2 border-b border-gray-50">
              <h2 className="text-2xl font-extralight text-blue-500 tracking-wide">
                Post a Medical Student Opening
              </h2>
              
              <div className="hidden md:flex space-x-1 text-gray-500 text-xs font-thin">
                {['Internship', 'Shadowing', 'Research', 'Volunteer'].map((type, i) => (
                  <span key={i} className="px-2 py-1 rounded-full bg-blue-100">
                    {type}
                  </span>
                ))}
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="clinicName"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    Clinic Name
                  </label>
                  <input
                    type="text"
                    id="clinicName"
                    name="clinicName"
                    value={openingFormData.clinicName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                  />
                </div>
                <div>
                  <label
                    htmlFor="positionTitle"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    Position Title
                  </label>
                  <input
                    type="text"
                    id="positionTitle"
                    name="positionTitle"
                    value={openingFormData.positionTitle}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="address"
                  className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                >
                  Address
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={openingFormData.address}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="city"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    City
                  </label>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    value={openingFormData.city}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                  />
                </div>
                <div>
                  <label
                    htmlFor="state"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    State
                  </label>
                  <input
                    type="text"
                    id="state"
                    name="state"
                    value={openingFormData.state}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="type"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    Type of Opening
                  </label>
                  <div className="relative">
                    <select
                      id="type"
                      name="type"
                      value={openingFormData.type}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 appearance-none transition-all"
                    >
                      <option value="internship">Internship</option>
                      <option value="shadowing">Shadowing</option>
                      <option value="research">Research Assistant</option>
                      <option value="volunteer">Volunteer</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-300">
                      <ChevronRight className="h-4 w-4 rotate-90" />
                    </div>
                  </div>
                </div>
                <div>
                  <label
                    htmlFor="timeCommitment"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    Time Commitment
                  </label>
                  <input
                    type="text"
                    id="timeCommitment"
                    name="timeCommitment"
                    value={openingFormData.timeCommitment}
                    onChange={handleInputChange}
                    placeholder="e.g., 20 hrs/week, 3 months"
                    required
                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="availability"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    Availability
                  </label>
                  <div className="relative">
                    <select
                      id="availability"
                      name="availability"
                      value={openingFormData.availability}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 appearance-none transition-all"
                    >
                      <option value="weekdays">Weekdays</option>
                      <option value="weekends">Weekends</option>
                      <option value="flexible">Flexible</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-300">
                      <ChevronRight className="h-4 w-4 rotate-90" />
                    </div>
                  </div>
                </div>
                <div>
                  <label
                    htmlFor="compensation"
                    className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                  >
                    Compensation
                  </label>
                  <input
                    type="text"
                    id="compensation"
                    name="compensation"
                    value={openingFormData.compensation}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="block text-xs font-thin text-gray-400 mb-1 uppercase tracking-wider"
                >
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={openingFormData.description}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 resize-none transition-all"
                  rows={3}
                />
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <label
                    htmlFor="customQuestion"
                    className="block text-xs font-thin text-gray-400 uppercase tracking-wider"
                  >
                    Additional Questions
                  </label>
                  <span className="text-xs text-gray-300 font-extralight">
                    {customQuestions.length} added
                  </span>
                </div>
                
                <div className="flex gap-2">
                  <input
                    type="text"
                    id="customQuestion"
                    name="customQuestion"
                    value={questionInput}
                    onChange={(e) => setQuestionInput(e.target.value)}
                    placeholder="Enter application question"
                    className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md focus:ring-1 focus:ring-blue-300 font-extralight text-gray-700 transition-all"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      if (questionInput.trim() !== '') {
                        setCustomQuestions([...customQuestions, questionInput.trim()]);
                        setQuestionInput('');
                      }
                    }}
                    className="p-2 bg-blue-400 hover:bg-blue-500 text-white rounded-md transition-colors flex-shrink-0"
                    aria-label="Add question"
                  >
                    <PlusCircle className="w-5 h-5" />
                  </button>
                </div>
                
                {customQuestions.length > 0 && (
                  <div className="mt-2 space-y-1.5">
                    {customQuestions.map((question, index) => (
                      <motion.div 
                        key={index}
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.2 }}
                        className="flex items-center justify-between bg-blue-50 p-2 px-3 rounded-md group"
                      >
                        <span className="font-extralight text-sm text-gray-600 truncate pr-2">
                          {question}
                        </span>
                        <button
                          type="button"
                          onClick={() => setCustomQuestions(customQuestions.filter((_, i) => i !== index))}
                          className="opacity-0 group-hover:opacity-100 text-gray-300 hover:text-blue-500 transition-all flex-shrink-0"
                          aria-label="Remove question"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>

              <div className="pt-4">
                <motion.button
                  type="submit"
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full bg-blue-400 hover:bg-blue-500 text-white font-thin text-sm py-3 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-300 transition-all flex items-center justify-center"
                >
                  <UserPlus className="w-4 h-4 mr-2 opacity-70" />
                  <span className="tracking-wider uppercase">Submit Opening</span>
                </motion.button>
              </div>
            </form>
          </motion.div>
          
          <motion.div 
            className="mt-8 text-center text-white opacity-60 font-extralight text-sm tracking-wide"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            <p className="max-w-md mx-auto">
              Connect with aspiring medical students and create valuable learning experiences for the next generation of healthcare professionals.
            </p>
          </motion.div>
        </div>
      </main>
      <Footer className="bg-transparent text-white p-4 transition-all duration-300 ease-in-out mt-auto" />
    </div>
  );
};

export default ClinicOpenings;