"use client";
import React, { ChangeEvent, FormEvent } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import Navbar from "@/components/Navbar";
import AdNavbar from "@/components/NavbarAd";

import Footer from "@/components/Footer";

export default function GraduateConsulting() {
  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // put this to the firebase
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-purple-50 to-indigo-100">
      <Navbar />
      <div className="relative bottom-0 left-0 right-0 h-12 sm:h-24 bg-gradient-to-t from-transparent to-purple-300"></div>
      <main className="flex-grow py-4 sm:py-8">
        <div className="container mx-auto px-4 sm:px-32">
          {/* Page Header */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-8 w-full max-w-7xl mx-auto">
            <h1 className="text-3xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-b from-purple-500 to-purple-300 mb-4 sm:mb-0 h-24">
              Graduate School Consulting
            </h1>
            <Link href="/" className="w-full sm:w-auto">
              <Button
                variant="outline"
                className="flex items-center justify-center space-x-2 text-purple-500 border-purple-300 hover:bg-purple-50 transition-all duration-300 w-full sm:max-w-xs"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Home</span>
              </Button>
            </Link>
          </div>

          {/* Main Content */}
          <div className="grid md:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {/* Description Section */}
            <Card className="p-6 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <h2 className="text-2xl font-bold text-purple-600">Advance Your Academic Journey</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700">
                    Our graduate school consulting service provides expert guidance for those seeking advanced degrees across various disciplines. We help you navigate the complex application process for Master&apos;s, PhD, Law, and other professional programs.
                  </p>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold text-purple-500">Program-Specific Guidance</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>PhD Programs (Research & Academia)</li>
                        <li>Master&apos;s Programs (MA, MS, MBA)</li>
                        <li>Law School (JD, LLM)</li>
                        <li>Other Professional Degrees</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-purple-500">Application Support</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>Statement of purpose development</li>
                        <li>Research proposal guidance</li>
                        <li>CV/Resume optimization</li>
                        <li>Writing sample selection & editing</li>
                        <li>Recommendation letter strategy</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-purple-500">Test Preparation</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>GRE strategy and planning</li>
                        <li>GMAT preparation</li>
                        <li>LSAT guidance</li>
                        <li>Subject test planning</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-purple-500">Career Integration</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>Program selection strategy</li>
                        <li>Research alignment planning</li>
                        <li>Career trajectory guidance</li>
                        <li>Funding and scholarship advice</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Form */}
            <Card className="p-6 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <h2 className="text-2xl font-bold text-purple-600">Join the Graduate School Consulting Waitlist</h2>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="Enter your full name"
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label htmlFor="program" className="block text-sm font-medium text-gray-700 mb-1">
                      Intended Program Type
                    </label>
                    <select
                      id="program"
                      className="w-full p-2 border rounded-md border-gray-300 focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">Select program type</option>
                      <option value="phd">PhD Program</option>
                      <option value="masters">Master&apos;s Program</option>
                      <option value="law">Law School</option>
                      <option value="mba">MBA</option>
                      <option value="other">Other Professional Degree</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="field" className="block text-sm font-medium text-gray-700 mb-1">
                      Field of Study
                    </label>
                    <select
                      id="field"
                      className="w-full p-2 border rounded-md border-gray-300 focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">Select your field</option>
                      <option value="stem">STEM</option>
                      <option value="humanities">Humanities</option>
                      <option value="social">Social Sciences</option>
                      <option value="business">Business</option>
                      <option value="law">Law</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-1">
                      Application Timeline
                    </label>
                    <select
                      id="timeline"
                      className="w-full p-2 border rounded-md border-gray-300 focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">Select your timeline</option>
                      <option value="2024">Fall 2024</option>
                      <option value="2025">Fall 2025</option>
                      <option value="2026">Fall 2026</option>
                      <option value="undecided">Undecided</option>
                    </select>
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white transition-colors duration-300"
                  >
                    Join Waitlist
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <div className="relative bottom-0 left-0 right-0 h-12 sm:h-24 bg-gradient-to-b from-transparent to-purple-300"></div>
      <Footer className="bg-purple-300"/>
    </div>
  );
}