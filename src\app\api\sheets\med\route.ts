import { NextResponse } from 'next/server';

/**
 * GET /api/sheets/med
 * Proxies the “Opportunities” sheet (range A1:O500).
 */
export async function GET() {
  const API_KEY   = process.env.NEXT_PUBLIC_GOOGLE_SHEETS_API_KEY;
  const SHEET_ID  = process.env.NEXT_PUBLIC_SPREADSHEET_ID;          // ← medical ID
  const SHEET     = 'Opportunities';
  const RANGE     = 'A1:O500';

  if (!API_KEY || !SHEET_ID) {
    return NextResponse.json(
      { error: 'Google-Sheets env vars are missing' },
      { status: 500 },
    );
  }

  const url =
    `https://sheets.googleapis.com/v4/spreadsheets/` +
    `${SHEET_ID}/values/${SHEET}!${RANGE}?key=${API_KEY}`;

  const upstream = await fetch(url, { cache: 'no-store' });          // always fresh
  if (!upstream.ok) {
    return NextResponse.json(
      { error: 'Google Sheets request failed' },
      { status: upstream.status },
    );
  }

  return NextResponse.json(await upstream.json(), { status: 200 });
}
