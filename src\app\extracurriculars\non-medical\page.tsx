"use client"
import { useEffect, useMem<PERSON>, useRef, useState } from "react"
import Link from "next/link"
import { Search, Users, BookOpen, Heart, ChevronDown, Filter, BookmarkIcon, ChevronsDown, ArrowRight } from "lucide-react"
import { motion } from "framer-motion"
import { useAuth } from "@/context/AuthContext"
import Navbar from "@/components/Navbar"
import Footer from "@/components/Footer"
import { SparklesCore } from "@/components/ui/sparkles"

import OpportunityCard from "@/components/NonOpportunity"
import { supabase } from "../../../../supabase/supabaseClient"
import { ExtracurricularLimits } from "@/components/ExtracurricularLimits"
import { useSubscription } from "@/hooks/useSubscription"

// Initialize Supabase client
// const supabase = createClient(
//   process.env.NEXT_PUBLIC_SUPABASE_URL!,
//   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
// )

// Add plan type interface
type PlanType = 'free' | 'basic_monthly' | 'premium_monthly' | null;

interface UserProfile {
  plan_type: PlanType;
}


// ---------------------------------------------
// Static "Benefits" Data
// ---------------------------------------------
const benefits = [
  {
    icon: <Users className="w-8 h-8" />,
    title: "Leadership Development",
    description:
      "Take charge of clubs, organizations, and community initiatives to develop essential leadership skills.",
  },
  {
    icon: <BookOpen className="w-8 h-8" />,
    title: "Skill Enhancement",
    description: "Participate in workshops, competitions, and hands-on projects to build practical expertise.",
  },
  {
    icon: <Heart className="w-8 h-8" />,
    title: "Community Impact",
    description: "Make a difference in your community through meaningful volunteer work and social initiatives.",
  },
]

// ---------------------------------------------
// Filter Config
// ---------------------------------------------
const filterConfig = {
  age: {
    label: "Age",
    options: ["Any", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22"],
  },
  grade: {
    label: "Grade",
    options: ["Any", "6", "7", "8", "9", "10", "11", "12", "College"],
  },
  location: {
    label: "Location",
    options: [
      "Any",
      "Virtual",
      "International",
      "Alabama",
      "Alaska",
      "Arizona",
      "Arkansas",
      "California",
      "Colorado",
      "Connecticut",
      "Delaware",
      "Florida",
      "Georgia",
      "Hawaii",
      "Idaho",
      "Illinois",
      "Indiana",
      "Iowa",
      "Kansas",
      "Kentucky",
      "Louisiana",
      "Maine",
      "Maryland",
      "Massachusetts",
      "Michigan",
      "Minnesota",
      "Mississippi",
      "Missouri",
      "Montana",
      "Nebraska",
      "Nevada",
      "New Hampshire",
      "New Jersey",
      "New Mexico",
      "New York",
      "North Carolina",
      "North Dakota",
      "Ohio",
      "Oklahoma",
      "Oregon",
      "Pennsylvania",
      "Rhode Island",
      "South Carolina",
      "South Dakota",
      "Tennessee",
      "Texas",
      "Utah",
      "Vermont",
      "Virginia",
      "Washington",
      "West Virginia",
      "Wisconsin",
      "Wyoming",
    ],
  },
  timeline: {
    label: "Timeline",
    options: ["Any", "School Year", "Summer", "School Year/Summer", "Fall", "Spring"],
  },
  compensation: {
    label: "Compensation",
    options: ["Any", "Prizes", "Scholarships", "Stipend", "Unpaid"],
  },
  fees: {
    label: "Program Fee",
    options: ["Any", "Free", "Under $100", "$100-$500", "$500-$1000", "$1000+"],
  },
  tags: {
    label: "Interests",
    options: [
      "Any",
      "AI",
      "Advocacy",
      "Astronomy",
      "Athletics",
      "Biology",
      "Business",
      "Chemistry",
      "Computer Science",
      "Dance",
      "Data Science",
      "Economics",
      "Engineering",
      "Entrepreneurship",
      "Environmental Science",
      "Finance",
      "Fine Arts",
      "Geography",
      "History",
      "Language",
      "Law",
      "Leadership",
      "Liberal Arts",
      "Marine Science",
      "Math",
      "Mathematics",
      "Music",
      "Philosophy",
      "Physics",
      "Policy",
      "Psychology",
      "Research",
      "Robotics",
      "STEM",
      "Writing",
    ],
  },
  programType: {
    label: "Program Type",
    options: [
      "Any",
      "Competition",
      "School Club",
      "Educational Program",
      "Summer Program",
      "Internship",
      "Fellowship",
      "Nonprofit Organization",
      "Research Program",
      "Research Internship Program",
      "Apprenticeship",
      "Course",
      "Job",
      "STEM Program",
    ],
  },
}

// ---------------------------------------------
// Opportunity interface
// ---------------------------------------------
interface Opportunity {
  id: string
  tags: string[]
  title: string
  location: string[]
  age: string
  grades: string
  description: string
  submissionDate: string
  opportunityType: string
  compensation: string
  fee: string
  competitiveness: string
  website?: string
  contact?: string
  sponsor?: string
}

// ---------------------------------------------
// Fetch Data from Google Sheets
// ---------------------------------------------
const fetchData = async (): Promise<Opportunity[]> => {
  try {
    const response = await fetch("/api/sheets/non-med", { cache: "no-store" });
    if (!response.ok) throw new Error(`HTTP error ${response.status}`);

    const result = await response.json();

    if (result.values && result.values.length > 1) {
      const headers: string[] = result.values[0];            // ← typed
      const mappedRows = result.values.slice(1).map((row: string[], idx: number) => {
        const mapped: Record<string, string> = {};

        // add explicit param types ↓↓↓
        headers.forEach((h: string, i: number) => {
          mapped[h] = row[i] || "";
        });

        return {
          id: `op-${idx}`,
          title: mapped["Name"],
          location: mapped["Location"]
            ? mapped["Location"].split(",").map((l) => l.trim())
            : [],
          age: mapped["Ages"],
          grades: mapped["Grades"],
          description: mapped["Description"],
          submissionDate: mapped["Opportunity Date"],
          opportunityType: mapped["Opportunity Type"],
          compensation: mapped["Compensation"],
          fee: mapped["Fee ($)"],
          competitiveness: mapped["Prestige/Competitiveness"],
          website: mapped["Website"],
          contact: mapped["Contact"],
          tags: mapped["Tags"] ? mapped["Tags"].split(",").map((t) => t.trim()) : [],
          sponsor: mapped["Sponsor"]
        } as Opportunity;
      });

      /* -- optional sanity filter/sort exactly as before -- */
      const cleaned = mappedRows
        .filter(
          (o: { title: string; opportunityType: string; description: string }) =>
            o.title?.trim() &&
            o.opportunityType?.trim() &&
            o.description?.trim(),
        )
        .sort((a: { sponsor: any }, b: { sponsor: any }) =>
          a.sponsor && !b.sponsor ? -1 : !a.sponsor && b.sponsor ? 1 : 0,
        );

      return cleaned;
    }

    return [];
  } catch (err) {
    console.error("Error fetching data from Sheets proxy:", err);
    return [];
  }
};

/* ---------- rest of the component unchanged ---------- */


// ---------------------------------------------
// Main Page Component
// ---------------------------------------------
interface FilterState {
  age: string;
  grade: string;
  location: string;
  timeline: string;
  compensation: string;
  fees: string;
  tags: string;
  programType: string;
}

const Page = () => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([])
  const [filteredOpportunities, setFilteredOpportunities] = useState<Opportunity[]>([])
  const [displayedOpportunities, setDisplayedOpportunities] = useState<Opportunity[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [showLoginOverlay, setShowLoginOverlay] = useState(false)
  // Never show premium overlay for premium users, even during initial render
  const [showPremiumOverlay, setShowPremiumOverlay] = useState(false)
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 0)
  const [userPlan, setUserPlan] = useState<PlanType>(null);

  // Get authentication status from AuthContext
  const { user, isAuthenticated } = useAuth()

  // Get subscription info to determine if user has premium
  const { planType } = useSubscription();
  // Premium users have unlimited access
  const hasUnlimitedAccess = planType === 'premium_monthly' || planType === 'premium_yearly';
  // Set a reasonable limit based on plan type
  const nonmedECsLimit = hasUnlimitedAccess ? 999999 : planType === 'basic_monthly' || planType === 'basic_yearly' ? 100 : 30;

  // Force premium overlay to always be false for premium users
  const actualShowPremiumOverlay = hasUnlimitedAccess ? false : showPremiumOverlay;

  // Unified filter state
  const [filters, setFilters] = useState<FilterState>({
    age: "Any",
    grade: "Any",
    location: "Any",
    timeline: "Any",
    compensation: "Any",
    fees: "Any",
    tags: "Any",
    programType: "Any",
  })

  // Local state to track which filter dropdown is open
  const [activeFilter, setActiveFilter] = useState<string | null>(null)
  const dropdownRef = useRef<HTMLDivElement | null>(null)

  // Immediately disable premium overlay for premium users
  useEffect(() => {
    if (hasUnlimitedAccess) {
      setShowPremiumOverlay(false);

      // Add a direct DOM manipulation as a last resort
      // This will forcibly remove any premium overlay that might appear
      const removeOverlay = () => {
        const overlays = document.querySelectorAll('.fixed.inset-0.bg-blue-900\\/80');
        overlays.forEach(overlay => {
          if (overlay.innerHTML.includes('Unlock Premium Access')) {
            overlay.remove();
          }
        });
      };

      // Run immediately
      removeOverlay();

      // Also run after a short delay to catch any that appear during loading
      const timerId = setTimeout(removeOverlay, 100);
      const timerId2 = setTimeout(removeOverlay, 500);
      const timerId3 = setTimeout(removeOverlay, 1000);

      // Set up an interval to keep checking
      const intervalId = setInterval(removeOverlay, 2000);

      return () => {
        clearTimeout(timerId);
        clearTimeout(timerId2);
        clearTimeout(timerId3);
        clearInterval(intervalId);
      };
    }
  }, [hasUnlimitedAccess]);

  // Refs
  const opportunitiesSectionRef = useRef<HTMLDivElement>(null)
  const listingsEndRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setActiveFilter(null)
      }
    }
    document.addEventListener("mousedown", handleOutsideClick)
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick)
    }
  }, [])

  // Window resize handler
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleFilter = (filterKey: string) => {
    setActiveFilter((prev) => (prev === filterKey ? null : filterKey))
  }

  // Load data once
  useEffect(() => {
    const loadOpportunities = async () => {
      const data = await fetchData()
      setOpportunities(data)
      setFilteredOpportunities(data)
    }
    loadOpportunities()
  }, [])

  // Handle filter changes
  const handleFilterChange = (filterType: keyof FilterState, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: value,
    }))
  }

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      age: "Any",
      grade: "Any",
      location: "Any",
      timeline: "Any",
      compensation: "Any",
      fees: "Any",
      tags: "Any",
      programType: "Any",
    })
    setSearchQuery("")
    setActiveFilter(null)
  }

  // Count active filters
  const countAppliedFilters = () => {
    return Object.values(filters).filter((value) => value !== "Any").length
  }

  // Function to scroll to the opportunities section
  const scrollToOpportunitiesSection = () => {
    opportunitiesSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
  }

  // Update filtered opportunities when filters or search changes
  useEffect(() => {
    const filtered = opportunities.filter((opp) => {
      // Combined text search
      const combinedText = (
        opp.title +
        " " +
        opp.description +
        " " +
        opp.opportunityType +
        " " +
        opp.location.join(" ")
      ).toLowerCase()

      if (searchQuery && !combinedText.includes(searchQuery.toLowerCase())) {
        return false
      }

      // Age filter
      if (filters.age !== "Any") {
        const ageRange = opp.age.replace(/\s+/g, "").split(",")
        if (!ageRange.includes(filters.age)) {
          return false
        }
      }

      // Grade filter
      if (filters.grade !== "Any") {
        const gradeRange = opp.grades.replace(/\s+/g, "").split(",")
        if (!gradeRange.includes(filters.grade)) {
          return false
        }
      }

      // Location filter
      if (filters.location !== "Any") {
        const locationMatch = opp.location.some((loc) =>
          loc.toLowerCase().includes(filters.location.toLowerCase()),
        )
        if (!locationMatch) {
          return false
        }
      }

      // Timeline filter
      if (filters.timeline !== "Any") {
        if (!opp.submissionDate.toLowerCase().includes(filters.timeline.toLowerCase())) {
          return false
        }
      }

      // Compensation filter
      if (filters.compensation !== "Any") {
        if (!opp.compensation.toLowerCase().includes(filters.compensation.toLowerCase())) {
          return false
        }
      }

      // Fees filter
      if (filters.fees !== "Any") {
        const feeValue = opp.fee.toLowerCase()
        if (filters.fees === "Free") {
          return feeValue.includes("free") || feeValue.includes("n/a") || feeValue.includes("$0")
        }
        // (Additional fee range logic can be added here)
      }

      // Tags filter
      if (filters.tags && filters.tags !== "Any") {
        if (!Array.isArray(opp.tags)) return false;
        const selectedTag = filters.tags.toLowerCase();
        const oppTagsLower = opp.tags.map((t: string) => t.toLowerCase());
        if (!oppTagsLower.includes(selectedTag)) {
          return false;
        }
      }

      // Program Type filter
      if (filters.programType && filters.programType !== "Any") {
        const selectedProgramType = filters.programType.toLowerCase()
        const oppProgramTypes = opp.opportunityType.split(",").map((type) => type.trim().toLowerCase())
        if (!oppProgramTypes.includes(selectedProgramType)) {
          return false
        }
      }

      return true
    })

    setFilteredOpportunities(filtered)
  }, [opportunities, searchQuery, filters])


  // Fetch user's plan type when authenticated
  useEffect(() => {
    const fetchUserPlan = async () => {
      if (!isAuthenticated || !user) return;
      
      try {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('plan_type')
          .eq('id', user.id)
          .maybeSingle();
          
          if (error) console.error(error);
          console.log('Profile row:', profile);          // <-- see what comes back
          
          setUserPlan(profile?.plan_type?.toLowerCase() as PlanType ?? 'free');
      } catch (err) {
        console.error('Error fetching user plan:', err);
        setUserPlan('free'); // Default to free plan on error
      }
    };

    fetchUserPlan();
  }, [isAuthenticated, user]);

  // Get view limit based on authentication and plan type
  const getViewLimit = () => {
    if (!isAuthenticated) return 15;
    if (!userPlan) return 15;
    
    switch (userPlan) {
      case 'free':
        return 30;
      case 'basic_monthly':
        return 60;
      case 'premium_monthly':
        return Infinity;
      default:
        return 15;
    }
  };

  // Update displayed opportunities based on view limit
  useEffect(() => {
    const viewLimit = getViewLimit();
    setDisplayedOpportunities(filteredOpportunities.slice(0, viewLimit));
  }, [filteredOpportunities, isAuthenticated, userPlan]);

  // Completely disable premium overlay for premium users with MutationObserver
  useEffect(() => {
    // If user has unlimited access, never show premium overlay
    if (hasUnlimitedAccess) {
      // Create a MutationObserver to ensure the overlay stays hidden
      const observer = new MutationObserver(() => {
        if (showPremiumOverlay) {
          setShowPremiumOverlay(false);
        }
      });

      // Start observing the document
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return () => observer.disconnect();
    }
  }, [hasUnlimitedAccess, showPremiumOverlay]);

  // Limit displayed opportunities based on subscription plan
  useEffect(() => {
    // If not authenticated, show limited opportunities
    if (!isAuthenticated) {
      setDisplayedOpportunities(filteredOpportunities.slice(0, 30));
      return;
    }

    // If authenticated, use the subscription limit
    const limit = typeof nonmedECsLimit === 'number' ? nonmedECsLimit : 999999;
    setDisplayedOpportunities(filteredOpportunities.slice(0, limit));
  }, [filteredOpportunities, isAuthenticated, nonmedECsLimit])


  // Intersection observer to trigger modals when scrolling to end
  useEffect(() => {
    // Skip completely for premium users
    if (hasUnlimitedAccess) return;

    if (!listingsEndRef.current) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Don't show any overlays if opportunities are still loading or being filtered
          if (opportunities.length === 0 || filteredOpportunities.length === 0 || displayedOpportunities.length === 0) {
            return;
          }

          if (!isAuthenticated && filteredOpportunities.length > 30) {
            setShowLoginOverlay(true);
          } else if (isAuthenticated && !hasUnlimitedAccess && filteredOpportunities.length > 60) {
            // Only show premium overlay for non-premium users
            if (!hasUnlimitedAccess) {
              setShowPremiumOverlay(true);
            }
          }
        }
      });
    }, { threshold: 0.1 });

    observer.observe(listingsEndRef.current);
    return () => observer.disconnect();
  }, [isAuthenticated, filteredOpportunities.length, hasUnlimitedAccess, opportunities.length]);


  useEffect(() => {
    document.title = "Klinn | Non-Medical Opportunities"
  }, [])

  // Handle login click from overlay
  const handleLoginClick = () => {
    setShowLoginOverlay(false);
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      {/* Hero Section */}
      <div className="relative min-h-screen bg-gradient-to-b from-indigo-600 to-indigo-800">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff0a_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0a_1px,transparent_1px)] bg-[size:44px_44px]" />
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
            <div className="relative">
              <div className="absolute -left-40 -top-40 h-80 w-80 rounded-full bg-purple-400/30 blur-[120px]" />
              <div className="absolute -right-40 -bottom-40 h-80 w-80 rounded-full bg-indigo-400/30 blur-[120px]" />
            </div>
          </div>
          <SparklesCore
            id="tsparticlesfullpage"
            background="transparent"
            minSize={0.4}
            maxSize={0.8}
            particleDensity={40}
            className="w-full h-full"
            particleColor="#ffffff"
          />
        </div>

        <Navbar />

        <div className="relative container mx-auto px-4">
          <section className="relative h-screen flex items-center justify-center">
            <div className="max-w-4xl mx-auto text-center space-y-10">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/5 backdrop-blur-sm border border-white/10"
              >
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-200 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-indigo-100"></span>
                </span>
                <span className="text-sm text-indigo-50 tracking-wide">Non-Medical Opportunities</span>
              </motion.div>

              {/* Main Heading */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-4"
              >
                <h1 className="text-6xl sm:text-7xl md:text-8xl font-light text-white tracking-tight">
                  Pursue Your
                  <span className="block mt-2 font-normal bg-gradient-to-r from-indigo-100 via-white to-indigo-100 bg-clip-text text-transparent">
                    Passions
                  </span>
                </h1>
                <p className="text-xl text-indigo-100/80 font-light max-w-2xl mx-auto leading-relaxed">
                  Discover and engage in meaningful extracurricular activities that align with your interests and career goals.
                </p>
              </motion.div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="pt-6"
              >
                <button
                  onClick={scrollToOpportunitiesSection}
                  className="group inline-flex items-center gap-2 px-6 py-3 text-sm sm:text-base text-white border border-white/10 rounded-full hover:bg-white/5 transition-all duration-300 backdrop-blur-sm bg-white/20"
                >
                  Explore Opportunities
                  <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-0.5 transition-transform" />
                </button>
              </motion.div>
            </div>

            {/* Scroll Indicator */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 1 }}
              className="absolute bottom-12 left-1/2 -translate-x-1/2"
            >
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <ChevronsDown className="w-6 h-6 text-white/30" />
              </motion.div>
            </motion.div>
          </section>
        </div>
      </div>

      {/* Benefits Section */}
      {/* maybe add this later */}
      {/* <div className="bg-white py-24 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-indigo-600 text-lg mb-2"
            >
              Benefits
            </motion.p>
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-4xl font-light text-gray-900 mb-4"
            >
              Enhance Your <span className="font-normal">Academic Experience</span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="text-gray-600 max-w-2xl mx-auto"
            >
              Engage in activities that complement your academic journey and prepare you for future success.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.2 }}
                className="bg-indigo-50 rounded-xl p-8 shadow-sm"
              >
                <div className="w-12 h-12 mb-6 text-indigo-600">{benefit.icon}</div>
                <h3 className="text-xl font-medium text-gray-900 mb-3">{benefit.title}</h3>
                <p className="text-gray-600 font-light">{benefit.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </div> */}

      {/* Main Content */}
      <main ref={opportunitiesSectionRef} className="relative bg-gray-50 py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-screen-xl mx-auto">
          {/* Page Header */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-extralight text-indigo-800 mb-4 md:mb-0">
              Non-Medical <span className="font-light">Opportunities</span>
            </h2>
          </div>

          {/* Search and Filter UI */}
          <div className="mb-12">
            {/* Search Bar  */}
            <div className="mb-6">
              <div className="flex items-center relative">
                <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-indigo-300 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search opportunities..."
                  className="w-full pl-12 pr-4 py-3 rounded-full border border-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-400 bg-indigo-50/50 text-indigo-800 font-light"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Filter System */}
            <div className="flex flex-wrap items-center gap-2 mb-4">
              {/* Filter Dropdowns */}
              {Object.entries(filterConfig).map(([key, config]) => (
                <div key={key} className="relative">
                  <button
                    onClick={() => toggleFilter(key)}
                    className={`px-5 py-2 rounded-full text-sm font-light transition-colors flex items-center ${
                      filters[key as keyof typeof filters] !== "Any"
                        ? 'bg-indigo-500 text-white'
                        : 'bg-indigo-50 text-indigo-800 hover:bg-indigo-100'
                    }`}
                  >
                    {config.label}
                    {filters[key as keyof typeof filters] !== "Any" && (
                      <span className="ml-2 bg-white text-indigo-500 rounded-full w-5 h-5 inline-flex items-center justify-center text-xs">
                        1
                      </span>
                    )}
                    <ChevronDown className="w-4 h-4 ml-1" />
                  </button>

                  {activeFilter === key && (
                    <div className="absolute z-10 mt-1 bg-white rounded-lg shadow-lg py-2 w-48 max-h-60 overflow-y-auto border border-indigo-100">
                      {config.options.map((option) => (
                        <div
                          key={option}
                          className="px-4 py-2 hover:bg-indigo-50 cursor-pointer flex items-center"
                          onClick={() => handleFilterChange(key as keyof FilterState, option)}
                        >
                          <div className={`w-4 h-4 rounded ${
                            filters[key as keyof typeof filters] === option
                              ? 'bg-indigo-500'
                              : 'border border-indigo-300'
                          } mr-3`}></div>
                          <span className="font-light">{option}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}

              {/* Clear Filters Button */}
              {countAppliedFilters() > 0 && (
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 rounded-full text-sm font-light border border-red-100 text-red-500 hover:bg-red-50 transition-colors ml-auto"
                >
                  Clear all ({countAppliedFilters()})
                </button>
              )}
            </div>
          </div>

          {/* Subscription Limits Alert */}
          {isAuthenticated && (
            <div className="mb-6">
              <ExtracurricularLimits type="nonmedECs" />
            </div>
          )}

          {/* Results Count */}
          <div className="mb-8 text-sm font-light text-indigo-600">
            {filteredOpportunities.length} opportunities found
          </div>

          {/* Opportunities Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {opportunities.length === 0 ? (
              <div className="text-center py-16">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
                <p className="text-lg font-light text-indigo-500">Loading opportunities...</p>
              </div>
            ) : filteredOpportunities.length === 0 ? (
              <div className="text-center py-16">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mx-auto mb-4"></div>
                <p className="text-lg font-light text-indigo-500">Processing results...</p>
              </div>
            ) : displayedOpportunities.length > 0 ? (
              displayedOpportunities.map((opp) => (
                <OpportunityCard key={opp.id} opportunity={opp} />
              ))
            ) : (
              <div className="text-center py-16">
                <p className="text-lg font-light text-indigo-500">No opportunities match your current filters.</p>
                <button
                  onClick={clearFilters}
                  className="mt-4 text-blue-600 underline"
                >
                  Clear all filters
                </button>
              </div>
            )}

            {/* View Count Message */}
            {filteredOpportunities.length > 0 && (
              <div className="text-center text-blue-400 font-extralight pt-6 pb-4">
                Showing {displayedOpportunities.length} of {filteredOpportunities.length} opportunities
              </div>
            )}
          </motion.div>

          {((!isAuthenticated && filteredOpportunities.length > 15) ||
            (isAuthenticated && filteredOpportunities.length > getViewLimit())) && (
            <div ref={listingsEndRef} className="h-4 w-full" />
          )}

          {/* Login Overlay */}
          {showLoginOverlay && !isAuthenticated && filteredOpportunities.length > 15 && (
            <div className="fixed inset-0 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-white rounded-2xl shadow-xl max-w-md w-full p-8"
              >
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BookmarkIcon className="w-8 h-8 text-blue-500" />
                  </div>
                  <h3 className="text-2xl font-light text-blue-800">Discover More Opportunities</h3>
                  <p className="text-blue-600 mt-2 font-light">
                    Create an account to access our full database of opportunities.
                  </p>
                </div>
                <div className="flex flex-col gap-3">
                  <Link href="/auth" onClick={handleLoginClick} className="w-full">
                    <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-light py-3 px-4 rounded-full transition-colors">
                      Sign in
                    </button>
                  </Link>
                  <Link href="/auth" onClick={handleLoginClick} className="w-full">
                    <button className="w-full bg-white border border-blue-200 hover:bg-blue-50 text-blue-700 font-light py-3 px-4 rounded-full transition-colors">
                      Create account
                    </button>
                  </Link>
                  <button
                    onClick={() => setShowLoginOverlay(false)}
                    className="text-blue-400 hover:text-blue-600 text-sm font-light mt-2"
                  >
                    Continue browsing
                  </button>
                </div>
              </motion.div>
            </div>
          )}


          {/* Premium Overlay - Only shown for non-premium users */}
          {actualShowPremiumOverlay && isAuthenticated && filteredOpportunities.length > 60 && opportunities.length > 0 && displayedOpportunities.length > 0 && (

            <div className="fixed inset-0 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-white rounded-2xl shadow-xl max-w-md w-full p-8"
              >
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-indigo-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BookmarkIcon className="w-8 h-8 text-indigo-500" />
                  </div>
                  <h3 className="text-2xl font-light text-indigo-800">Unlock Premium Access</h3>
                  <p className="text-indigo-600 mt-2 font-light">
                    Upgrade to view all opportunities and access exclusive features.
                  </p>
                </div>
                <div className="flex flex-col gap-3">
                  <Link href="/pricing" className="w-full">
                    <button className="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-light py-3 px-4 rounded-full transition-colors">
                      View Plans
                    </button>
                  </Link>
                  <button
                    onClick={() => setShowPremiumOverlay(false)}
                    className="text-indigo-400 hover:text-indigo-600 text-sm font-light mt-2"
                  >
                    Continue browsing
                  </button>
                </div>
              </motion.div>
            </div>
          )}
        </div>
      </main>
      <Footer className="bg-indigo-600" />
    </div>
  )
}

export default Page
