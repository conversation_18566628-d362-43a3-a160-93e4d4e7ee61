// layout.tsx
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Script from "next/script";
import { AuthProvider } from '@/context/AuthContext';
import { Toaster } from "@/components/ui/toaster";
import AmbassadorGuard from '@/components/AmbassadorGuard';

// Initialize Rubik font with desired weights
const rubik = Rubik({
  subsets: ["latin"],
  weight: ['400', '500', '700'],
});

export const metadata: Metadata = {
  title: "Klinn",
  description: "A student-led EdTech startup making the healthcare field more accessible for aspiring students",
  keywords: "klinnworks, klinn, klinn works, admissions consulting, college admissions consulting, college admissions counseling, medical school admissions consulting, medical school, college, university, healthcare, extracurriculars, healthcare opportunities, volunteering, clinical experience, clinical experience search, extracurricular search, healthcare extracurriculars, med extracurriculars",
  icons: {
    icon: '/favicon.ico',
    apple: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta name="google-site-verification" content="hU-Xjbr_H89ZZuQxys2Xcbty3-kYHs_FAdZy4JirBcM" />
        <Script
          src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
          strategy="afterInteractive"
        />
        <Script id="gtag-init" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
          `}
        </Script>
      </head>
      <body className={rubik.className}>

        <AuthProvider>
          {/* <AmbassadorGuard> */}


            {children}

            <Toaster />
          {/* </AmbassadorGuard> */}

        </AuthProvider>


      </body>
    </html>
  );
}
