import React, { useState } from 'react';
import { Search } from 'lucide-react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';

interface ClientFormProps {
  onSearch: (zipcode: string) => void;
}

const ClientForm: React.FC<ClientFormProps> = ({ onSearch }) => {
  const [zipcode, setZipcode] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(zipcode);
  };

  return (
    <motion.form 
      onSubmit={handleSubmit}
      className="space-y-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative">
        <input
          type="text"
          value={zipcode}
          onChange={(e) => setZipcode(e.target.value)}
          placeholder="Enter ZIP code"
          className="w-full p-3 pl-10 font-thin border-2 border-blue-300 rounded-lg text-gray-800 bg-white bg-opacity-90 placeholder-gray-500 focus:outline-none focus:border-blue-500 transition duration-300"
          required
        />
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
      </div>
      <Button 
        type="submit" 
        className="w-full bg-blue-600 font-thin text-white p-3 rounded-lg hover:bg-blue-700 transition duration-300 flex items-center justify-center space-x-2"
      >
        <Search size={20} />
        <span>Search Clinics</span>
      </Button>
    </motion.form>
  );
};

export default ClientForm;