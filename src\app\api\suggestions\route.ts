import { Groq } from 'groq-sdk';
import { NextRequest, NextResponse } from "next/server";
import { getUserSubscription, hasAccessToFeature, getFeatureLimit } from "@/lib/subscription";
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
// import { getUserSubscription, hasAccessToFeature, getFeatureLimit } from "@/lib/subscription";
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

const groq = new Groq({
  apiKey: process.env.GROQ_KLINN_LIVE_API_KEY,
});

interface Contact {
  name: string;
  institution: string;
  topics?: string[];
  papers?: { title: string; year: string }[];
}

interface Suggestions {
  selectedContact: Contact;
  workType: string;
}

export async function POST(req: NextRequest) {
  try {
    const body: Suggestions = await req.json();
    const { selectedContact, workType } = body;

    // Create Supabase client for route handlers
    const supabase = createRouteHandlerClient({ cookies });

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error getting user:', userError);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = user.id;

    // Get user's profile information for context
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
    }

    // Parse topics and papers from selectedContact
    const topics = Array.isArray(selectedContact.topics) ? selectedContact.topics.slice(0, 5).join(', ') : 'Various research areas';
    const recentPapers = selectedContact.papers && Array.isArray(selectedContact.papers) ? 
      selectedContact.papers
        .filter(paper => paper.title) // Filter out papers with null titles
        .slice(0, 3)
        .map(paper => `- ${paper.title} (${paper.year})`)
        .join('\n') : 
      'No recent publications available';

    // Create contextual prompt for Groq
    const prompt = `Generate 8-10 medium sized contextual email suggestions for a student reaching out to a professor. Each suggestion should be a single sentence that could be used in an email. Use punctuation and write full sentences.

Context:
- Professor: ${selectedContact.name} at ${selectedContact.institution}
- Professor's research topics: ${topics}
- Work type: ${workType}
- Student background: ${userProfile?.bio || 'Undergraduate student interested in research'}

Recent professor publications:
${recentPapers}

Requirements:
- Make suggestions specific to the professor's research area (especially Support Vector Machines, Statistical Learning Theory, Machine Learning)
- Include the work type context (${workType})
- Vary between opening lines, research interest statements, and closing remarks
- Keep each suggestion under 20 words
- Make them sound natural and professional
- Include specific research topics when relevant
- Reference specific papers or methodologies when appropriate
- Avoid generic phrases like "I am interested in your work"
- Don't make every suggestion about the research and topics the professor is working on, but do include them when relevant
- Make sure the suggestions are diverse and not repetitive

Return only the suggestions as a JSON array of strings, no additional text.`;

    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      model: "llama-3.3-70b-versatile",
      temperature: 0.7,
      max_tokens: 800
    });

    const response = completion.choices[0]?.message?.content;
    
    if (!response) {
      return NextResponse.json({ error: "Failed to generate suggestions" }, { status: 500 });
    }

    // Parse the response to extract suggestions
    let suggestions: string[] = [];
    try {
      // Try to parse as JSON array first
      suggestions = JSON.parse(response);
    } catch {
      // If JSON parsing fails, split by lines and clean up
      suggestions = response
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('[') && !line.startsWith(']'))
        .map(line => line.replace(/^[-•*]\s*/, '').replace(/^"\s*/, '').replace(/\s*"$/, ''))
        .filter(line => line.length > 10); // Filter out very short lines
    }

    console.log('Generated suggestions:', suggestions);

    return NextResponse.json({ 
      suggestions,
      contact: {
        name: selectedContact.name,
        institution: selectedContact.institution,
        topics: Array.isArray(selectedContact.topics) ? selectedContact.topics.slice(0, 3) : []
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Error in POST request:', error);
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}