import {
    <PERSON>,
    <PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
  } from "@/components/ui/card";
  import { Progress } from "@/components/ui/progress";
  import { Badge } from "@/components/ui/badge";
  import {
    Users,
    Award,
    LineChart,
    Bar<PERSON>hart3,
    Target,
    Mail,
    Clock,
    Calendar,
  } from "lucide-react";
  import { useEffect, useState } from "react";
  import { supabase } from "../../supabase/supabaseClient";
  import { useAuth } from "@/context/AuthContext";
  
  /* ------------------------------------------------------------------ */
  /* Types                                                               */
  /* ------------------------------------------------------------------ */
  interface StatsContentProps {
    profileData: any;
    resumes?: any[];
    bookmarkedOpportunities: any[];
    jobPostings: any[];
    emailsSent: number;            // total successful e-mails
    scheduledEmailsSent: number;   // successful + scheduled
    clinicsBookmarked: number;        // ← NEW
  }

  interface ScheduledEmail {
    id: string;
    to_addresses: string[];
    subject: string;
    send_at: string;
    status: 'pending' | 'processing' | 'sent' | 'failed';
    sent_at?: string;
    error?: string;
  }

  interface DailyEmailCount {
    date: string;
    count: number;
  }
  
  /* ------------------------------------------------------------------ */
  /* Component                                                           */
  /* ------------------------------------------------------------------ */
  export default function StatsContent({
    profileData,
    resumes,
    bookmarkedOpportunities,
    jobPostings,
    emailsSent,
    scheduledEmailsSent,
    clinicsBookmarked
  }: StatsContentProps) {

     /* 🛑  early-out while anything is still “undefined”  */
  const propsReady =
  typeof emailsSent === "number" &&
  typeof scheduledEmailsSent === "number" &&
  typeof clinicsBookmarked === "number";

const { user } = useAuth();
const [scheduledEmails, setScheduledEmails] = useState<ScheduledEmail[]>([]);
const [isLoadingScheduled, setIsLoadingScheduled] = useState(true);
const [dailyEmailCounts, setDailyEmailCounts] = useState<DailyEmailCount[]>([]);
const [isLoadingEmails, setIsLoadingEmails] = useState(true);

useEffect(() => {
      async function fetchScheduledEmails() {
        if (!user) return;
        
        try {
          const { data, error } = await supabase
            .from('scheduled_emails')
            .select('*')
            .eq('user_id', user.id)
            .order('send_at', { ascending: true });

          if (error) throw error;
          setScheduledEmails(data || []);
        } catch (err) {
          console.error('Error fetching scheduled emails:', err);
        } finally {
          setIsLoadingScheduled(false);
        }
      }

      fetchScheduledEmails();
    }, [user]);

    useEffect(() => {
      async function fetchDailyEmailCounts() {
        if (!user) return;
        
        try {
          // Get the start of the week (7 days ago)
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - 6);
          startDate.setHours(0, 0, 0, 0);

          // Fetch emails sent in the last 7 days
          const { data: emailLogs, error } = await supabase
            .from('email_logs')
            .select('sent_at')
            .eq('user_id', user.id)
            .gte('sent_at', startDate.toISOString());

          if (error) throw error;

          // Initialize array for the last 7 days
          const counts: DailyEmailCount[] = Array.from({ length: 7 }, (_, i) => {
            const date = new Date(startDate);
            date.setDate(date.getDate() + i);
            return {
              date: date.toISOString().split('T')[0],
              count: 0
            };
          });

          // Count emails for each day
          emailLogs?.forEach(log => {
            const date = new Date(log.sent_at).toISOString().split('T')[0];
            const index = counts.findIndex(d => d.date === date);
            if (index !== -1) {
              counts[index].count++;
            }
          });

          setDailyEmailCounts(counts);
        } catch (err) {
          console.error('Error fetching daily email counts:', err);
        } finally {
          setIsLoadingEmails(false);
        }
      }

      fetchDailyEmailCounts();
    }, [user]);

if (!propsReady) {
  return (
    <div className="flex justify-center py-10 text-gray-500">
      Loading&nbsp;stats…
    </div>
  );
}

    /* ---------- derived stats ---------- */
    const totalResumes = resumes?.length ?? 0;
    const totalBookmarks = clinicsBookmarked;        // ← use number from RPC
    const totalJobs = jobPostings?.length ?? 0;
  
    const memberSince = profileData?.created_at
      ? new Date(profileData.created_at)
      : null;
    const daysAsMember = memberSince
      ? Math.floor((Date.now() - memberSince.getTime()) / 86400000)
      : 0;
  
    /* ---------- JSX ---------- */
    return (
      <div className="space-y-6">
        {/* Header badges */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Profile Statistics</h2>
            <p className="text-gray-500 mt-1">
              Track your profile activity and engagement
            </p>
          </div>
  
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="bg-blue-50 border-blue-200 text-blue-700 px-3 py-1"
            >
              <Users className="h-3.5 w-3.5 mr-1.5" />
              <span>{daysAsMember} Days Active</span>
            </Badge>
            <Badge
              variant="outline"
              className="bg-green-50 border-green-200 text-green-700 px-3 py-1"
            >
              <Award className="h-3.5 w-3.5 mr-1.5" />
              <span>{profileData?.tokens ?? 0} Tokens</span>
            </Badge>
          </div>
        </div>
  
        {/* Overview + Activity */}
        <div className="grid md:grid-cols-3 gap-6">
          {/* Profile Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                <span>Profile Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <StatRow label="Resumes" value={totalResumes} color="blue" />
                <StatRow label="Bookmarks" value={totalBookmarks} color="green" />
                <StatRow label="Job Postings" value={totalJobs} color="yellow" />
                {/* NEW rows */}
                <StatRow
                  label="Emails Sent"
                  value={emailsSent}
                  color="indigo"
                  Icon={Mail}
                />
                <StatRow
                  label="Scheduled Emails"
                  value={scheduledEmailsSent}
                  color="purple"
                  Icon={Clock}
                />
                {/* Member-since row */}
                <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                  <span className="font-medium">Member Since</span>
                  <span className="text-sm font-bold">
                    {memberSince ? memberSince.toLocaleDateString() : "N/A"}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
  
          {/* Weekly activity chart */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5 text-blue-500" />
                <span>Weekly Email Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingEmails ? (
                <div className="text-center py-4 text-gray-500">Loading email data...</div>
              ) : (
                <div className="h-[220px] flex items-end justify-between gap-2">
                  {dailyEmailCounts.map((day, i) => (
                    <div key={i} className="relative flex flex-col items-center flex-1">
                      <div
                        className="w-full bg-blue-500 rounded-t-md transition-all duration-500"
                        style={{
                          height: `${Math.max(day.count * 20, 4)}px`,
                          opacity: day.count > 0 ? 1 : 0.3,
                        }}
                      />
                      <div className="absolute -top-6 text-sm font-medium text-blue-600">
                        {day.count}
                      </div>
                      <div className="mt-2 text-xs text-gray-500">
                        {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Scheduled Emails Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-500" />
              <span>Scheduled Emails</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingScheduled ? (
              <div className="text-center py-4 text-gray-500">Loading scheduled emails...</div>
            ) : scheduledEmails.length === 0 ? (
              <div className="text-center py-4 text-gray-500">No scheduled emails</div>
            ) : (
              <div className="space-y-4">
                {scheduledEmails.map((email) => (
                  <div
                    key={email.id}
                    className="p-4 rounded-lg border border-gray-200 hover:border-blue-200 transition-colors"
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-gray-900">{email.subject}</h4>
                        <p className="text-sm text-gray-500">
                          To: {email.to_addresses.join(", ")}
                        </p>
                      </div>
                      <Badge
                        variant="outline"
                        className={`${
                          email.status === "sent"
                            ? "bg-green-50 border-green-200 text-green-700"
                            : email.status === "failed"
                            ? "bg-red-50 border-red-200 text-red-700"
                            : "bg-blue-50 border-blue-200 text-blue-700"
                        }`}
                      >
                        {email.status.charAt(0).toUpperCase() + email.status.slice(1)}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>
                          {new Date(email.send_at).toLocaleString()}
                        </span>
                      </div>
                      {email.sent_at && (
                        <span>
                          Sent: {new Date(email.sent_at).toLocaleString()}
                        </span>
                      )}
                    </div>
                    {email.error && (
                      <div className="mt-2 text-sm text-red-600">
                        Error: {email.error}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
  
        {/* Goals card – unchanged */}
        <GoalsCard
          resumes={totalResumes}
          bookmarks={totalBookmarks}
          jobs={totalJobs}
        />
      </div>
    );
  }
  
  /* ------------------------------------------------------------------ */
  /* Helper components                                                   */
  /* ------------------------------------------------------------------ */
  
  /* Re-usable stat row */
  function StatRow({
    label,
    value,
    color,
    Icon,
  }: {
    label: string;
    value: number;
    color: "blue" | "green" | "yellow" | "indigo" | "purple";
    Icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  }) {
    const colorMap = {
      blue: "bg-blue-50 text-blue-500",
      green: "bg-green-50 text-green-500",
      yellow: "bg-yellow-50 text-yellow-500",
      indigo: "bg-indigo-50 text-indigo-500",
      purple: "bg-purple-50 text-purple-500",
    }[color];
  
    return (
      <div
        className={`flex justify-between items-center p-3 rounded-lg ${colorMap}`}
      >
        <span className="font-medium flex items-center gap-1">
          {Icon && <Icon className="h-4 w-4" />}
          {label}
        </span>
        <span className="text-xl font-bold">{value}</span>
      </div>
    );
  }
  
  /* Goals progress card */
  function GoalsCard({
    resumes,
    bookmarks,
    jobs,
  }: {
    resumes: number;
    bookmarks: number;
    jobs: number;
  }) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-500" />
            <span>Profile Goals</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <GoalRow label="Resume Goal (3)" done={resumes} total={3} />
          <GoalRow label="Bookmarks Goal (10)" done={bookmarks} total={10} />
          <GoalRow label="Job Postings Goal (5)" done={jobs} total={5} />
        </CardContent>
      </Card>
    );
  }
  
  function GoalRow({
    label,
    done,
    total,
  }: {
    label: string;
    done: number;
    total: number;
  }) {
    return (
      <div>
        <div className="flex justify-between mb-2">
          <span className="text-sm font-medium">{label}</span>
          <span className="text-sm text-gray-500">
            {done}/{total}
          </span>
        </div>
        <Progress value={(done / total) * 100} className="h-2" />
      </div>
    );
  }
  
  /* Monthly trends chart */
  function MonthlyCard({ data }: { data: number[] }) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-500" />
            <span>Monthly Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[220px] flex items-end justify-center gap-12">
            {data.map((count, i) => (
              <div key={i} className="relative flex flex-col items-center">
                <div
                  className="w-16 bg-blue-500 rounded-t-md transition-all duration-500"
                  style={{ height: `${Math.max(count * 5, 4)}px` }}
                />
                <div className="absolute -top-6 text-sm font-medium text-blue-600">
                  {count}
                </div>
                <div className="mt-2 text-sm font-medium text-gray-700">
                  {["January", "February", "March"][i]}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }
  