"use client";

import React, { ChangeEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Search, GraduationCap, Rocket, Building } from "lucide-react";

export interface InterestOption {
  id: string;
  label: string;
}

export interface FilterPanelProps {
  workType: "research" | "startup" | "clinic";
  setWorkType: (value: "research" | "startup" | "clinic") => void;
  interests: string[];
  setInterests: React.Dispatch<React.SetStateAction<string[]>>;
  customInterest: string;
  setCustomInterest: (value: string) => void;
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  interestOptions: InterestOption[];
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  workType,
  setWorkType,
  interests,
  setInterests,
  customInterest,
  setCustomInterest,
  searchQuery,
  setSearchQuery,
  interestOptions,
}) => {
  const toggleInterest = (interestId: string) => {
    setInterests((prev: string[]) =>
      prev.includes(interestId)
        ? prev.filter((i: string) => i !== interestId)
        : [...prev, interestId]
    );
  };

  const addCustomInterest = () => {
    const trimmed = customInterest.trim().toLowerCase();
    if (trimmed && !interests.includes(trimmed)) {
      setInterests([...interests, trimmed]);
      setCustomInterest("");
    }
  };

  return (
    <div className="card p-4 border rounded-md space-y-4">
      <div className="grid md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="work-type">Opportunity Type</Label>
          <Select value={workType} onValueChange={(v) => setWorkType(v as "research" | "startup" | "clinic")}>
            <SelectTrigger id="work-type" className="mt-1.5">
              <SelectValue placeholder="Select opportunity type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="research">
                <div className="flex items-center gap-2">
                  <GraduationCap className="h-4 w-4 text-blue-500" />
                  <span>Research</span>
                </div>
              </SelectItem>
              <SelectItem value="startup">
                <div className="flex items-center gap-2">
                  <Rocket className="h-4 w-4 text-purple-500" />
                  <span>Startups</span>
                </div>
              </SelectItem>
              <SelectItem value="clinic">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-red-500" />
                  <span>Clinics</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="search">Search</Label>
          <div className="relative mt-1.5">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="search"
              placeholder="Search..."
              className="pl-9"
              value={searchQuery}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="custom-interest">Add Custom Interest</Label>
          <div className="flex mt-1.5 gap-2">
            <Input
              id="custom-interest"
              placeholder="Type your interest..."
              value={customInterest}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setCustomInterest(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  addCustomInterest();
                }
              }}
            />
            <Button size="sm" onClick={addCustomInterest} disabled={!customInterest.trim()}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div>
        <Label className="mb-2 block">Your Interests</Label>
        <div className="flex flex-wrap gap-2">
          {interestOptions.map((interest) => (
            <button
              key={interest.id}
              type="button"
              className={`px-2 py-1 rounded-full text-sm border cursor-pointer ${
                interests.includes(interest.id)
                  ? "bg-blue-100 text-blue-800"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
              onClick={() => toggleInterest(interest.id)}
            >
              {interest.label}
            </button>
          ))}
          {interests
            .filter((i) => !interestOptions.some((o) => o.id === i))
            .map((i) => (
              <button
                key={i}
                type="button"
                className="px-2 py-1 rounded-full text-sm bg-green-100 text-green-800 hover:bg-green-200 flex items-center gap-1"
                onClick={() => setInterests(interests.filter((int) => int !== i))}
              >
                {i} <span>&times;</span>
              </button>
            ))}
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;
