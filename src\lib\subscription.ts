import { createClient } from '@supabase/supabase-js';

export type SubscriptionPlan = 'free' | 'basic_monthly' | 'basic_yearly' | 'premium_monthly' | 'premium_yearly';

// Map of price IDs to subscription plans
export const PRICE_ID_TO_PLAN: Record<string, SubscriptionPlan> = {
  "price_1OvXXXXXXXXXXXXXXXXXXXXX": "basic_monthly",
  "price_2OvXXXXXXXXXXXXXXXXXXXXX": "basic_yearly",
  "price_3OvXXXXXXXXXXXXXXXXXXXXX": "premium_monthly",
  "price_4OvXXXXXXXXXXXXXXXXXXXXX": "premium_yearly",
};

// Map of subscription plans to price IDs
export const PLAN_TO_PRICE_ID: Record<SubscriptionPlan, string> = {
  "basic_monthly": "price_1OvXXXXXXXXXXXXXXXXXXXXX",
  "basic_yearly": "price_2OvXXXXXXXXXXXXXXXXXXXXX",
  "premium_monthly": "price_3OvXXXXXXXXXXXXXXXXXXXXX",
  "premium_yearly": "price_4OvXXXXXXXXXXXXXXXXXXXXX",
  "free": "",
};

export interface Subscription {
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  planType: SubscriptionPlan;
  status?: string;
  currentPeriodEnd?: Date;
}

// Helper function to create Supabase client with appropriate key
const getSupabaseClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  // Use service role key for server operations, anon key as fallback
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

export async function updateUserSubscription(
  userId: string,
  subscription: Subscription
) {
  if (!userId) {
    throw new Error('User ID is required to update subscription');
  }

  try {
    const supabase = getSupabaseClient();

    const { error } = await supabase
      .from('profiles')
      .update({
        stripe_customer_id: subscription.stripeCustomerId,
        stripe_subscription_id: subscription.stripeSubscriptionId,
        plan_type: subscription.planType,
        subscription_status: subscription.status,
        subscription_end_date: subscription.currentPeriodEnd?.toISOString(),
      })
      .eq('id', userId);

    if (error) throw error;

    return true;
  } catch (error) {
    console.error('Error updating user subscription:', error);
    throw error;
  }
}

export async function getUserSubscription(userId: string): Promise<Subscription | null> {
  if (!userId) {
    return null;
  }

  try {
    const supabase = getSupabaseClient();

    const { data, error } = await supabase
      .from('profiles')
      .select('stripe_customer_id, stripe_subscription_id, plan_type, subscription_status, subscription_end_date')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Supabase error getting subscription:', error);
      return null;
    }

    if (!data) return null;

    return {
      stripeCustomerId: data.stripe_customer_id || undefined,
      stripeSubscriptionId: data.stripe_subscription_id || undefined,
      planType: (data.plan_type as SubscriptionPlan) || 'free',
      status: data.subscription_status || undefined,
      currentPeriodEnd: data.subscription_end_date ? new Date(data.subscription_end_date) : undefined,
    };
  } catch (error) {
    console.error('Error getting user subscription:', error);
    return null;
  }
}

export function hasAccessToFeature(
  subscription: Subscription | null,
  feature: string
): boolean {
  if (!subscription) return false;

  const accessRules = {
    free: {
      coldCallScripts: 1,
      tokens: 5,
      nonmedECs: 30,
      medicalECs: 30,
      jobApplications: 5,
      aiResumeScorer: 0,
      priorityApplicant: false,
      customProfileBanner: false,
      earlyAccessJobs: 0,
      aiJobMatching: false,
      automatedEmails: 3,
      maxEmails: 100,
      newsletter: false,
    },
    basic_monthly: {
      coldCallScripts: 3,
      tokens: 15,
      nonmedECs: 60,
      medicalECs: 60,
      jobApplications: 30,
      aiResumeScorer: 2,
      priorityApplicant: false,
      customProfileBanner: true,
      earlyAccessJobs: 24,
      aiJobMatching: false,
      automatedEmails: 15,
      maxEmails: 150,
      newsletter: true,
    },
    basic_yearly: {
      coldCallScripts: 3,
      tokens: 15,
      nonmedECs: 60,
      medicalECs: 60,
      jobApplications: 30,
      aiResumeScorer: 2,
      priorityApplicant: false,
      customProfileBanner: true,
      earlyAccessJobs: 24,
      aiJobMatching: false,
      automatedEmails: 15,
      maxEmails: 150,
      newsletter: true,
    },
    premium_monthly: {
      coldCallScripts: 10,
      tokens: 50,
      nonmedECs: 999999,
      medicalECs: 999999,
      jobApplications: 999999,
      aiResumeScorer: 5,
      priorityApplicant: true,
      customProfileBanner: true,
      earlyAccessJobs: 48,
      aiJobMatching: true,
      automatedEmails: 25,
      maxEmails: 200,
      newsletter: true,
    },
    premium_yearly: {
      coldCallScripts: 10,
      tokens: 50,
      nonmedECs: 999999,
      medicalECs: 999999,
      jobApplications: 999999,
      aiResumeScorer: 5,
      priorityApplicant: true,
      customProfileBanner: true,
      earlyAccessJobs: 48,
      aiJobMatching: true,
      automatedEmails: 25,
      maxEmails: 200,
      newsletter: true,
    },
  };

  if (
    subscription.planType !== 'free' &&
    subscription.status !== 'active' &&
    subscription.status !== 'trialing'
  ) {
    return false;
  }

  const planRules = accessRules[subscription.planType];
  if (!planRules) return false;

  switch (feature) {
    case 'coldCallScripts':
      return planRules.coldCallScripts > 0;
    case 'tokens':
      return planRules.tokens > 0;
    case 'nonmedECs':
      return planRules.nonmedECs > 0;
    case 'medicalECs':
      return planRules.medicalECs > 0;
    case 'jobApplications':
      return planRules.jobApplications > 0;
    case 'aiResumeScorer':
      return planRules.aiResumeScorer > 0;
    case 'priorityApplicant':
      return planRules.priorityApplicant;
    case 'customProfileBanner':
      return planRules.customProfileBanner;
    case 'earlyAccessJobs':
      return planRules.earlyAccessJobs > 0;
    case 'aiJobMatching':
      return planRules.aiJobMatching;
    case 'automatedEmails':
      return planRules.automatedEmails > 0;
    case 'newsletter':
      return planRules.newsletter;
    default:
      return false;
  }
}

export function getFeatureLimit(
  subscription: Subscription | null,
  feature: string
): number {
  if (!subscription) return 0;

  const accessRules = {
    free: {
      coldCallScripts: 1,
      tokens: 5,
      nonmedECs: 30,
      medicalECs: 30,
      jobApplications: 5,
      aiResumeScorer: 0,
      earlyAccessJobs: 0,
      automatedEmails: 3,
      maxEmails: 100,
    },
    basic_monthly: {
      coldCallScripts: 3,
      tokens: 15,
      nonmedECs: 60,
      medicalECs: 60,
      jobApplications: 30,
      aiResumeScorer: 2,
      earlyAccessJobs: 24,
      automatedEmails: 15,
      maxEmails: 150,
    },
    basic_yearly: {
      coldCallScripts: 3,
      tokens: 15,
      nonmedECs: 60,
      medicalECs: 60,
      jobApplications: 30,
      aiResumeScorer: 2,
      earlyAccessJobs: 24,
      automatedEmails: 15,
      maxEmails: 150,
    },
    premium_monthly: {
      coldCallScripts: 10,
      tokens: 50,
      nonmedECs: 999999,
      medicalECs: 999999,
      jobApplications: 999999,
      aiResumeScorer: 5,
      earlyAccessJobs: 48,
      automatedEmails: 25,
      maxEmails: 200,
    },
    premium_yearly: {
      coldCallScripts: 10,
      tokens: 50,
      nonmedECs: 999999,
      medicalECs: 999999,
      jobApplications: 999999,
      aiResumeScorer: 5,
      earlyAccessJobs: 48,
      automatedEmails: 25,
      maxEmails: 200,
    },
  };

  const planRules = accessRules[subscription.planType];
  if (!planRules) return 0;

  return planRules[feature as keyof typeof planRules] || 0;
}