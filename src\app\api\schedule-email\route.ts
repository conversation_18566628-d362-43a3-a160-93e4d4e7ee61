import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

export async function POST(req: Request) {
  try {
    /*───────────────────────────────────────────────────────────
     * 1️⃣ Extract the JWT that the browser sent
     * ───────────────────────────────────────────────────────────*/
    const jwt =
      req.headers.get("authorization")?.replace("Bearer ", "") ?? "";
    if (!jwt) {
      return NextResponse.json({ error: "No JWT" }, { status: 401 });
    }

    /*───────────────────────────────────────────────────────────
     * 2️⃣ Create a per-request Supabase client that will send
     * that JWT in the Authorization header of every query
     * ───────────────────────────────────────────────────────────*/
    const supa = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: { headers: { Authorization: `Bearer ${jwt}` } }, // ← key line
      }
    );

    /*───────────────────────────────────────────────────────────
     * 3️⃣ Verify the user
     * ───────────────────────────────────────────────────────────*/
    const {
      data: { user },
    } = await supa.auth.getUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    /*───────────────────────────────────────────────────────────
     * 4️⃣ Validate request body
     * ───────────────────────────────────────────────────────────*/
    const { to, cc, bcc, subject, body, sendAt, attachments } = await req.json();
    if (!to?.length || !subject || !body || !sendAt) {
      return NextResponse.json({ error: "Bad request" }, { status: 400 });
    }

    // Additional validation
    if (!Array.isArray(to)) {
      return NextResponse.json({ error: "Recipients (to) must be an array" }, { status: 400 });
    }

    if (cc && !Array.isArray(cc)) {
      return NextResponse.json({ error: "CC recipients must be an array" }, { status: 400 });
    }

    if (bcc && !Array.isArray(bcc)) {
      return NextResponse.json({ error: "BCC recipients must be an array" }, { status: 400 });
    }

    // Validate date format
    const sendAtDate = new Date(sendAt);
    if (isNaN(sendAtDate.getTime())) {
      return NextResponse.json({ error: "Invalid date format for sendAt" }, { status: 400 });
    }

    /*───────────────────────────────────────────────────────────
     * 5️⃣ Insert the job – RLS sees auth.uid() now, so it passes
     * ───────────────────────────────────────────────────────────*/
    const { error, data } = await supa.from("scheduled_emails").insert({
      user_id: user.id, // text
      to_addresses: to, // text[]
      cc_addresses: cc || [], 
      bcc_addresses: bcc || [],
      subject,
      body,
      attachments: attachments || [],
      send_at: sendAt, // ISO string or timestamptz
      status: "pending",
    }).select();

    if (error) {
      console.error("Database insertion error:", error);
      throw new Error(`Failed to schedule email: ${error.message}`);
    }

    return NextResponse.json({ 
      ok: true, 
      id: data?.[0]?.id || null,
      message: "Email scheduled successfully" 
    });
  } catch (err: any) {
    console.error("schedule-email error:", err);
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
}
