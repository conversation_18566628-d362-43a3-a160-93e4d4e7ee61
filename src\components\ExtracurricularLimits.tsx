import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Info } from 'lucide-react';
import { Button } from './ui/button';
import { useRouter } from 'next/navigation';
import { useSubscription } from '@/hooks/useSubscription';

interface ExtracurricularLimitsProps {
  type: 'medicalECs' | 'nonmedECs';
  showAlert?: boolean;
  className?: string;
}

export function ExtracurricularLimits({ type, showAlert = true, className = '' }: ExtracurricularLimitsProps) {
  const { planType } = useSubscription();
  const router = useRouter();

  // Determine limits based on plan type
  const isPremium = planType === 'premium_monthly' || planType === 'premium_yearly';
  const isBasic = planType === 'basic_monthly' || planType === 'basic_yearly';

  // Set limits based on plan type
  const limit = isPremium ? 999999 : isBasic ? 100 : 30;
  const hasAccess = true; // All users have access to ECs
  const typeName = type === 'medicalECs' ? 'Medical Extracurriculars' : 'Non-Medical Extracurriculars';

  // Don't show alerts for premium users or if showAlert is false
  if (!showAlert || isPremium) {
    return null;
  }

  // For free and basic users, show the appropriate alert
  if (!isBasic) {
    return (
      <div className={className}>
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Limited Access</AlertTitle>
          <AlertDescription>
            You can view up to {limit} {typeName.toLowerCase()} with your current plan.
            <Button
              variant="link"
              className="p-0 h-auto font-normal"
              onClick={() => router.push('/pricing')}
            >
              Upgrade your subscription
            </Button> for unlimited access.
          </AlertDescription>
        </Alert>
      </div>
    );
  } else {
    return (
      <div className={className}>
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Basic Plan</AlertTitle>
          <AlertDescription>
            Your basic plan allows access to {limit} {typeName.toLowerCase()}.
            <Button
              variant="link"
              className="p-0 h-auto font-normal"
              onClick={() => router.push('/pricing')}
            >
              Upgrade to premium
            </Button> for unlimited access.
          </AlertDescription>
        </Alert>
      </div>
    );
  }
}
