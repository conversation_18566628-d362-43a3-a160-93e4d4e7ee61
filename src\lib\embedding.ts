// // lib/embedding.ts
// export async function embedText(text: string): Promise<number[]> {
//     // Dummy embedding: create a deterministic vector based on text.
//     // Replace with a call to your BERT model or external API.
//     const dim = 768;
//     const vector = new Array(dim).fill(0);
//     for (let i = 0; i < text.length; i++) {
//       const charCode = text.charCodeAt(i);
//       // Distribute the character code into the vector cyclically.
//       vector[i % dim] += charCode;
//     }
//     // Normalize the vector.
//     const norm = Math.sqrt(vector.reduce((sum, x) => sum + x * x, 0));
//     return vector.map((x) => x / norm);
//   }
  