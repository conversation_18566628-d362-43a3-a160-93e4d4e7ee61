"use client"

import { useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { HelpCircle, Book, MessageCircle, FileText } from "lucide-react"
import Navbar from "@/components/Navbar"
import Footer from "@/components/Footer"

export default function HelpPage() {
  useEffect(() => {
    document.title = "Klinn | Help Center"
  }, [])

  const helpCategories = [
    {
      title: "FAQs",
      description: "Browse our frequently asked questions",
      icon: <HelpCircle className="h-8 w-8" />,
      link: "/faqs",
      color: "from-blue-600 to-blue-800",
    },
    {
      title: "Contact Support",
      description: "Get in touch with our support team",
      icon: <MessageCircle className="h-8 w-8" />,
      link: "/contact",
      color: "from-blue-400 to-blue-600",
    },
    {
      title: "Guides",
      description: "Step-by-step guides to help you",
      icon: <FileText className="h-8 w-8" />,
      link: "/guides",
      color: "from-blue-300 to-blue-500",
    },
  ]

  return (
    <div className="relative flex flex-col min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 font-thin">
      <Navbar />

      <Image
        src="/images/backgroundpic.jpg"
        alt="Medical background"
        layout="fill"
        objectFit="cover"
        className="absolute inset-0 w-full h-full z-0 mix-blend-overlay opacity-30"
      />

      <section className="relative flex items-center justify-center min-h-screen text-white pt-15 lg-mt-20 mt-8">
        <div className="relative z-10 w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-5xl sm:text-6xl font-thin mb-6 leading-tight">
              How Can We <span className="text-blue-300">Help You?</span>
            </h1>
            <p className="text-xl sm:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
              Find the resources you need to make the most of Klinn&apos;s services.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 max-w-7xl mx-auto">            {helpCategories.map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Link href={category.link}>
                  <div
                    className={`bg-gradient-to-br ${category.color} hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300 rounded-xl p-6 h-full flex flex-col items-center text-center`}
                  >
                    <div className="bg-white/10 p-4 rounded-full mb-4">{category.icon}</div>
                    <h3 className="text-2xl font-medium mb-2">{category.title}</h3>
                    <p className="text-blue-100">{category.description}</p>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-16 text-center"
          >
            <h2 className="text-3xl font-thin mb-6">Still Need Help?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Our support team is always ready to assist you with any questions or issues you might have.
            </p>
            <Link href="/contact">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg transition-colors">
                Contact Us
              </button>
            </Link>
          </motion.div>
        </div>
      </section>
      <Footer className="bg-transparent text-white" />
    </div>
  )
}

