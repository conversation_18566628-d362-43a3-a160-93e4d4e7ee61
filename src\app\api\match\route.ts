// app/api/match/route.ts
import { NextResponse } from 'next/server'
import { createRouteClient } from '../../../../supabase/serversupabaseClient'

export async function POST(request: Request) {
  // 1) get your Supabase client & user
  const supabase = createRouteClient()
  const {
    data: { user }
  } = await supabase.auth.getUser()
  if (!user) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
  }

  // 2) pull p_limit out of the request body
  const { p_limit } = await request.json()
  console.log("→ [match] got p_limit=", p_limit)

  // 3) fetch the profile embedding
  const { data: profile, error: profErr } = await supabase
    .from('profiles')
    .select('resume_embedding')
    .eq('id', user.id)
    .maybeSingle()
  if (profErr || !profile?.resume_embedding) {
    return NextResponse.json({ error: 'No embedding yet' }, { status: 400 })
  }

  // 4) call your two RPCs, now truly honoring p_limit
  const [
    { data: startups, error: startErr },
    { data: professors, error: profErrs }
  ] = await Promise.all([
    supabase.rpc('match_startups', {
      resume_embedding: profile.resume_embedding,
      p_limit: p_limit ?? 50
    }),
    supabase.rpc('match_professors', {
      resume_embedding: profile.resume_embedding,
      p_limit: p_limit ?? 50
    }),
  ])
    console.log("→ [match] returning", startups?.length, "startups and", professors?.length, "professors")

  if (startErr || profErrs) {
    console.error(startErr, profErrs)
    return NextResponse.json({ error: 'Match query failed' }, { status: 500 })
  }

  return NextResponse.json({ startups, professors })
}
