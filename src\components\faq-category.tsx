"use client"

import type React from "react"
import { useState } from "react"
import { motion } from "framer-motion"
import { HelpCircle, ChevronDown, ChevronUp } from "lucide-react"

interface FAQCategoryProps {
  title: string
  children: React.ReactNode
}

export default function FAQCategory({ title, children }: FAQCategoryProps) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="mb-6">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
        className="bg-blue-800/50 rounded-lg overflow-hidden"
      >
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between p-4 text-xl font-medium text-white hover:bg-blue-700/50 transition-colors"
          aria-expanded={isOpen}
        >
          <div className="flex items-center">
            <HelpCircle className="mr-2 h-5 w-5 text-blue-300" />
            <span>{title}</span>
          </div>
          {isOpen ? <ChevronUp className="h-5 w-5 text-blue-300" /> : <ChevronDown className="h-5 w-5 text-blue-300" />}
        </button>

        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="px-4 py-2"
          >
            {children}
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}

