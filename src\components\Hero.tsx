import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronRight, Sparkles } from 'lucide-react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

const words = ['Nursing', 'Medicine', 'Healthcare', 'Research', 'Biology', 'Chemistry'];

const Hero = () => {
  const [mounted, setMounted] = useState(false);
  const [currentWord, setCurrentWord] = useState(0);

  useEffect(() => {
    setMounted(true);

    const interval = setInterval(() => {
      setCurrentWord((prev) => (prev + 1) % words.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-blue-300 to-blue-50">
        <div className="absolute inset-0">
          <div className="absolute inset-0 opacity-30 mix-blend-soft-light">
            <svg width="100%" height="100%" className="absolute inset-0">
              <filter id="noise">
                <feTurbulence type="fractalNoise" baseFrequency="0.65" numOctaves="3" stitchTiles="stitch" />
                <feColorMatrix type="matrix" values="1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.5 0" />
              </filter>
              <rect width="100%" height="100%" filter="url(#noise)" />
            </svg>
          </div>

          <div className="absolute w-96 h-96 -left-48 top-1/4 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
          <div className="absolute w-96 h-96 right-1/4 bottom-1/4 bg-cyan-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse" />
          <div className="absolute w-64 h-64 right-1/3 top-1/3 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-10">
            <div className="w-full h-full animate-float" />
          </div>
        </div>
      </div>

      <div className="relative z-10 px-4 max-w-3xl">
        <div className={`text-center transition-all duration-1000 ${mounted ? 'opacity-100' : 'opacity-0'}`}>
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center gap-2 px-3 py-1 mb-8 bg-white/60 backdrop-blur-sm rounded-full shadow-sm"
          >
            <Sparkles className="w-3 h-3 text-blue-500" />
            <span className="text-xs font-light tracking-wide text-blue-900">
              Transform Your Future in Healthcare
            </span>
          </motion.div>

          <h1 className="text-4xl md:text-6xl font-extralight text-blue-900 tracking-tight">
            Your Gateway to a Career in{' '}
            <span className="relative inline-block h-16">
            <div className="-mt-10 md:-mt-3">
              <span className="absolute bottom-3 md:bottom-0 left-0 right-0 h-2 bg-yellow-300/30 rounded-sm"></span>

              <span className="relative inline-block h-20">
                <AnimatePresence mode="wait">
                  <motion.span
                    key={words[currentWord]}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: -20, opacity: 0 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="text-blue-600 font-normal block md:pt-3 pt-12 pb-3"
                  >
                    {words[currentWord]}
                  </motion.span>
                </AnimatePresence>
              </span>
            </div>
            </span>
          </h1>

          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-base text-blue-800/70 mb-8 max-w-lg mx-auto font-extralight tracking-wide md:pt-8"
          >
            Empowering the next generation of healthcare professionals through innovative education and clinical experience.
          </motion.p>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-10"
          >
            <Link href="/clinic-search">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white px-5 py-5 rounded-md text-xs font-light tracking-wide transition-all shadow-sm -mt-5 md:mt-0">
                <span className="flex items-center">
                  Find Clinical Placements
                  <ChevronRight className="ml-1 w-3 h-3" />
                </span>
              </Button>
            </Link>
            <Link href="/extracurriculars">
              <Button variant="outline" className="bg-white/5 hover:bg-white/10 text-blue-900 px-5 py-5 rounded-md text-xs font-light tracking-wide transition-all border border-blue-100">
                Explore Programs
              </Button>
            </Link>
          </motion.div>
        </div>
      </div>

      <div className="absolute bottom-0 left-0 right-0 h-64 bg-gradient-to-t from-blue-200 via-blue-250/80 to-transparent pointer-events-none" />
      <div className="absolute inset-0 bg-noise opacity-[0.03] pointer-events-none" />

      {/* <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-blue-200/50 to-transparent pointer-events-none" /> */}
    </section>
  );
};

export default Hero;