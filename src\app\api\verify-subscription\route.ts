import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createClient } from '@supabase/supabase-js';
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { SubscriptionPlan } from "@/lib/subscription";

// For admin operations that bypass RLS
const getSupabaseAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }
  
  return createClient(supabaseUrl, supabaseKey);
};

const getStripeClient = () => {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
  
  if (!stripeSecretKey) {
    console.error('Missing Stripe secret key');
    throw new Error('Missing Stripe secret key');
  }
  
  return new Stripe(stripeSecretKey, {
    apiVersion: '2023-10-16' as Stripe.LatestApiVersion,
  });
};

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    // Get the current user from the session using the route client
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.error('No active session found:', sessionError);
      return NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 });
    }
    
    const user = session.user;
    if (!user) {
      console.error('No user found in session');
      return NextResponse.json({ error: "Unauthorized - No user found" }, { status: 401 });
    }

    const userId = user.id;
    
    // Use admin client to directly query the database
    const adminClient = getSupabaseAdminClient();
    const { data: profile, error: profileError } = await adminClient
      .from('profiles')
      .select('stripe_customer_id, stripe_subscription_id')
      .eq('id', userId)
      .single();
      
    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json({ error: "Error fetching user profile" }, { status: 500 });
    }
    
    const stripeCustomerId = profile?.stripe_customer_id;
    const stripeSubscriptionId = profile?.stripe_subscription_id;
    
    if (!stripeSubscriptionId) {
      return NextResponse.json({ 
        verified: false,
        message: "No subscription found",
        planType: "free",
        status: "inactive"
      });
    }
    
    // Verify subscription directly with Stripe
    const stripe = getStripeClient();
    const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId, {
      expand: ['items.data.price.product']
    });
    
    // Determine plan type from product name
    let planType: SubscriptionPlan = "free";
    const productName = ((subscription.items.data[0].price.product as Stripe.Product).name || '').toLowerCase();
    
    if (productName.includes('premium') && productName.includes('monthly')) {
      planType = "premium_monthly";
    } else if (productName.includes('premium') && productName.includes('yearly')) {
      planType = "premium_yearly";
    } else if (productName.includes('basic') && productName.includes('monthly')) {
      planType = "basic_monthly";
    } else if (productName.includes('basic') && productName.includes('yearly')) {
      planType = "basic_yearly";
    }
    
    const isActive = subscription.status === 'active' || subscription.status === 'trialing';
    
    // Update the database with the verified subscription data
    if (isActive) {
      const endTimestamp = (subscription as any).current_period_end || 0;
      const currentPeriodEnd = new Date(endTimestamp * 1000);
      
      await adminClient
        .from('profiles')
        .update({
          plan_type: planType,
          subscription_status: subscription.status,
          subscription_end_date: currentPeriodEnd.toISOString(),
        })
        .eq('id', userId);
        
      console.log('Updated subscription data from Stripe verification:', {
        userId,
        planType,
        status: subscription.status,
        endDate: currentPeriodEnd.toISOString()
      });
    }
    
    return NextResponse.json({
      verified: true,
      planType,
      status: subscription.status,
      currentPeriodEnd: (subscription as any).current_period_end ? new Date(((subscription as any).current_period_end as number) * 1000).toISOString() : null,
      message: isActive ? "Subscription is active" : "Subscription is not active"
    });
  } catch (error) {
    console.error("Error verifying subscription:", error);
    return NextResponse.json(
      { error: "Failed to verify subscription", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
