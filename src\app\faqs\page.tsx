"use client"

import React, { useEffect } from "react"
import Image from "next/image"
import { motion } from "framer-motion"
import { Search, HelpCircle, ChevronDown, ChevronUp } from "lucide-react"
import Navbar from "@/components/Navbar"
import Footer from "@/components/Footer"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export default function FAQsPage() {
  useEffect(() => {
    document.title = "Klinn | FAQs"
  }, [])

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 font-thin">
      <Navbar />

      <div className="fixed inset-0 w-full h-full z-0">
        <Image
          src="/images/backgroundpic.jpg"
          alt="Medical background"
          fill
          priority
          className="mix-blend-overlay opacity-30 select-none object-cover"
          sizes="100vw"
        />
      </div>

      <section className="relative min-h-screen text-white pt-15 lg-mt-20 mt-8 overflow-y-auto">
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h1 className="text-5xl sm:text-6xl font-thin mb-6 leading-tight">
              Frequently Asked <span className="text-blue-300">Questions</span>
            </h1>
            <p className="text-xl sm:text-2xl mb-8 text-blue-100 max-w-2xl mx-auto">
              Find answers to common questions about Klinn and our services.
            </p>
          </motion.div>

          <div className="relative">
            <div className="flex items-center mb-8 bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-lg p-3">
              <Search className="text-blue-300 ml-2 mr-2" />
              <input
                type="text"
                placeholder="Search FAQs..."
                className="w-full bg-transparent border-none focus:outline-none text-white placeholder-blue-200"
              />
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl p-6 shadow-2xl"
            >
              <FAQAccordion />
            </motion.div>
          </div>
        </div>
      </section>
      <Footer className="bg-transparent text-white" />
    </div>
  )
}

function FAQAccordion() {
  return (
    <Accordion type="multiple" className="space-y-4">
      <AccordionCategory title="General" />
      <AccordionCategory title="Products" />

      <AccordionCategory title="Billing and Pricing">
        <AccordionItem value="billing-1" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            How does Klinn&apos;s pricing work?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Klinn offers 4 tiers: Free, Basic, Premium, and Custom. For more information visit{" "}
            <a href="https://klinn.works/pricing" className="text-blue-300 hover:underline">
              https://klinn.works/pricing
            </a>
            .
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="billing-2" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            When will I be billed?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            We bill users at the beginning of each billing cycle — either monthly or annually depending on your
            subscription plan.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="billing-3" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Can I cancel my plan?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Yes, you can cancel your plan at any time from your account settings. Your subscription will remain active
            until the end of the billing period. You will not be charged for the next billing cycle.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="billing-4" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Can I switch between plans?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Yes, you can upgrade or downgrade your plan at any time from your account settings. Changes take effect
            almost immediately or at the next billing cycle, depending on the plan.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="billing-5" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Do you offer custom pricing for organizations?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Yes! We offer custom pricing for organizations such as school districts, universities, and CTE programs
            looking to purchase subscriptions for many students. Please contact{" "}
            <a href="mailto:<EMAIL>" className="text-blue-300 hover:underline">
              <EMAIL>
            </a>{" "}
            for a quote.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="billing-6" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Can I refund my subscription?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            We offer refunds on a case-by-case basis within 7 days of purchase. Please contact our support team if you
            believe you&apos;ve been billed in error.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="billing-7" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Is my payment information secure?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Absolutely. We use Stripe, a secure, PCI-compliant payment processor and never store your credit card
            information.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="billing-8" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            How can we support Klinn?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            We appreciate any kind of support—whether that be through subscribing to our paid plans, writing a
            testimonial, donating to our Buy Me a Coffee, or simply using our tools.
          </AccordionContent>
        </AccordionItem>
      </AccordionCategory>

      <AccordionCategory title="Klinn Student Scholars">
        <AccordionItem value="scholars-1" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            What is Klinn Student Scholars?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Klinn Student Scholars is a program, free of cost for students. Students sign up and get verified to get
            access to exclusive discounts and/or benefits from our partners. Not all partners necessarily provide these
            perks.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="scholars-2" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Who are Klinn&apos;s current partners for Klinn Student Scholars?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Currently we are partnered with sprint.dev, crackd.it, Ora AI, Runway Mobile App, and Excellence-AI.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="scholars-3" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Am I eligible for Klinn Student Scholars?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            To be eligible for Klinn Student Scholars, you must be 13+ years of age and enrolled in an educational
            institution full-time. Klinn reserves the right to accept or deny any student from the program.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="scholars-4" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            How do I sign up for Klinn Student Scholars?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            To sign up, go to{" "}
            <a href="https://klinn.works/scholars" className="text-blue-300 hover:underline">
              https://klinn.works/scholars
            </a>{" "}
            and click on the Apply button. You will be redirected to the signup form and will be required to upload
            proof that you&apos;re a student (transcript, student ID, etc). We will then verify you as soon as possible and
            notify you if you&apos;re approved or rejected.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="scholars-5" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            How do I access the benefits?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            After you sign up and get approved, you can access the benefits on the{" "}
            <a href="https://klinn.works/scholars" className="text-blue-300 hover:underline">
              https://klinn.works/scholars
            </a>
            . Make sure you are signed into the account for which you were approved. The benefits may be redeemed
            through a link, code, or other method depending on the partner; these will be found on the scholars page
            linked above.
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="scholars-6" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            Does Klinn make money off Klinn Student Scholars?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            Klinn may receive commissions and/or earnings from partners at no extra cost to the student.
          </AccordionContent>
        </AccordionItem>
      </AccordionCategory>

      <AccordionCategory title="Other">
        <AccordionItem value="other-1" className="border-b border-blue-400/30">
          <AccordionTrigger className="text-left font-normal py-4 hover:text-blue-300 transition-colors">
            What if my question wasn&apos;t answered?
          </AccordionTrigger>
          <AccordionContent className="text-blue-100 pb-4">
            If your questions were not answered within the FAQs, feel free to email{" "}
            <a href="mailto:<EMAIL>" className="text-blue-300 hover:underline">
              <EMAIL>
            </a>{" "}
            or create a ticket in our Discord.
            <p className="mt-2">
              To create a ticket, join the Discord linked above and select the type of ticket you&apos;d like to issue in the
              #create-a-ticket channel.
            </p>
          </AccordionContent>
        </AccordionItem>
      </AccordionCategory>
    </Accordion>
  )
}

interface AccordionCategoryProps {
  title: string
  children?: React.ReactNode
}

function AccordionCategory({ title, children }: AccordionCategoryProps) {
  const [isOpen, setIsOpen] = React.useState(false)

  return (
    <div className="mb-6">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
        className="bg-blue-800/50 rounded-lg overflow-hidden"
      >
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between p-4 text-xl font-medium text-white hover:bg-blue-700/50 transition-colors"
        >
          <div className="flex items-center">
            <HelpCircle className="mr-2 h-5 w-5 text-blue-300" />
            <span>{title}</span>
          </div>
          {isOpen ? <ChevronUp className="h-5 w-5 text-blue-300" /> : <ChevronDown className="h-5 w-5 text-blue-300" />}
        </button>

        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="px-4 py-2"
          >
            {children || <p className="text-blue-100 py-4">More information coming soon.</p>}
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}