import json
import csv
import os

def json_to_csv(json_file_path, csv_file_path):
    """
    Convert a JSON file containing an array of objects to a CSV file.
    Each JSON object in the array becomes a row in the CSV.
    Keep nested JSON objects intact for Supabase import.
    """
    # Read JSON file
    with open(json_file_path, 'r', encoding='utf-8') as json_file:
        data = json.load(json_file)
    
    # Check if data is a list
    if not isinstance(data, list):
        raise ValueError("JSON file should contain an array of objects")
    
    # Extract all field names from the JSON data
    fieldnames = set()
    for item in data:
        fieldnames.update(item.keys())
    fieldnames = list(fieldnames)
    
    # Write to CSV file
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
        writer.writeheader()
        
        for item in data:
            # Make a copy to avoid modifying the original data
            row_data = item.copy()
            
            # Store topics as JSON string for JSONB column (same as papers)
            if 'topics' in row_data and isinstance(row_data['topics'], list):
                row_data['topics'] = json.dumps(row_data['topics'])
            
            # Keep papers as JSON string - allows import to Supabase JSONB column
            if 'papers' in row_data and isinstance(row_data['papers'], list):
                row_data['papers'] = json.dumps(row_data['papers'])
            
            writer.writerow(row_data)
    
    print(f"Conversion complete. CSV file saved at {csv_file_path}")

if __name__ == "__main__":
    json_file_path = 'C:/Users/<USER>/Documents/klinn/python-backend/professors_ai_doxxed.json'
    csv_file_path = 'C:/Users/<USER>/Documents/klinn/python-backend/professors_ai_doxxed.csv'
    
    # Check if the JSON file exists
    if not os.path.isfile(json_file_path):
        print(f"Error: File {json_file_path} not found.")
    else:
        json_to_csv(json_file_path, csv_file_path)