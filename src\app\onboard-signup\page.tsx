"use client"

import type React from "react"
import { useState, useEffect, type ChangeEvent } from "react"
import { useRouter } from "next/navigation"
import { supabase } from "../../../supabase/supabaseClient"
import Navbar from "@/components/Navbar"
import Footer from "@/components/Footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, CheckCircle, ArrowRight, FileText, Upload, Download, Check, Trash2, Calendar } from "lucide-react"
import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

interface ProfileData {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  education: string;
  current_school?: string;
  location?: string;
  sat_act_score?: string;
  mcat_score?: string;
  bio?: string;
  website?: string;
  school_attended?: string;
  experience?: string;
  projects?: string;
  skills?: string;
  resumes?: Array<{
    name: string;
    url: string;
    uploaded_at: string;
  }>;
  default_resume_url?: string;
  role?: string;
  created_at?: string;
  updated_at?: string;
  tokens: number;
  referred_by: string;
}

export default function Onboarding() {
  const router = useRouter()
  const [profile, setProfile] = useState<ProfileData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState({
    education: "",
    current_school: "",
    sat_act_score: "",
    mcat_score: "",
    experience: "",
    projects: "",
    skills: "",
  })

  // Define the steps for the onboarding process
  const steps = [
    { title: "Resume", description: "Upload your resume" },
    { title: "Education", description: "Tell us about your education" },
    { title: "Experience", description: "Share your work experience" },
    { title: "Skills", description: "List your skills and projects" },
  ]

  // Calculate progress percentage based on the current step.
  const progress = ((currentStep + 1) / steps.length) * 100

  // Enhanced profile fetching: retry fetching and auto-create a profile if needed.
  useEffect(() => {
    const fetchOrCreateProfile = async () => {
      setIsLoading(true);
      setError(null);
  
      try {
        // Get current authenticated user.
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
          router.push("/auth");
          return;
        }
  
        // Retrieve referral from localStorage (if any)
        const referral = typeof window !== "undefined" ? localStorage.getItem("referral") : null;
  
        let profileData = null;
        let retries = 0;
        const maxRetries = 3;
  
        while (retries < maxRetries && !profileData) {
          // Try to fetch the profile.
          const { data, error: fetchError } = await supabase
            .from("profiles")
            .select("*")
            .eq("id", user.id)
            .single();
  
          if (!fetchError && data) {
            profileData = data;
            break;
          }
  
          // If profile doesn't exist or there's an error fetching, attempt to create it.
          // Ensure we include referral info during creation.
          const referralText = referral ? referral.toString() : null;
          const { error: createError } = await supabase.from("profiles").upsert({
            id: user.id,
            email: user.email,
            first_name: user.user_metadata?.full_name?.split(" ")[0] || "",
            last_name: user.user_metadata?.full_name?.split(" ").slice(1).join(" ") || "",
            role: "general",
            onboarding_complete: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            tokens: 5, // Initial tokens on sign-up.
            referred_by: referralText, // Store the referral info as text.
          });
  
          if (createError) {
            throw createError;
          }
  
          retries++;
          if (retries < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * retries));
          }
        }
  
        if (!profileData) {
          throw new Error("Failed to fetch or create profile after multiple attempts");
        }
  
        // Set the profile state and initialize the onboarding form with any existing data.
        setProfile(profileData);
        setFormData({
          education: profileData.education || "",
          current_school: profileData.current_school || "",
          sat_act_score: profileData.sat_act_score || "",
          mcat_score: profileData.mcat_score || "",
          experience: profileData.experience || "",
          projects: profileData.projects || "",
          skills: profileData.skills || "",
        });
      } catch (err: any) {
        console.error("Profile error:", err);
        setError(err.message || "Failed to load profile data");
      } finally {
        setIsLoading(false);
      }
    };
  
    fetchOrCreateProfile();
  }, [router]);  
  

  // Handle input changes for text fields and textareas.
  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  // Handle resume file upload.
  const handleResumeUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !profile) return

    try {
      const uniqueFileName = `${Date.now()}-${file.name}`
      const filePath = `${profile.id}/${uniqueFileName}`

      const { error: uploadError } = await supabase.storage
        .from("resumes")
        .upload(filePath, file)

      if (uploadError) throw uploadError

      const { data: urlData } = await supabase.storage
        .from("resumes")
        .getPublicUrl(filePath)

      if (!urlData?.publicUrl) throw new Error("Failed to get public URL")

      // Update the profile with the new resume details.
      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          resumes: [
            ...(profile.resumes || []),
            {
              name: file.name,
              url: urlData.publicUrl,
              uploaded_at: new Date().toISOString(),
            },
          ],
          default_resume_url: urlData.publicUrl,
          updated_at: new Date().toISOString(),
        })
        .eq("id", profile.id)

      if (updateError) throw updateError

      // Refresh the profile data.
      const { data: updatedProfile } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", profile.id)
        .single()

      if (updatedProfile) setProfile(updatedProfile)
    } catch (error) {
      console.error("Error during resume upload:", error)
      setError("Failed to upload resume. Please try again.")
    }
  }

  // Handle removal of a resume.
  const handleRemoveResume = async (resumeUrl: string) => {
    if (!profile) return

    try {
      const filePath = resumeUrl.split("/").slice(4).join("/")

      const { error: deleteError } = await supabase.storage
        .from("resumes")
        .remove([filePath])

      if (deleteError) throw deleteError

      const updatedResumes = (profile.resumes || []).filter(
        (resume) => resume.url !== resumeUrl
      )

      const { error: updateError } = await supabase
        .from("profiles")
        .update({
          resumes: updatedResumes,
          default_resume_url:
            profile.default_resume_url === resumeUrl
              ? updatedResumes[0]?.url || null
              : profile.default_resume_url,
          updated_at: new Date().toISOString(),
        })
        .eq("id", profile.id)

      if (updateError) throw updateError

      // Refresh profile data.
      const { data: updatedProfile } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", profile.id)
        .single()

      if (updatedProfile) setProfile(updatedProfile)
    } catch (error) {
      console.error("Error during resume removal:", error)
      setError("Failed to remove resume. Please try again.")
    }
  }

  // Handle setting a default resume.
  const handleSetDefaultResume = async (resumeUrl: string) => {
    if (!profile) return

    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          default_resume_url: resumeUrl,
          updated_at: new Date().toISOString(),
        })
        .eq("id", profile.id)

      if (error) throw error

      // Refresh profile data.
      const { data: updatedProfile } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", profile.id)
        .single()

      if (updatedProfile) setProfile(updatedProfile)
    } catch (error) {
      console.error("Error during setting default resume:", error)
      setError("Failed to set default resume. Please try again.")
    }
  }

// Handle submission of onboarding details.
const handleSubmit = async () => {
  if (!profile) return;

  try {
    // Update the user's profile with onboarding details.
    const { error } = await supabase
      .from("profiles")
      .update({
        education: formData.education,
        current_school: formData.current_school,
        sat_act_score: formData.sat_act_score,
        mcat_score: formData.mcat_score,
        experience: formData.experience,
        projects: formData.projects,
        skills: formData.skills,
        onboarding_complete: true,
        updated_at: new Date().toISOString(),
      })
      .eq("id", profile.id);

    if (error) throw error;

    // If a referral exists, call the API endpoint to award the referrer a token.
    if (profile.referred_by) {
      const response = await fetch('/api/award-referrer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ referrerId: profile.referred_by })
      });
      const resData = await response.json();
      if (!response.ok) {
        console.error("Error awarding referrer token:", resData.error);
      } else {
        console.log("Successfully awarded referrer token:", resData.newTokenCount);
      }
    }
    

    router.push("/profile-dashboard");
  } catch (err) {
    console.error("Error updating profile during onboarding:", err);
    setError("Failed to save profile. Please try again.");
  }
};


// async function awardReferrer(referrerId: string) {
//   console.log("Awarding referrer token for:", referrerId);
//   const { data, error: fetchError } = await supabase
//     .from("profiles")
//     .select("id, tokens")
//     .eq("id", referrerId)
//     .maybeSingle();
  
//   if (fetchError) {
//     console.error("Error fetching referrer profile:", fetchError);
//     return;
//   }

//   if (!data) {
//     console.error("Referrer profile not found. Referrer ID:", referrerId);
//     return;
//   }

//   const newTokenCount = (data.tokens || 0) + 1;
//   const { error: updateError } = await supabase
//     .from("profiles")
//     .update({ tokens: newTokenCount, updated_at: new Date().toISOString() })
//     .eq("id", referrerId);

//   if (updateError) {
//     console.error("Could not update referrer token count:", updateError);
//   }
// }



  // Navigate to the next step or submit the form.
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleSubmit()
    }
  }

  // Navigate to the previous step.
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  if (isLoading)
    return (
      <div className="min-h-screen flex flex-col bg-blue-50">
        <Navbar />
        <div className="flex-grow flex items-center justify-center pt-20">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-lg text-blue-600 font-light">Loading your profile...</p>
          </div>
        </div>
        <Footer />
      </div>
    )

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-300 to-blue-600">
      <Navbar />
      <div className="absolute inset-0 bg-[url('/grid-blue.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] pointer-events-none opacity-20" />
      
      <main className="flex-grow container mx-auto px-4 py-8 pt-24 relative z-10">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          <div className="mb-12">
            <motion.h1 
              className="text-4xl font-thin text-center text-blue-900 tracking-tight"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              Complete Your Profile
            </motion.h1>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
            >
              <p className="text-center text-blue-600 mt-2 font-extralight tracking-wide">
                Help us personalize your experience
              </p>
            </motion.div>
          </div>

          {/* Progress bar */}
          <div className="mb-10">
            <div className="flex justify-between mb-3">
              {steps.map((step, index) => (
                <motion.div 
                  key={index} 
                  className={`text-xs font-light ${index <= currentStep ? "text-blue-600" : "text-blue-900"}`}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.1 * index, duration: 0.5 }}
                >
                  {step.title}
                </motion.div>
              ))}
            </div>
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ duration: 0.5 }}
              style={{ originX: 0 }}
            >
              <Progress 
                value={progress} 
                className="h-1 bg-blue-100" 
                style={{ 
                  "--tw-progress-fill": "linear-gradient(to right, #3b82f6, #60a5fa)"
                } as React.CSSProperties} 
              />
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-blue-100/50 backdrop-blur-xl rounded-2xl transform rotate-1 scale-[1.03] -z-10 shadow-lg"></div>
            <Card className="border-0 bg-gradient-to-br from-white/90 to-blue-50/90 backdrop-blur-sm shadow-[0_8px_30px_rgba(59,130,246,0.1)] rounded-xl overflow-hidden border border-blue-100/50">
              <CardContent className="p-8 md:p-10">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="min-h-[400px] flex flex-col"
                >
                  {/* Step 1: Resume Upload */}
                  {currentStep === 0 && (
                    <div className="space-y-8 flex-1">
                      <div className="text-center mb-8">
                        <h2 className="text-2xl font-thin text-blue-800 tracking-wide">{steps[currentStep].title}</h2>
                        <p className="text-blue-600/80 font-extralight">{steps[currentStep].description}</p>
                      </div>
                      <div className="space-y-6">
                        <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                          <div className="space-y-4">
                            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 flex flex-col sm:flex-row sm:items-center gap-4">
                              <div className="flex-1">
                                <h3 className="font-light text-blue-800 text-sm sm:text-base mb-1">Upload New Resume</h3>
                                <p className="text-xs sm:text-sm text-blue-600">
                                  Upload your resume in PDF, DOC, or DOCX format
                                </p>
                              </div>
                              <div>
                                <Label
                                  htmlFor="resume-upload"
                                  className="cursor-pointer inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors text-sm"
                                >
                                  <Upload className="h-4 w-4" />
                                  Upload Resume
                                </Label>
                                <Input
                                  id="resume-upload"
                                  type="file"
                                  accept=".pdf,.doc,.docx"
                                  onChange={handleResumeUpload}
                                  className="hidden"
                                />
                              </div>
                            </div>

                            <Separator />

                            <div>
                              <h3 className="text-lg font-light text-gray-800 mb-3 flex items-center gap-2">
                                <FileText className="h-5 w-5 text-blue-600" />
                                Your Resumes
                              </h3>

                              {profile?.resumes && profile.resumes.length > 0 ? (
                                <div className="space-y-3">
                                  {profile.resumes.map((resume) => (
                                    <div
                                      key={resume.url}
                                      className={`p-4 rounded-lg border ${
                                        profile.default_resume_url === resume.url
                                          ? "bg-blue-50 border-blue-200"
                                          : "bg-gray-50 border-gray-200"
                                      }`}
                                    >
                                      <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:justify-between">
                                        <div className="flex-1">
                                          <div className="flex items-center gap-2">
                                            <FileText
                                              className={`h-5 w-5 ${
                                                profile.default_resume_url === resume.url ? "text-blue-600" : "text-gray-600"
                                              }`}
                                            />
                                            <h4 className="font-light text-base truncate max-w-[200px]">{resume.name}</h4>
                                            {profile.default_resume_url === resume.url && (
                                              <Badge className="bg-blue-100 text-blue-800 border-blue-200 text-xs">
                                                Default
                                              </Badge>
                                            )}
                                          </div>
                                          <p className="text-xs text-gray-500 mt-1 flex items-center gap-1.5">
                                            <Calendar className="h-3.5 w-3.5" />
                                            Uploaded: {new Date(resume.uploaded_at).toLocaleDateString()}
                                          </p>
                                        </div>
                                        <div className="flex flex-wrap items-center gap-2 mt-2 sm:mt-0">
                                          {profile.default_resume_url !== resume.url && (
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              onClick={() => handleSetDefaultResume(resume.url)}
                                              className="text-xs border-blue-300 text-blue-700 hover:bg-blue-50 h-8 px-2 py-0"
                                            >
                                              <Check className="h-3.5 w-3.5 mr-1" />
                                              Set Default
                                            </Button>
                                          )}
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => window.open(resume.url, "_blank")}
                                            className="text-xs h-8 px-2 py-0"
                                          >
                                            <Download className="h-3.5 w-3.5 mr-1" />
                                            View
                                          </Button>
                                          <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleRemoveResume(resume.url)}
                                            className="text-xs border-red-300 text-red-700 hover:bg-red-50 h-8 px-2 py-0"
                                          >
                                            <Trash2 className="h-3.5 w-3.5 mr-1" />
                                            Remove
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              ) : (
                                <div className="text-center py-10 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                                  <h4 className="text-lg font-light text-gray-600 mb-1">No Resumes Uploaded</h4>
                                  <p className="text-sm text-gray-500">
                                    Upload your first resume to get started
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Step 2: Education */}
                  {currentStep === 1 && (
                    <div className="space-y-8 flex-1">
                      <div className="text-center mb-8">
                        <h2 className="text-2xl font-thin text-blue-800 tracking-wide">{steps[currentStep].title}</h2>
                        <p className="text-blue-600/80 font-extralight">{steps[currentStep].description}</p>
                      </div>
                      <div className="space-y-6">
                        <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                          <Label htmlFor="education" className="text-blue-700 font-light">Education Level</Label>
                          <Input
                            id="education"
                            name="education"
                            placeholder="E.g. Bachelor's, Master's, PhD"
                            value={formData.education}
                            onChange={handleInputChange}
                            className="mt-2 bg-white/80 border-blue-200 focus:border-blue-400 text-blue-900 font-light"
                          />
                        </div>
                        <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                          <Label htmlFor="current_school" className="text-blue-700 font-light">Current School</Label>
                          <Input
                            id="current_school"
                            name="current_school"
                            placeholder="Enter your current school"
                            value={formData.current_school}
                            onChange={handleInputChange}
                            className="mt-2 bg-white/80 border-blue-200 focus:border-blue-400 text-blue-900 font-light"
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                            <Label htmlFor="sat_act_score" className="text-blue-700 font-light">SAT/ACT Score</Label>
                            <Input
                              id="sat_act_score"
                              name="sat_act_score"
                              placeholder="Optional"
                              value={formData.sat_act_score}
                              onChange={handleInputChange}
                              className="mt-2 bg-white/80 border-blue-200 focus:border-blue-400 text-blue-900 font-light"
                            />
                          </div>
                          <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                            <Label htmlFor="mcat_score" className="text-blue-700 font-light">MCAT Score</Label>
                            <Input
                              id="mcat_score"
                              name="mcat_score"
                              placeholder="Optional"
                              value={formData.mcat_score}
                              onChange={handleInputChange}
                              className="mt-2 bg-white/80 border-blue-200 focus:border-blue-400 text-blue-900 font-light"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Step 3: Experience */}
                  {currentStep === 2 && (
                    <div className="space-y-8 flex-1">
                      <div className="text-center mb-8">
                        <h2 className="text-2xl font-thin text-blue-800 tracking-wide">{steps[currentStep].title}</h2>
                        <p className="text-blue-600/80 font-extralight">{steps[currentStep].description}</p>
                      </div>
                      <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                        <Label htmlFor="experience" className="text-blue-700 font-light">Work/Internship Experience</Label>
                        <Textarea
                          id="experience"
                          name="experience"
                          placeholder="Describe your relevant experience"
                          value={formData.experience}
                          onChange={handleInputChange}
                          className="mt-2 min-h-[200px] bg-white/80 border-blue-200 focus:border-blue-400 text-blue-900 font-light"
                        />
                      </div>
                    </div>
                  )}

                  {/* Step 4: Skills and Projects */}
                  {currentStep === 3 && (
                    <div className="space-y-8 flex-1">
                      <div className="text-center mb-8">
                        <h2 className="text-2xl font-thin text-blue-800 tracking-wide">{steps[currentStep].title}</h2>
                        <p className="text-blue-600/80 font-extralight">{steps[currentStep].description}</p>
                      </div>
                      <div className="space-y-6">
                        <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                          <Label htmlFor="skills" className="text-blue-700 font-light">Skills & Interests</Label>
                          <Input
                            id="skills"
                            name="skills"
                            placeholder="E.g. JavaScript, Python, Design"
                            value={formData.skills}
                            onChange={handleInputChange}
                            className="mt-2 bg-white/80 border-blue-200 focus:border-blue-400 text-blue-900 font-light"
                          />
                        </div>
                        <div className="bg-white/70 p-6 rounded-xl border border-blue-100 backdrop-blur-sm shadow-sm">
                          <Label htmlFor="projects" className="text-blue-700 font-light">Projects or Portfolios</Label>
                          <Textarea
                            id="projects"
                            name="projects"
                            placeholder="List notable projects or research"
                            value={formData.projects}
                            onChange={handleInputChange}
                            className="mt-2 min-h-[200px] bg-white/80 border-blue-200 focus:border-blue-400 text-blue-900 font-light"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </motion.div>

                {/* Navigation buttons */}
                <div className="flex justify-between mt-10 pt-6 border-t border-blue-100">
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={prevStep}
                      disabled={currentStep === 0}
                      className="flex items-center gap-2 border-blue-200 text-blue-700 hover:bg-blue-50 hover:text-blue-800 font-light"
                    >
                      <ChevronLeft className="h-4 w-4" /> Back
                    </Button>
                  </motion.div>
                  <div className="flex gap-4">
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={() => router.push("/profile-dashboard")}
                        className="text-blue-500 hover:text-blue-700 hover:bg-blue-50 font-light"
                      >
                        Skip
                      </Button>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                      <Button
                        type="button"
                        onClick={nextStep}
                        className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-500 hover:to-blue-400 text-white font-light px-6"
                      >
                        {currentStep === steps.length - 1 ? "Complete" : "Next"}{" "}
                        {currentStep < steps.length - 1 ? <ChevronRight className="h-4 w-4" /> : <ArrowRight className="h-4 w-4" />}
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </main>
      <Footer />
    </div>
  )
}
