import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

const getStripeClient = () => {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
  if (!stripeSecretKey) {
    throw new Error('Missing STRIPE_SECRET_KEY');
  }
  return new Stripe(stripeSecretKey, {
    apiVersion: '2023-10-16' as Stripe.LatestApiVersion,
  });
};

export async function POST(req: NextRequest) {
  try {
    // Get the current user from the session
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      console.log('Portal session request: No user session found');
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`Portal session request for user: ${session.user.id}`);

    // Get the user's profile to check for existing customer ID
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('stripe_customer_id, email')
      .eq('id', session.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return NextResponse.json(
        { error: "Profile not found" },
        { status: 404 }
      );
    }

    if (!profile?.stripe_customer_id) {
      console.log(`No Stripe customer ID found for user ${session.user.id} (${profile?.email})`);
      return NextResponse.json(
        { error: "No subscription found" },
        { status: 400 }
      );
    }

    console.log(`Creating portal session for customer: ${profile.stripe_customer_id}`);

    const stripe = getStripeClient();

    try {
      // Create a portal session
      const portalSession = await stripe.billingPortal.sessions.create({
        customer: profile.stripe_customer_id,
        return_url: `${req.headers.get('origin')}/profile-dashboard`,
      });

      console.log(`Portal session created successfully: ${portalSession.id}`);
      return NextResponse.json({ url: portalSession.url });
    } catch (stripeError: any) {
      // Handle specific Stripe errors
      if (stripeError.type === 'StripeInvalidRequestError' && stripeError.code === 'resource_missing') {
        console.log(`Stripe customer ${profile.stripe_customer_id} not found, clearing from database`);

        // Clear the invalid customer ID from the database
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            stripe_customer_id: null,
            subscription_status: null,
            subscription_plan_type: 'free',
            subscription_current_period_end: null
          })
          .eq('id', session.user.id);

        if (updateError) {
          console.error('Error clearing invalid customer data:', updateError);
        } else {
          console.log('Successfully cleared invalid customer data from database');
        }

        return NextResponse.json(
          { error: "Subscription data was outdated and has been reset. Please subscribe again." },
          { status: 400 }
        );
      }

      // Re-throw other Stripe errors to be caught by the outer catch block
      throw stripeError;
    }
  } catch (error) {
    console.error('Error creating portal session:', error);
    return NextResponse.json(
      { error: 'Failed to create portal session' },
      { status: 500 }
    );
  }
}
