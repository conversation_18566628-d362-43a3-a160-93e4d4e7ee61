'use client';

import React, { useEffect, useState } from 'react';
import { supabase } from '../../../../supabase/supabaseClient';
import { useRouter, useSearchParams } from 'next/navigation';

export default function ResetPasswordPage() {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const [isResetting, setIsResetting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [tokenVerified, setTokenVerified] = useState(false);

  const [email, setEmail] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Extract required params
    const token = searchParams.get('token');
    const type = searchParams.get('type');
    const emailQuery = searchParams.get('email');

    // Validate query parameters
    if (!token || type !== 'recovery' || !emailQuery) {
      setError('Invalid reset link parameters');
      setIsLoading(false);
      return;
    }

    // Store email for verifyOtp
    setEmail(emailQuery);
    setTokenVerified(true);
    setIsLoading(false);
  }, [searchParams]);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    // Front-end checks
    if (newPassword !== confirmPassword) {
      setError("Passwords don't match");
      return;
    }
    if (newPassword.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }

    // Immediately show progress to user
    setIsResetting(true);
    setError('');
    setMessage('Resetting your password…');

    try {
      const token = searchParams.get('token');
      if (!token) {
        throw new Error('Missing token parameter');
      }

      console.log('Verifying token (otp) before updating password...');

      // 1) Verify the token explicitly
      const { data: verifyData, error: verifyError } = await supabase.auth.verifyOtp({
        token,
        email,
        type: 'recovery',
      });
      if (verifyError) {
        console.error('verifyOtp failed:', verifyError);
        throw verifyError;
      }
      console.log('verifyOtp =>', verifyData);

      // 2) Check session to ensure the ephemeral login stuck
      const { data: sessionData } = await supabase.auth.getSession();
      console.log('After verifyOtp, getSession =>', sessionData);

      // 3) Update the user’s password
      console.log('Token verified, updating password...');
      const { data: updateData, error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });
      console.log('updateUser =>', 'data:', updateData, 'error:', updateError);
      if (updateError) {
        console.error('Password update failed:', updateError);
        throw updateError;
      }

      console.log('Password updated successfully');
      setMessage('Password updated successfully! Redirecting…');

      // 4) Optionally sign out and go to login
      await supabase.auth.signOut();
      setTimeout(() => router.push('/auth'), 2000);
    } catch (err: any) {
      console.error('Error details:', err);
      setError(err.message || 'Password reset failed');
      // Clear the success message if something went wrong
      setMessage('');
    } finally {
      setIsResetting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#12100E] to-[#93C5FD] text-white">
        <h1 className="text-3xl font-bold">Checking Reset Link…</h1>
      </div>
    );
  }

  if (!tokenVerified) {
    // If invalid, user sees error + button to request new link
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#12100E] to-[#93C5FD] text-white">
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">Invalid Reset Link</h1>
          <p className="text-red-500">{error}</p>
          <button
            onClick={() => router.push('/auth/forgot-password')}
            className="mt-4 bg-white text-[#1E3A8A] font-bold py-2 px-4 rounded-lg hover:bg-opacity-90 transition"
          >
            Request New Link
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-[#12100E] to-[#93C5FD]">
      <main className="flex-grow flex items-center justify-center p-6 pt-12">
        <div className="container mx-auto flex flex-col items-center justify-center">
          <h1 className="text-3xl font-bold mb-6 text-white">Reset Password</h1>
          <form onSubmit={handleResetPassword} className="space-y-5 w-full max-w-md">
            <input
              type="password"
              placeholder="New Password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
              required
              minLength={6}
            />
            <input
              type="password"
              placeholder="Confirm Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
              required
              minLength={6}
            />

            {error && <p className="text-red-500 text-sm">{error}</p>}
            {message && <p className="text-green-500 text-sm">{message}</p>}

            <button
              type="submit"
              disabled={isResetting}
              className="w-full bg-white text-[#1E3A8A] font-bold py-3 rounded-lg hover:bg-opacity-90 transition duration-300 disabled:opacity-50"
            >
              {isResetting ? 'Confirmed' : 'Set New Password'}
            </button>
          </form>
        </div>
      </main>
    </div>
  );
}
