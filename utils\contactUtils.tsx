export function getFilteredContacts(
    contacts: any[],
    workType: "research" | "startup" | "clinic",
    interests: string[],
    searchQuery: string
  ) {
    let filtered = contacts.filter((c) => c.type === workType);
    if (interests.length > 0) {
      filtered = filtered.filter((contact) =>
        contact.interests.some((interest: string) => interests.includes(interest.toLowerCase()))
      );
    }
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (contact) =>
          contact.name.toLowerCase().includes(query) ||
          (contact.department && contact.department.toLowerCase().includes(query)) ||
          (contact.university && contact.university.toLowerCase().includes(query)) ||
          (contact.company && contact.company.toLowerCase().includes(query)) ||
          contact.interests.some((i: string) => i.toLowerCase().includes(query))
      );
    }
    return filtered.sort((a, b) => b.match_score - a.match_score);
  }
  