/// <reference lib="deno.ns" />

import { serve } from "std/http/server.ts";
import { createClient } from "@supabase/supabase-js";

serve(async (req) => {
  try {
    const payload = await req.json();
    console.log("▶️ received payload:", JSON.stringify(payload));

    const bucket = payload?.record?.bucket_id;
    const key    = payload?.record?.name;
    if (bucket !== "resumes") {
      console.log("⏭ ignoring bucket:", bucket);
      return new Response("ignored", { status: 200 });
    }

    // 1) get a signed URL
    const admin = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );
    const { data, error: signErr } = await admin
      .storage
      .from(bucket)
      .createSignedUrl(key, 300);

    if (signErr) throw new Error("signedUrl error: " + JSON.stringify(signErr));
    console.log("✅ signedUrl:", data.signedUrl);

    // 2) download the file
    const pdfRes = await fetch(data.signedUrl!);
    if (!pdfRes.ok) throw new Error("download failed: " + pdfRes.status);
    const bytes = new Uint8Array(await pdfRes.arrayBuffer());

    // 3) send to your Python service
    const form = new FormData();
    form.append("file", new File([bytes], "resume.pdf", { type: "application/pdf" }));

    const vectorBase = Deno.env.get("VECTOR_API");
    if (!vectorBase) throw new Error("VECTOR_API not set");
    console.log("⏩ calling vector service at", vectorBase + "/embed-pdf");

    const embedRes = await fetch(vectorBase + "/embed-pdf", {
      method: "POST",
      body: form,
    });
    if (!embedRes.ok) {
      const txt = await embedRes.text();
      throw new Error(`embed-pdf failed ${embedRes.status}: ${txt}`);
    }
    const { embedding } = await embedRes.json();
    console.log("🧮 received embedding of length", embedding.length);

    // 4) write back to your profiles table
    const userId = key.split("/")[0];
    const { error: dbErr } = await admin
      .from("profiles")
      .update({
        resume_embedding: embedding,
        resume_emb_updated_at: new Date().toISOString(),
      })
      .eq("id", userId);

    if (dbErr) throw new Error("DB update error: " + JSON.stringify(dbErr));
    console.log("🎉 saved embedding to profile", userId);

    return new Response("ok", { status: 200 });

  } catch (err) {
    console.error("🔥 fatal error in embed_resume:", err);
    return new Response(
      JSON.stringify({ error: (err as Error).message }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
});
