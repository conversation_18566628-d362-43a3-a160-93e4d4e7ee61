// /app/api/auth/google/gmail/callback/route.ts

import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '../../../../../../../supabase/serversupabaseClient'; 
// ^ Adjust to your actual path
// e.g. 'app/lib/' or 'lib/'

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  // Create the Supabase client for THIS request
  const supabase = createRouteClient();

  const url = new URL(req.url);
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state') || '';

  console.log('Gmail callback: code =', code, 'state =', state);

  let redirectTo = '/profile-dashboard';
  if (state.includes('|')) {
    const parts = state.split('|');
    if (parts.length > 1) {
      redirectTo = parts[1];
    }
  }

  function redirectAbsolute(path: string) {
    return NextResponse.redirect(new URL(path, req.url));
  }

  if (!code) {
    return redirectAbsolute(`${redirectTo}?auth_error=missing_code`);
  }

  try {
    // 1) Check if there's an authenticated user session
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return redirectAbsolute(`${redirectTo}?auth_error=no_user_session`);
    }

    // 2) Exchange the authorization code for tokens
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        code,
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
        client_secret: process.env.GOOGLE_CLIENT_SECRET || '',
        redirect_uri: `${url.origin}/api/auth/google/gmail/callback`,
        grant_type: 'authorization_code',
      }),
    });

    const tokens = await tokenResponse.json();
    if (!tokenResponse.ok || !tokens.access_token) {
      console.error('Failed to exchange code:', tokens);
      return redirectAbsolute(`${redirectTo}?auth_error=token_exchange_failed`);
    }

    // 3) Upsert tokens in your Supabase DB
    const expiresAt = new Date(Date.now() + tokens.expires_in * 1000).toISOString();

    const { error } = await supabase.from('user_oauth_tokens2').upsert({
      user_id: user.id,
      provider: 'google',
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      expires_at: expiresAt,
    });

    if (error) {
      console.error('Error upserting tokens:', error);
      return redirectAbsolute(`${redirectTo}?auth_error=db_upsert_failed`);
    }

    // 4) Redirect success
    return redirectAbsolute(`${redirectTo}?gmail_connected=true`);
  } catch (err) {
    console.error('Error exchanging code:', err);
    return redirectAbsolute(`${redirectTo}?auth_error=server_error`);
  }
}
