"use client";
import React from 'react';
import { Card, CardContent } from './ui/card';
import Link from 'next/link';
import { Button } from './ui/button';
import ClinicResults from "@/components/ClinicResults";
import { useSearchParams } from 'next/navigation';

export const ClinicSearch = () => {
    const searchParams = useSearchParams();
    const clinicsParam = searchParams.get("clinics");
    const clinics = clinicsParam ? JSON.parse(decodeURIComponent(clinicsParam)) : [];
    
  return (
    <div>
    {clinics.length > 0 ? (
        <ClinicResults clinics={clinics} />
      ) : (
        <Card className="bg-white shadow-lg p-12 text-center max-w-3xl mx-auto">
          <CardContent>
            <p className="text-2xl text-gray-600 mb-6">
              No results to display. Please perform a search first.
            </p>
            <Link href="/search">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white text-lg py-3 px-6 transition-all duration-300">
                Go to Search
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ClinicSearch;
