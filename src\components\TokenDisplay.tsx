"use client"

import { useState, useRef, useEffect } from "react"
import { createPortal } from "react-dom"
import { Coins, ExternalLink, Info } from "lucide-react"

interface TokenDisplayProps {
  tokens: number
  bgColor?: string
}

export function TokenDisplay({ tokens, bgColor = "bg-white" }: TokenDisplayProps) {
  const [showTooltip, setShowTooltip] = useState(false)
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 })
  const cardRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (showTooltip && cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect()
      setTooltipPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + rect.width / 2 + window.scrollX,
      })
    }
  }, [showTooltip])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        tooltipRef.current &&
        !tooltipRef.current.contains(event.target as Node) &&
        cardRef.current &&
        !cardRef.current.contains(event.target as Node)
      ) {
        setShowTooltip(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div className="mt-4">
      <div
        ref={cardRef}
        className={`relative overflow-hidden rounded-lg ${bgColor} dark:bg-gray-800 shadow-md transition-shadow duration-300 hover:shadow-lg border border-blue-100 dark:border-blue-900 cursor-pointer`}
        onMouseEnter={() => setShowTooltip(true)}
      >
        <div className="flex items-center justify-between p-4 w-80">
          <div className="flex items-center">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-50 dark:bg-blue-900">
              <Coins className="h-6 w-6 text-blue-500 dark:text-blue-300" />
            </div>
            <div className="ml-4">
              <p className="text-xs font-light uppercase text-blue-600 dark:text-blue-400">Your Balance</p>
              <div className="flex items-baseline">
                <span className="text-2xl font-light text-blue-700 dark:text-blue-200">{tokens}</span>
                <span className="ml-1 text-sm font-light text-blue-600 dark:text-blue-300">tokens</span>
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <Info className="h-5 w-5 text-blue-400 dark:text-blue-500" />
          </div>
        </div>
        <div className="px-4 pb-4">
          <div className="h-1 w-full overflow-hidden rounded-full bg-blue-100 dark:bg-blue-800">
            <div
              className="h-full rounded-full bg-blue-500 dark:bg-blue-400 transition-all duration-300 ease-in-out"
              style={{ width: `${Math.min(tokens * 10, 100)}%` }}
            ></div>
          </div>
        </div>
      </div>

      {showTooltip &&
        typeof document !== "undefined" &&
        createPortal(
          <div
            ref={tooltipRef}
            className="fixed z-50 w-72 p-4 text-sm bg-white dark:bg-gray-800 text-blue-800 dark:text-blue-200 rounded-lg shadow-lg transition-opacity duration-200 border border-blue-200 dark:border-blue-700"
            style={{
              top: `${tooltipPosition.top}px`,
              left: `${tooltipPosition.left}px`,
              transform: "translate(-50%, 8px)",
            }}
          >
            <div className="font-medium mb-2 text-blue-900 dark:text-blue-100">What are tokens used for?</div>
            <p className="mb-3 font-light">
              Tokens allow you to unlock premium listings, contact researchers or startup employees, and access detailed
              information about clinics and opportunities.
            </p>
            <a
              href="/faqs"
              className="inline-flex items-center text-xs text-blue-600 dark:text-blue-300 hover:text-blue-800 dark:hover:text-blue-100 transition-colors"
            >
              <span>View FAQ</span>
              <ExternalLink className="ml-1 h-3 w-3" />
            </a>
          </div>,
          document.body,
        )}
    </div>
  )
}
