"use client";
import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Instagram, Linkedin, Coffee, MessageSquare } from 'lucide-react';
import { FaDiscord } from 'react-icons/fa';
import Image from 'next/image';
import clsx from 'clsx';

export default function Footer({ className = '' }) {
  return (
    <footer
      className={clsx(
        'text-white p-8',
        className
      )}
    >
      <div className="container mx-auto grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
        {/* Find Us Section */}
        <div className="flex flex-col items-center">
          <h3 className="text-xl font-semibold mb-4 text-black">Find Us:</h3>
          <div className="flex flex-col space-y-2 items-center">
            <a
              href="https://www.instagram.com/klinnworks"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center"
            >
              <Instagram className="w-6 h-6 mr-2 text-black hover:text-[#BFDBF7]" />
              <span className="text-lg text-black hover:text-[#BFDBF7]">@klinnworks</span>
            </a>
            <a
              href="https://www.linkedin.com/company/klinn"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center"
            >
              <Linkedin className="w-6 h-6 mr-2 text-black hover:text-[#BFDBF7]" />
              <span className="text-lg text-black hover:text-[#BFDBF7]">@klinn</span>
            </a>
            <a
              href="https://discord.gg/N6h4z8S8a6"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center"
            >
              <FaDiscord className="w-6 h-6 mr-2 text-black hover:text-[#BFDBF7]" />
              <span className="text-lg text-black hover:text-[#BFDBF7]">Join our Discord!</span>
            </a>
          </div>
        </div>
        {/* Support Us Section */}
        <div className="flex flex-col items-center">
          <h3 className="text-xl text-black font-semibold mb-4">Support Us:</h3>
          <a
            href="https://buymeacoffee.com/klinn.works"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center bg-yellow-500 text-gray-900 px-4 py-2 rounded-md hover:bg-yellow-600 transition duration-300"
          >
            <Coffee className="mr-2" />
            Buy me a coffee
          </a>
          <Link href="/testimonials">
            <Button
              variant="outline"
              className="mt-4 inline-flex items-center border-gray-300 text-black hover:bg-transparent transition duration-300"
            >
              <MessageSquare className="mr-2" />
              Leave a testimonial
            </Button>
          </Link>
        </div>
        {/* Partners Section */}
        <div className="flex flex-col items-center">
          <h3 className="text-xl text-black font-semibold mb-4">Our Partners:</h3>
          <div className="flex space-x-4 justify-center">
            <a href="https://runwaymobile.app/">
              <Image
                src="/images/runwayAirplaneLogo.webp"
                alt="Runway"
                width={48}
                height={48}
                className="h-12 w-12 rounded-full object-cover transition-all duration-300 hover:scale-125"
              />
            </a>
            <a href="https://www.thryving.app/">
              <Image
                src="/images/thryvinglogo.png"
                alt="Thryving"
                width={48}
                height={48}
                className="h-12 w-12 rounded-full object-cover transition-all duration-300 hover:scale-125"
              />
            </a>
            <a href="http://504found.org/">
              <Image
                src="/images/504foundlogo.png"
                alt="504 Found"
                width={48}
                height={48}
                className="h-12 w-12 rounded-full object-cover transition-all duration-300 hover:scale-125"
              />
            </a>
            <Image
              src="/images/partnerlogo.jpg"
              alt="Thea"
              width={48}
              height={48}
              className="h-12 w-12 rounded-full object-cover transition-all duration-300 hover:scale-125"
            />
          </div>
        </div>
      </div>
      <div className="mt-8 text-center">
        <Link href="/customer-care/terms-of-service">
          <Button variant="link" className="text-black hover:text-[#BFDBF7]">
            Terms of Service
          </Button>
        </Link>
        <Link href="/customer-care/privacy-policy">
          <Button variant="link" className="ml-4 text-black hover:text-[#BFDBF7]">
            Privacy Policy
          </Button>
        </Link>
      </div>
    </footer>
  );
}
