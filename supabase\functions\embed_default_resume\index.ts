/// <reference lib="deno.ns" />
import { serve } from "std/http/server.ts";
import { createClient } from "@supabase/supabase-js";

serve(async (req) => {
  try {
    const { record: rec, old_record: old } = await req.json();

    // only run when default_resume_url actually changes
    if (!old || rec.default_resume_url === old.default_resume_url) {
      return new Response("no-op", { status: 200 });
    }

    // parse out bucket + key from the public URL
    const url = new URL(rec.default_resume_url as string);
    const parts = url.pathname.split("/").filter(Boolean);
    const bucket = parts[4];              // "resumes"
    const key    = parts.slice(5).join("/"); // "<userId>/<filename>"

    const admin = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );

    // get signed URL
    const { data: signData, error: signErr } = await admin
      .storage.from(bucket)
      .createSignedUrl(key, 300);
    if (signErr) throw signErr;

    // download the PDF
    const pdfRes = await fetch(signData.signedUrl!);
    if (!pdfRes.ok) throw new Error("download failed");
    const bytes = new Uint8Array(await pdfRes.arrayBuffer());

    // send to your vector service
    const form = new FormData();
    form.append("file", new File([bytes], "resume.pdf", { type: "application/pdf" }));

    const vectorBase = Deno.env.get("VECTOR_API")!;
    const embedRes = await fetch(`${vectorBase}/embed-pdf`, {
      method: "POST",
      body: form,
    });
    if (!embedRes.ok) {
      const txt = await embedRes.text();
      throw new Error(`vector error: ${txt}`);
    }
    const { embedding } = await embedRes.json();

    // write back to profiles
    const { error: dbErr } = await admin
      .from("profiles")
      .update({
        resume_embedding: embedding,
        resume_emb_updated_at: new Date().toISOString(),
      })
      .eq("id", rec.id);
    if (dbErr) throw dbErr;

    return new Response("embedded", { status: 200 });
  } catch (err: any) {
    console.error(err);
    return new Response(JSON.stringify({ error: err.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
});
