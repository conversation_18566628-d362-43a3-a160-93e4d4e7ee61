'use client';

import React, { FormEvent, useEffect } from 'react';
import { supabase } from '../../../supabase/supabaseClient'; // Adjust the path as needed
import Navbar from '@/components/Navbar';
import AdNavbar from '@/components/NavbarAd';
import Footer from '@/components/Footer';
import { motion } from 'framer-motion';

type Testimonial = {
  quote: string;
  author: string;
  role: string;
};

const testimonials: Testimonial[] = [
  {
    quote: "<PERSON><PERSON><PERSON> is intuitive to use - I only had to click once to find a useful clinic",
    author: "<PERSON>",
    role: "Co-Founder @ Runway Mobile"
  },
  {
    quote: "Amazing UI, super helpful for anyone trying to get into medicine. Clean and intuitive, great design. I could find any clinic I want on here.",
    author: "<PERSON>",
    role: "Co-Founder @ Runway Mobile"
  },
  {
    quote: "<PERSON><PERSON><PERSON> is amazing for finding opportunities for healthcare. It's really that 'first step' in bridging the gap between education and the medical field.",
    author: "<PERSON><PERSON><PERSON>",
    role: "Co-Founder @ Thryving"
  }
  // Add more testimonials here if needed.
];

export default function Reviews() {
  // Validate that the user has entered a full name
  const validateForm = (e: FormEvent<HTMLFormElement>) => {
    const form = e.target as HTMLFormElement;
    const nameInput = form.elements.namedItem('name') as HTMLInputElement;
    const name = nameInput.value.trim();
    if (name.split(' ').length < 2) {
      alert('Please enter your full name (first and last name).');
      return false;
    }
    return true;
  };

  // Handle the form submission to insert the testimonial into Supabase
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm(e)) {
      const form = e.target as HTMLFormElement;
      const formData = new FormData(form);
      const testimonialData = Object.fromEntries(formData.entries());

      // Create an object matching our table columns
      const testimonial = {
        name: testimonialData.name,
        age: Number(testimonialData.age),
        email: testimonialData.email,
        clinic: testimonialData.clinic,
        school: testimonialData.school || '', // optional
        testimonial: testimonialData.testimonial,
        agreement: testimonialData.agreement === 'on' // checkbox returns 'on'
      };

      try {
        const { error } = await supabase
          .from('testimonials')
          .insert(testimonial);
        if (error) throw error;
        alert('Thank you for your testimonial!');
        form.reset();
      } catch (error) {
        console.error('Error adding testimonial: ', error);
        alert('There was an error submitting your testimonial. Please try again.');
      }
    }
  };

  useEffect(() => {
    document.title = 'Klinn | Testimonials';
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-[#1E3A8A] via-[#3B82F6] to-[#93C5FD] font-rubik">
      <Navbar />
      <main className="flex-grow flex items-center justify-center p-6 pt-24 min-h-screen mt-20">
        <div className="container mx-auto flex flex-col lg:flex-row items-start justify-between space-y-12 lg:space-y-0 lg:space-x-12 -mt-20">
          <div className="lg:w-2/5 w-full">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
            >
              <h1 className="text-5xl lg:text-6xl font-light mb-6 text-white">Klinn Testimonials</h1>
              <p className="text-xl mb-8 text-white/90 font-light">
                See what our users have to say about Klinn! Your feedback is invaluable to us and helps future students understand the impact of Klinn.
              </p>
            </motion.div>

            {/* Testimonial List */}
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  className="bg-white/10 backdrop-blur-md rounded-lg p-6 shadow-lg border border-white/20 hover:border-white/40 transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 * index }}
                >
                  <blockquote className="text-white/90 italic text-lg mb-4 font-light">
                    &quot;{testimonial.quote}&quot;
                  </blockquote>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-light mr-3">
                      {testimonial.author.charAt(0)}
                    </div>
                    <div>
                      <p className="text-white font-light">{testimonial.author}</p>
                      <p className="text-white/70 text-sm font-light">{testimonial.role}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
          
          {/* Form Section */}
          <motion.div
            className="lg:w-3/5 w-full lg:pl-12 mt-8 lg:mt-0"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            <h2 className="mt-24 text-3xl font-light mb-6 text-white text-center lg:text-left">Share Your Experience</h2>
            <form id="form" onSubmit={handleSubmit} className="space-y-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-thin text-white/90">Full Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    placeholder="John Doe"
                    required
                    className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 bg-white/5 text-white placeholder-white/50 transition duration-300"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="age" className="text-sm font-thin text-white/90">Age</label>
                  <input
                    type="number"
                    id="age"
                    name="age"
                    placeholder="25"
                    max="99"
                    required
                    className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 bg-white/5 text-white placeholder-white/50 transition duration-300"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-thin text-white/90">Email Address</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="<EMAIL>"
                    required
                    className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 bg-white/5 text-white placeholder-white/50 transition duration-300"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="Title" className="text-sm font-thin text-white/90">Title</label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    placeholder="Student"
                    required
                    className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 bg-white/5 text-white placeholder-white/50 transition duration-300"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label htmlFor="school" className="text-sm font-thin text-white/90">School/Organization (if applicable)</label>
                <input
                  type="text"
                  id="school"
                  name="school"
                  placeholder="State University"
                  className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 bg-white/5 text-white placeholder-white/50 transition duration-300"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="testimonial" className="text-sm font-thin text-white/90">Your Klinn Experience</label>
                <textarea
                  id="testimonial"
                  name="testimonial"
                  placeholder="How has Klinn helped you?"
                  required
                  className="w-full px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 bg-white/5 text-white placeholder-white/50 transition duration-300 h-24 resize-none"
                ></textarea>
              </div>
              <div className="flex items-start space-x-2">
                <input
                  type="checkbox"
                  id="agreement"
                  name="agreement"
                  required
                  className="mt-1"
                />
                <label htmlFor="agreement" className="text-sm text-white/80">
                  I agree to my testimonial being used on Klinn&apos;s website or marketing materials.
                </label>
              </div>
              <button
                type="submit"
                className="font-light w-full bg-white text-[#1E3A8A] py-3 rounded-lg hover:bg-opacity-90 transition duration-300"
              >
                Submit Testimonial
              </button>
            </form>
          </motion.div>
        </div>
      </main>
      <Footer className="bg-transparent mt-auto" />
    </div>
  );
}
