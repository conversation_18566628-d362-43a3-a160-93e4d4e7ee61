import { SubscriptionPlan } from './subscription';

// Define the limits for each plan
export const PLAN_LIMITS = {
  free: {
    coldCallScripts: 1,
    tokens: 5,
    nonmedECs: 30,
    medicalECs: 30,
    jobApplications: 5,
    aiResumeScorer: 0,
    priorityApplicant: false,
    customProfileBanner: false,
    earlyAccessJobs: 0, // hours
    aiJobMatching: false,
    automatedEmails: 3,
    maxEmails: 100,
    newsletter: false,
  },
  basic_monthly: {
    coldCallScripts: 3,
    tokens: 15,
    nonmedECs: 60,
    medicalECs: 60,
    jobApplications: 30,
    aiResumeScorer: 2,
    priorityApplicant: false,
    customProfileBanner: true,
    earlyAccessJobs: 24, // hours
    aiJobMatching: false,
    automatedEmails: 15,
    maxEmails: 150,
    newsletter: true,
  },
  basic_yearly: {
    coldCallScripts: 3,
    tokens: 15,
    nonmedECs: 60,
    medicalECs: 60,
    jobApplications: 30,
    aiResumeScorer: 2,
    priorityApplicant: false,
    customProfileBanner: true,
    earlyAccessJobs: 24, // hours
    aiJobMatching: false,
    automatedEmails: 15,
    maxEmails: 150,
    newsletter: true,
  },
  premium_monthly: {
    coldCallScripts: 10,
    tokens: 50,
    nonmedECs: 999999, // effectively unlimited
    medicalECs: 999999, // effectively unlimited
    jobApplications: 999999, // effectively unlimited
    aiResumeScorer: 5,
    priorityApplicant: true,
    customProfileBanner: true,
    earlyAccessJobs: 48, // hours
    aiJobMatching: true,
    automatedEmails: 25,
    maxEmails: 200,
    newsletter: true,
  },
  premium_yearly: {
    coldCallScripts: 10,
    tokens: 50,
    nonmedECs: 999999, // effectively unlimited
    medicalECs: 999999, // effectively unlimited
    jobApplications: 999999, // effectively unlimited
    aiResumeScorer: 5,
    priorityApplicant: true,
    customProfileBanner: true,
    earlyAccessJobs: 48, // hours
    aiJobMatching: true,
    automatedEmails: 25,
    maxEmails: 200,
    newsletter: true,
  },
};

// Type for usage tracking
export interface UserUsage {
  coldCallScripts?: number;
  tokens?: number;
  jobApplications?: number;
  aiResumeScorer?: number;
  automatedEmails?: number;
  nonmedECs?: number;
  medicalECs?: number;
}

// Function to check if a user has reached their limit for a specific feature
export function hasReachedLimit(
  planType: SubscriptionPlan,
  feature: keyof typeof PLAN_LIMITS.free,
  currentUsage: number
): boolean {
  const limits = PLAN_LIMITS[planType] || PLAN_LIMITS.free;
  const limit = limits[feature];

  // For boolean features, they either have access (false = not reached limit) or don't (true = reached limit)
  if (typeof limit === 'boolean') {
    return !limit;
  }

  // For numeric features, check if usage has reached or exceeded the limit
  return currentUsage >= limit;
}

// Function to get the limit for a specific feature
export function getLimit(
  planType: SubscriptionPlan,
  feature: keyof typeof PLAN_LIMITS.free
): number | boolean {
  const limits = PLAN_LIMITS[planType] || PLAN_LIMITS.free;
  return limits[feature];
}

// Function to get remaining usage for a specific feature
export function getRemainingUsage(
  planType: SubscriptionPlan,
  feature: keyof typeof PLAN_LIMITS.free,
  currentUsage: number
): number {
  const limits = PLAN_LIMITS[planType] || PLAN_LIMITS.free;
  const limit = limits[feature];

  // Only applicable for numeric features
  if (typeof limit === 'number') {
    return Math.max(0, limit - currentUsage);
  }

  return 0;
}

// Function to check if a user has access to a specific feature
export function hasAccess(
  planType: SubscriptionPlan,
  feature: keyof typeof PLAN_LIMITS.free
): boolean {
  const limits = PLAN_LIMITS[planType] || PLAN_LIMITS.free;
  const limit = limits[feature];

  // For boolean features, the value directly indicates access
  if (typeof limit === 'boolean') {
    return limit;
  }

  // For numeric features, any value > 0 indicates access
  return limit > 0;
}

// Function to increment usage for a specific feature
export async function incrementUsage(
  userId: string,
  feature: keyof UserUsage,
  amount: number = 1
): Promise<boolean> {
  try {
    // This would typically update a usage table in your database
    // For now, we'll just log it
    console.log(`Incrementing ${feature} usage for user ${userId} by ${amount}`);

    // In a real implementation, you would:
    // 1. Get the current usage from the database
    // 2. Increment it by the specified amount
    // 3. Update the database
    // 4. Return true if successful

    return true;
  } catch (error) {
    console.error(`Error incrementing usage for ${feature}:`, error);
    return false;
  }
}

// Function to get current usage for a specific feature
export async function getCurrentUsage(
  userId: string,
  feature: keyof UserUsage
): Promise<number> {
  try {
    // This would typically query a usage table in your database
    // For now, we'll just return a placeholder value
    console.log(`Getting current ${feature} usage for user ${userId}`);

    // In a real implementation, you would:
    // 1. Query the database for the current usage
    // 2. Return the usage value

    return 0; // Placeholder
  } catch (error) {
    console.error(`Error getting current usage for ${feature}:`, error);
    return 0;
  }
}
