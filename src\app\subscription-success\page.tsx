'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { motion } from 'framer-motion';

const SubscriptionSuccessPage = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subscription, setSubscription] = useState<any>(null);

  useEffect(() => {
    document.title = 'Klinn | Subscription Activated';

    const sessionId = searchParams.get('session_id');
    if (!sessionId) {
      setError('No session ID found');
      setLoading(false);
      return;
    }

    // Fetch subscription details with retries
    const fetchSubscriptionDetails = async (retryCount = 0) => {
      try {
        // First try to verify the subscription directly with Stripe
        const timestamp = new Date().getTime();
        const verifyResponse = await fetch(`/api/verify-subscription?t=${timestamp}`);

        if (verifyResponse.ok) {
          const verifyData = await verifyResponse.json();

          if (verifyData.verified && verifyData.planType !== 'free') {
            console.log('Subscription verified with Stripe:', verifyData);

            // Use the verified data
            setSubscription({
              planType: verifyData.planType,
              status: verifyData.status,
              currentPeriodEnd: verifyData.currentPeriodEnd ? new Date(verifyData.currentPeriodEnd) : undefined
            });
            setLoading(false);
            return;
          }
        }

        // Fall back to the regular subscription API
        const response = await fetch(`/api/subscription?t=${timestamp}`);
        if (!response.ok) {
          throw new Error('Failed to fetch subscription details');
        }
        const data = await response.json();

        // Check if subscription data shows the correct plan
        // If still showing free plan but we have a subscription ID, retry
        if (data.subscription.planType === 'free' && data.subscription.stripeSubscriptionId && retryCount < 5) {
          console.log(`Subscription still showing as free plan, retrying (${retryCount + 1}/5)...`);
          setTimeout(() => fetchSubscriptionDetails(retryCount + 1), 2000);
          return;
        }

        setSubscription(data.subscription);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching subscription:', err);

        // Retry a few times if there's an error
        if (retryCount < 5) {
          console.log(`Error fetching subscription, retrying (${retryCount + 1}/5)...`);
          setTimeout(() => fetchSubscriptionDetails(retryCount + 1), 2000);
        } else {
          setError('Failed to load subscription details');
          setLoading(false);
        }
      }
    };

    fetchSubscriptionDetails();

    // Set up polling to check for subscription updates, but only if we're still loading
    let interval: NodeJS.Timeout | null = null;

    if (loading) {
      interval = setInterval(() => {
        // Use a timestamp to prevent caching
        const timestamp = new Date().getTime();
        fetch(`/api/verify-subscription?t=${timestamp}`)
          .then(response => response.json())
          .then(data => {
            console.log('Periodic subscription check on success page:', data);
            if (data.verified && data.planType !== 'free') {
              // If we have verified data, update the subscription and stop loading
              setSubscription({
                planType: data.planType,
                status: data.status,
                currentPeriodEnd: data.currentPeriodEnd ? new Date(data.currentPeriodEnd) : undefined
              });
              setLoading(false);
            }
          })
          .catch(error => {
            console.error('Error in periodic subscription check:', error);
          });
      }, 5000); // Check every 5 seconds
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [searchParams]); // Remove loading from dependencies

  const getPlanName = (planType: string) => {
    switch (planType) {
      case 'basic_monthly':
        return 'Basic Monthly';
      case 'basic_yearly':
        return 'Basic Yearly';
      case 'premium_monthly':
        return 'Premium Monthly';
      case 'premium_yearly':
        return 'Premium Yearly';
      default:
        return 'Free';
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      <Navbar />

      <main className="flex-grow pt-32 pb-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-2xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="w-10 h-10 text-green-500" />
              </div>
            </div>

            <h1 className="text-3xl font-extralight mb-4 text-gray-800">Subscription Activated!</h1>

            {loading ? (
              <p className="text-gray-500 mb-8">Loading your subscription details...</p>
            ) : error ? (
              <div className="text-red-500 mb-8">
                <p>{error}</p>
                <p className="mt-2 text-sm">Don&apos;t worry, your subscription has been processed. You can check your account details for more information.</p>
              </div>
            ) : (
              <div className="mb-8">
                <p className="text-gray-500 mb-4">
                  Thank you for subscribing to Klinn. Your {getPlanName(subscription?.planType || 'free')} plan is now active.
                </p>
                <div className="bg-blue-50 p-6 rounded-lg mt-6">
                  <h3 className="text-lg font-extralight mb-2 text-blue-700">Your Subscription Details</h3>
                  <p className="text-blue-600 mb-1">Plan: {getPlanName(subscription?.planType || 'free')}</p>
                  <p className="text-blue-600 mb-1">Status: {subscription?.status || 'active'}</p>
                  {subscription?.currentPeriodEnd && (
                    <p className="text-blue-600">
                      Next billing date: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button
                className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6 py-2 font-extralight"
                onClick={() => router.push('/profile-dashboard')}
              >
                Go to Dashboard
              </Button>
              <Button
                variant="outline"
                className="border-blue-600 text-blue-600 hover:bg-blue-50 rounded-full px-6 py-2 font-extralight"
                onClick={() => router.push('/jobs')}
              >
                Explore Jobs
              </Button>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer className="bg-blue-600 text-white py-10" />
    </div>
  );
};

export default SubscriptionSuccessPage;
