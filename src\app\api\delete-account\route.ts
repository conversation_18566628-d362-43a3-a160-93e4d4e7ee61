// app/api/delete-account/route.ts
import { NextResponse } from "next/server";
import { createRouteClient } from "../../../../supabase/serversupabaseClient"; // adjust path if needed

export async function DELETE(request: Request) {
  const supabase = createRouteClient();

  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  const userId = session.user.id;

  try {
    // 1. Fetch the user's profile to get email and token count.
    const { data: userProfile, error: profileError } = await supabase
      .from("profiles")
      .select("email, tokens")
      .eq("id", userId)
      .single();

    if (profileError) throw profileError;

    // 2. Archive the user's tokens: upsert so that if a record exists, update its tokens.
    if (userProfile) {
      const { error: archiveError } = await supabase
        .from("archived_users")
        .upsert(
          {
            email: userProfile.email.toLowerCase(), // ensure consistent casing
            tokens: userProfile.tokens,
          },
          { onConflict: "email" }
        );
      if (archiveError) throw archiveError;
    }

    // 3. Delete records from various tables.
    const deletionQueries = [
      supabase.from("bookmarks").delete().eq("user_id", userId),
      supabase.from("clinic_openings").delete().eq("user_id", userId),
      supabase.from("feedback").delete().eq("id", userId),
      supabase.from("job_applicants").delete().eq("applicant_id", userId),
      supabase.from("profiles").delete().eq("id", userId),
      supabase.from("reports").delete().eq("id", userId),
      supabase.from("testimonials").delete().eq("id", userId),
      supabase.from("user_unlocks").delete().eq("user_id", userId),
      // user_oauth_tokens2 will delete automatically due to ON DELETE CASCADE
    ];

    for (const query of deletionQueries) {
      const { error } = await query;
      if (error) throw error;
    }

    // 4. Delete the user's storage files (e.g. resumes).
    const { createClient } = await import("@supabase/supabase-js");
    const supabaseService = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data: files, error: listError } = await supabaseService.storage
      .from("resumes")
      .list(userId);
    if (listError) throw listError;

    if (files && files.length > 0) {
      const paths = files.map((file) => `${userId}/${file.name}`);
      const { error: removeError } = await supabaseService.storage
        .from("resumes")
        .remove(paths);
      if (removeError) throw removeError;
    }

    // 5. Delete the user from Supabase Auth using the admin method.
    const { error: authDeleteError } = await supabaseService.auth.admin.deleteUser(userId);
    if (authDeleteError) throw authDeleteError;

    return NextResponse.json({ message: "Account deleted successfully" }, { status: 200 });
  } catch (error) {
    console.error("Error deleting account:", error);
    return NextResponse.json({ error: "An error occurred while deleting the account" }, { status: 500 });
  }
}
