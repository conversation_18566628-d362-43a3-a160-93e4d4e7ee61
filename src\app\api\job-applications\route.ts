import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { checkSubscriptionLimits } from "@/lib/middleware/check-subscription-limits";
import { incrementUserUsage } from "@/lib/db/usage-tracking";

export async function POST(req: NextRequest) {
  try {
    // Check subscription limits
    const { allowed, response, userId, currentUsage, newUsage } = await checkSubscriptionLimits(
      req,
      "jobApplications",
      1
    );

    if (!allowed || !userId) {
      return response || NextResponse.json({ error: "Access denied" }, { status: 403 });
    }

    // Parse request body
    const { jobId, coverLetter, resume } = await req.json();

    if (!jobId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get the supabase client
    const supabase = createRouteHandlerClient({ cookies });
    
    // Check if the job exists
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('id, title, company')
      .eq('id', jobId)
      .single();
    
    if (jobError || !job) {
      console.error('Error fetching job:', jobError);
      return NextResponse.json({ error: "Job not found" }, { status: 404 });
    }
    
    // Check if the user has already applied to this job
    const { data: existingApplication, error: applicationError } = await supabase
      .from('job_applications')
      .select('id')
      .eq('user_id', userId)
      .eq('job_id', jobId)
      .single();
    
    if (existingApplication) {
      return NextResponse.json({ error: "You have already applied to this job" }, { status: 400 });
    }
    
    // Create the job application
    const { data: application, error: createError } = await supabase
      .from('job_applications')
      .insert([
        {
          user_id: userId,
          job_id: jobId,
          cover_letter: coverLetter,
          resume_id: resume?.id,
          status: 'submitted',
        }
      ])
      .select()
      .single();
    
    if (createError) {
      console.error('Error creating job application:', createError);
      return NextResponse.json({ error: "Failed to create job application" }, { status: 500 });
    }

    // Increment usage
    await incrementUserUsage(userId, "jobApplications", 1);

    // Return the created application
    return NextResponse.json({
      application,
      usageInfo: {
        feature: "jobApplications",
        current: currentUsage,
        new: newUsage,
      },
    });
  } catch (error) {
    console.error("Error creating job application:", error);
    return NextResponse.json(
      { error: "Failed to create job application" },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Get the current user from the session
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 });
    }
    
    const userId = session.user.id;
    
    // Get the user's job applications
    const { data: applications, error } = await supabase
      .from('job_applications')
      .select(`
        *,
        jobs:job_id (
          id,
          title,
          company,
          location,
          job_type,
          salary_range,
          posted_date
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching job applications:', error);
      return NextResponse.json({ error: "Failed to fetch job applications" }, { status: 500 });
    }
    
    return NextResponse.json({ applications });
  } catch (error) {
    console.error('Error in GET job applications:', error);
    return NextResponse.json({ error: "Server error" }, { status: 500 });
  }
}
