// "use client";

// import React from "react";
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogHeader,
//   DialogTitle,
//   DialogFooter,
// } from "@/components/ui/dialog";
// import { <PERSON>ton } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Textarea } from "@/components/ui/textarea";
// import { Label } from "@/components/ui/label";
// import { Mail, Send } from "lucide-react";

// export interface EmailDraft {
//   recipient: string;
//   subject: string;
//   message: string;
// }

// interface EmailDialogProps {
//   open: boolean;
//   setOpen: (open: boolean) => void;
//   emailDraft: EmailDraft;
//   setEmailDraft: (draft: EmailDraft) => void;
//   handleSendEmail: () => void;
//   isAuthenticating: boolean;
//   isSending: boolean;
//   googleAccessToken: string;
//   selectedContact: any;
// }

// const EmailDialog: React.FC<EmailDialogProps> = ({
//   open,
//   setOpen,
//   emailDraft,
//   setEmailDraft,
//   handleSendEmail,
//   isAuthenticating,
//   isSending,
//   googleAccessToken,
//   selectedContact,
// }) => {
//   return (
//     <Dialog open={open} onOpenChange={setOpen}>
//       <DialogContent className="sm:max-w-[600px]">
//         <DialogHeader>
//           <DialogTitle className="flex items-center gap-2">
//             <Mail className="h-5 w-5 text-blue-500" />
//             <span>Compose Your Message</span>
//           </DialogTitle>
//           {selectedContact && (
//             <DialogDescription>
//               {selectedContact.type === "research"
//                 ? `Reaching out to ${selectedContact.university} Researcher`
//                 : selectedContact.type === "startup"
//                 ? `Inquiring about ${selectedContact.company}`
//                 : `Inquiring about ${selectedContact.name}`}
//             </DialogDescription>
//           )}
//         </DialogHeader>

//         <div className="space-y-4 mt-4">
//           <div className="space-y-2">
//             <Label htmlFor="recipient">Recipient Email</Label>
//             <Input
//               id="recipient"
//               placeholder="Enter email address"
//               value={emailDraft.recipient}
//               onChange={(e) => setEmailDraft({ ...emailDraft, recipient: e.target.value })}
//             />
//           </div>

//           <div className="space-y-2">
//             <Label htmlFor="subject">Subject</Label>
//             <Input
//               id="subject"
//               placeholder="Enter message subject"
//               value={emailDraft.subject}
//               onChange={(e) => setEmailDraft({ ...emailDraft, subject: e.target.value })}
//             />
//           </div>

//           <div className="space-y-2">
//             <Label htmlFor="message">Message</Label>
//             <Textarea
//               id="message"
//               placeholder="Write your message here..."
//               className="min-h-[200px]"
//               value={emailDraft.message}
//               onChange={(e) => setEmailDraft({ ...emailDraft, message: e.target.value })}
//             />
//           </div>
//         </div>

//         <DialogFooter className="mt-4">
//           <Button variant="outline" onClick={() => setOpen(false)}>
//             Cancel
//           </Button>
//           <Button
//             onClick={handleSendEmail}
//             disabled={
//               !emailDraft.recipient ||
//               !emailDraft.subject ||
//               !emailDraft.message ||
//               isAuthenticating ||
//               isSending
//             }
//           >
//             {isAuthenticating ? (
//               <>
//                 <span className="animate-spin mr-2">●</span>
//                 Authenticating...
//               </>
//             ) : !googleAccessToken ? (
//               <>Connect Google to Send</>
//             ) : isSending ? (
//               <>
//                 <span className="animate-spin mr-2">●</span>
//                 Sending...
//               </>
//             ) : (
//               <>
//                 <Send className="h-4 w-4 mr-2" />
//                 Send Message
//               </>
//             )}
//           </Button>
//         </DialogFooter>
//       </DialogContent>
//     </Dialog>
//   );
// };

// export default EmailDialog;
