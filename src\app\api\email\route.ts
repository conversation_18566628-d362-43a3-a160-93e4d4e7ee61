import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

interface EmailData {
    from: string;
    to: string[];
    subject: string;
    contents: string;
}

export async function POST(request: Request) {
  try {
    const { from, to, subject, contents } = await request.json() as EmailData;
    const { data, error } = await resend.emails.send({
      from: from,
      to: to,
      subject: subject,
      html: contents,
    });

    if (error) {
      return Response.json({ error }, { status: 500 });
    }

    return Response.json(data);
  } catch (error) {
    return Response.json({ error }, { status: 500 });
  }
}