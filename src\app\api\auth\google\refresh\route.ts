import { NextRequest, NextResponse } from "next/server";
import { createRoute<PERSON><PERSON>lerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

export async function GET(req: NextRequest) {
  const supabase = createRouteHandlerClient({ cookies });

  /* ------------------------------------------------------------------
   * 1. Who’s calling?
   * ------------------------------------------------------------------ */
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  /* ------------------------------------------------------------------
   * 2. Load the stored token row
   * ------------------------------------------------------------------ */
  const { data: token, error } = await supabase
    .from("user_oauth_tokens2")
    .select("*")
    .eq("user_id", user.id)
    .eq("provider", "google")
    .maybeSingle();

  if (error || !token) {
    return NextResponse.json(
      { error: "Token record not found" },
      { status: 404 }
    );
  }

  /* ------------------------------------------------------------------
   * 3. Still fresh? → return it
   * ------------------------------------------------------------------ */
  const now = Date.now();
  const expiresAt = new Date(token.expires_at).getTime();
  if (now < expiresAt - 60_000) {
    return NextResponse.json({ access_token: token.access_token });
  }

  /* ------------------------------------------------------------------
   * 4. Need to refresh
   * ------------------------------------------------------------------ */
  if (!token.refresh_token) {
    // we shouldn’t get here, but be defensive
    return NextResponse.json(
      { code: "reauth", error: "No refresh token" },
      { status: 401 }
    );
  }

  const res = await fetch("https://oauth2.googleapis.com/token", {
    method: "POST",
    headers: { "Content-Type": "application/x-www-form-urlencoded" },
    body: new URLSearchParams({
      client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || "",
      client_secret: process.env.GOOGLE_CLIENT_SECRET || "",
      refresh_token: token.refresh_token,
      grant_type: "refresh_token",
    }),
  });

  const body = await res.json();

  /* ------------------------------------------------------------------
   * 5. Google says “no”  →  token is revoked (7-day rule, 6-month rule, etc.)
   * ------------------------------------------------------------------ */
  if (!res.ok || !body.access_token) {
    // clean up so we don’t keep trying a dead token
    await supabase
      .from("user_oauth_tokens2")
      .delete()
      .eq("user_id", user.id)
      .eq("provider", "google");

    return NextResponse.json(
      { code: "reauth", error: "Refresh token revoked", details: body },
      { status: 401 }
    );
  }

  /* ------------------------------------------------------------------
   * 6. Success  →  store & return the fresh access-token
   * ------------------------------------------------------------------ */
  const newExpiry = new Date(Date.now() + body.expires_in * 1000).toISOString();

  await supabase
    .from("user_oauth_tokens2")
    .update({
      access_token: body.access_token,
      expires_at: newExpiry,
      // If Google ever returns a new refresh_token, save it:
      // refresh_token: body.refresh_token ?? token.refresh_token,
      last_used_at: new Date().toISOString(),
    })
    .eq("user_id", user.id)
    .eq("provider", "google");

  return NextResponse.json({ access_token: body.access_token });
}
