"use client";
import React, { Suspense } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import ClinicSearch from "@/components/ClinicSearch";

export default function Results() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-300 to-indigo-100">
      <Navbar />
      {/* <div className="relative bottom-0 left-0 right-0 h-12 sm:h-24 bg-gradient-to-t from-transparent to-blue-300"></div> */}
      <main className="flex-grow py-4 sm:py-8">
        <div className="container mx-auto px-4 sm:px-32">
          {/* Page Header */}
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-8 w-full max-w-7xl mx-auto">
            <h1 className="text-3xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-b from-blue-500 to-blue-400 mb-4 sm:mb-0">
              Search Results
            </h1>
            <Link href="/clinic-search" className="w-full sm:w-auto">
              <Button
                variant="outline"
                className="flex items-center justify-center space-x-2 text-blue-500 border-blue-300 hover:bg-blue-50 transition-all duration-300 w-full sm:max-w-xs"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>New Search</span>
              </Button>
            </Link>
          </div>

          {/* Clinic Results or No Results */}
          <Suspense fallback={<div>Loading...</div>}>
            <ClinicSearch />
          </Suspense>
        </div>
      </main>
      <div className="relative bottom-0 left-0 right-0 h-12 sm:h-24 bg-gradient-to-b from-transparent to-blue-300"></div>
      <Footer />
    </div>
  );
}