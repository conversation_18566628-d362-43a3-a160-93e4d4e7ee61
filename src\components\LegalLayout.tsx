import React from 'react';
import AdNavbar from '@/components/NavbarAd';
import Footer from '@/components/Footer';
import Navbar from './Navbar';

interface LegalLayoutProps {
  children: React.ReactNode;
  title: string;
  lastUpdated: string;
}

const LegalLayout: React.FC<LegalLayoutProps> = ({ children, title, lastUpdated }) => {
  return (
    <div className="flex flex-col min-h-screen bg-blue-300">
      <Navbar />
      <main className="flex-grow py-32"> 
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-semibold mb-8 text-black text-center">{title}</h1> 
          <p className="text-center text-black mb-8">Effective Date: {lastUpdated}</p> 
          <div>
            {children}
          </div>
        </div>
      </main>
      <Footer className="bg-gradient-to-b from-blue-300 to-blue-600 p-8 transition-all duration-300 ease-in-out" />
    </div>
  );
};

export default LegalLayout;
