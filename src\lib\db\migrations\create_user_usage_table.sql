-- Create user_usage table for tracking feature usage
CREATE TABLE IF NOT EXISTS user_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  month INTEGER NOT NULL,
  year INTEGER NOT NULL,
  cold_call_scripts INTEGER DEFAULT 0,
  tokens INTEGER DEFAULT 0,
  job_applications INTEGER DEFAULT 0,
  ai_resume_scorer INTEGER DEFAULT 0,
  automated_emails INTEGER DEFAULT 0,
  nonmed_ecs INTEGER DEFAULT 0,
  medical_ecs INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, month, year)
);

-- Add RLS policies
ALTER TABLE user_usage ENABLE ROW LEVEL SECURITY;

-- Policy for users to read their own usage
CREATE POLICY user_usage_select_policy ON user_usage
  FOR SELECT USING (auth.uid() = user_id);

-- Policy for users to update their own usage (though this should typically be done by the server)
CREATE POLICY user_usage_update_policy ON user_usage
  FOR UPDATE USING (auth.uid() = user_id);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update the updated_at timestamp
CREATE TRIGGER update_user_usage_updated_at
BEFORE UPDATE ON user_usage
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS user_usage_user_id_month_year_idx ON user_usage (user_id, month, year);
