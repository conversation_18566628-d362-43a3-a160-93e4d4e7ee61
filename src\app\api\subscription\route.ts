import { NextRequest, NextResponse } from "next/server";
import { getUserSubscription, hasAccessToFeature, getFeatureLimit } from "@/lib/subscription";
import { createClient } from '@supabase/supabase-js';
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import Stripe from 'stripe';

// For admin operations that bypass RLS
const getSupabaseAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

const getStripeClient = () => {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
  if (!stripeSecretKey) {
    throw new Error('Missing Stripe secret key');
  }
  return new Stripe(stripeSecretKey, {
    apiVersion: '2025-05-28.basil',
  });
};

export const dynamic = 'force-dynamic';

export async function GET(req: NextRequest) {
  try {
    // Get the current user from the session using the route client
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('No active session found:', sessionError);
      return NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 });
    }

    const user = session.user;
    if (!user) {
      console.error('No user found in session');
      return NextResponse.json({ error: "Unauthorized - No user found" }, { status: 401 });
    }

    const userId = user.id;
    const url = new URL(req.url);
    const feature = url.searchParams.get("feature");

    // Use admin client to directly query the database for better reliability
    const adminClient = getSupabaseAdminClient();
    const { data: profile, error: profileError } = await adminClient
      .from('profiles')
      .select('plan_type, subscription_status, subscription_end_date, stripe_customer_id, stripe_subscription_id')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json({ error: "Error fetching user profile" }, { status: 500 });
    }

    // Create a subscription object from the profile data
    const directSubscription = {
      planType: (profile?.plan_type as any) || 'free',
      status: profile?.subscription_status || 'inactive',
      currentPeriodEnd: profile?.subscription_end_date ? new Date(profile.subscription_end_date) : undefined,
      stripeCustomerId: profile?.stripe_customer_id,
      stripeSubscriptionId: profile?.stripe_subscription_id,
    };

    console.log('User subscription data from DB:', {
      userId,
      planType: directSubscription.planType,
      status: directSubscription.status,
      endDate: directSubscription.currentPeriodEnd?.toISOString(),
    });

    // If the user has a Stripe subscription ID but the plan type is 'free',
    // check Stripe directly to get the correct plan type
    if (directSubscription.stripeSubscriptionId && (!directSubscription.planType || directSubscription.planType === 'free')) {
      try {
        const stripe = getStripeClient();
        const subscription = await stripe.subscriptions.retrieve(directSubscription.stripeSubscriptionId, {
          expand: ['items.data.price.product']
        });

        if (subscription.status === 'active' || subscription.status === 'trialing') {
          const productName = ((subscription.items.data[0].price.product as Stripe.Product).name || '').toLowerCase();

          if (productName.includes('premium') && productName.includes('monthly')) {
            directSubscription.planType = 'premium_monthly';
          } else if (productName.includes('premium') && productName.includes('yearly')) {
            directSubscription.planType = 'premium_yearly';
          } else if (productName.includes('basic') && productName.includes('monthly')) {
            directSubscription.planType = 'basic_monthly';
          } else if (productName.includes('basic') && productName.includes('yearly')) {
            directSubscription.planType = 'basic_yearly';
          }

          directSubscription.status = subscription.status;

          // Update the database with the correct plan type
          await adminClient
            .from('profiles')
            .update({
              plan_type: directSubscription.planType,
              subscription_status: directSubscription.status
            })
            .eq('id', userId);

          console.log('Updated subscription data from Stripe:', {
            userId,
            planType: directSubscription.planType,
            status: directSubscription.status
          });
        }
      } catch (error) {
        console.error('Error retrieving subscription from Stripe:', error);
      }
    }

    // Also get subscription using the helper function as a backup
    const subscription = await getUserSubscription(userId);

    if (feature) {
      // Use the direct subscription data if available, otherwise fall back to the helper function
      const featureSubscription = {
        planType: directSubscription.planType || subscription?.planType || "free",
        status: directSubscription.status || subscription?.status || "inactive",
        currentPeriodEnd: directSubscription.currentPeriodEnd || subscription?.currentPeriodEnd,
      };

      const hasAccess = hasAccessToFeature(featureSubscription, feature);
      const limit = getFeatureLimit(featureSubscription, feature);

      return NextResponse.json({
        hasAccess,
        limit,
        planType: featureSubscription.planType,
        status: featureSubscription.status,
      });
    }

    // Use the direct subscription data if available, otherwise fall back to the helper function
    const finalSubscription = {
      planType: directSubscription.planType || subscription?.planType || "free",
      status: directSubscription.status || subscription?.status || "inactive",
      currentPeriodEnd: directSubscription.currentPeriodEnd || subscription?.currentPeriodEnd,
      stripeCustomerId: directSubscription.stripeCustomerId || subscription?.stripeCustomerId,
      stripeSubscriptionId: directSubscription.stripeSubscriptionId || subscription?.stripeSubscriptionId,
    };

    return NextResponse.json({
      subscription: {
        planType: finalSubscription.planType,
        status: finalSubscription.status,
        currentPeriodEnd: finalSubscription.currentPeriodEnd,
        stripeCustomerId: finalSubscription.stripeCustomerId,
        stripeSubscriptionId: finalSubscription.stripeSubscriptionId,
        features: {
          coldCallScripts: {
            hasAccess: hasAccessToFeature(finalSubscription, 'coldCallScripts'),
            limit: getFeatureLimit(finalSubscription, 'coldCallScripts'),
          },
          tokens: {
            hasAccess: hasAccessToFeature(finalSubscription, 'tokens'),
            limit: getFeatureLimit(finalSubscription, 'tokens'),
          },
          nonmedECs: {
            hasAccess: hasAccessToFeature(finalSubscription, 'nonmedECs'),
            limit: getFeatureLimit(finalSubscription, 'nonmedECs'),
          },
          medicalECs: {
            hasAccess: hasAccessToFeature(finalSubscription, 'medicalECs'),
            limit: getFeatureLimit(finalSubscription, 'medicalECs'),
          },
          jobApplications: {
            hasAccess: hasAccessToFeature(finalSubscription, 'jobApplications'),
            limit: getFeatureLimit(finalSubscription, 'jobApplications'),
          },
          aiResumeScorer: {
            hasAccess: hasAccessToFeature(finalSubscription, 'aiResumeScorer'),
            limit: getFeatureLimit(finalSubscription, 'aiResumeScorer'),
          },
          priorityApplicant: {
            hasAccess: hasAccessToFeature(finalSubscription, 'priorityApplicant'),
          },
          customProfileBanner: {
            hasAccess: hasAccessToFeature(finalSubscription, 'customProfileBanner'),
          },
          earlyAccessJobs: {
            hasAccess: hasAccessToFeature(finalSubscription, 'earlyAccessJobs'),
            limit: getFeatureLimit(finalSubscription, 'earlyAccessJobs'),
          },
          aiJobMatching: {
            hasAccess: hasAccessToFeature(finalSubscription, 'aiJobMatching'),
          },
          automatedEmails: {
            hasAccess: hasAccessToFeature(finalSubscription, 'automatedEmails'),
            limit: getFeatureLimit(finalSubscription, 'automatedEmails'),
          },
          maxEmails: {
            limit: getFeatureLimit(finalSubscription, 'maxEmails'),
          },
          newsletter: {
            hasAccess: hasAccessToFeature(finalSubscription, 'newsletter'),
          },
        }
      },
    });
  } catch (error) {
    console.error("Error fetching subscription:", error);
    return NextResponse.json(
      { error: "Failed to fetch subscription data", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}