import React from 'react';
import { SubscriptionLimitsDisplay } from './SubscriptionLimitsDisplay';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Info } from 'lucide-react';
import { useSubscriptionLimits } from '@/hooks/useSubscriptionLimits';
import { Button } from './ui/button';
import { useRouter } from 'next/navigation';

interface JobApplicationLimitsProps {
  showAlert?: boolean;
  className?: string;
}

export function JobApplicationLimits({ showAlert = true, className = '' }: JobApplicationLimitsProps) {
  const { checkLimit } = useSubscriptionLimits();
  const router = useRouter();
  
  const { hasReached, limit, remaining, hasAccess } = checkLimit('jobApplications');
  
  if (!showAlert) {
    return (
      <div className={className}>
        <SubscriptionLimitsDisplay
          feature="jobApplications"
          title="Job Applications"
          description="Track your monthly job application usage"
        />
      </div>
    );
  }
  
  if (hasReached) {
    return (
      <div className={className}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Application Limit Reached</AlertTitle>
          <AlertDescription>
            You&apos;ve reached your monthly limit of {limit} job applications.
            <Button 
              variant="link" 
              className="p-0 h-auto font-normal text-red-700 hover:text-red-800"
              onClick={() => router.push('/pricing')}
            >
              Upgrade your subscription
            </Button> to apply to more jobs.
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  if (remaining && remaining <= 3) {
    return (
      <div className={className}>
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Application Limit Almost Reached</AlertTitle>
          <AlertDescription>
            You have {remaining} job application{remaining === 1 ? '' : 's'} remaining this month. 
            <Button 
              variant="link" 
              className="p-0 h-auto font-normal"
              onClick={() => router.push('/pricing')}
            >
              Upgrade your subscription
            </Button> for more applications.
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return null;
}
