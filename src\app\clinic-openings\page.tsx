"use client";
import React, { useEffect } from 'react';
import Ad<PERSON><PERSON>bar from '@/components/NavbarAd';
import Footer from '@/components/Footer';
import JobBoard from '../../components/JobBoard';
import Navbar from '@/components/Navbar';

const ClinicOpenings = () => {
  useEffect(() => {
    document.title = 'Klinn | Medical Student Opportunities';
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-400 to-teal-200 relative">
      <Navbar />
      
      <main className="bg-transparent flex-grow container mx-auto px-4 py-12">
        
        <div className="flex-grow">
          <JobBoard />
        </div>
      </main>

      <Footer className="bg-transparent text-white p-8 transition-all duration-300 ease-in-out mt-auto pt-24" />
    </div>
  );
};

export default ClinicOpenings;