"use client"

import type React from "react"
import { useState } from "react"
import { Search } from "lucide-react"

interface FAQSearchProps {
  onSearch: (query: string) => void
}

export default function FAQSearch({ onSearch }: FAQSearchProps) {
  const [query, setQuery] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(query)
  }

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className="flex items-center bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-lg p-3">
        <Search className="text-blue-300 ml-2 mr-2" />
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search FAQs..."
          className="w-full bg-transparent border-none focus:outline-none text-white placeholder-blue-200"
        />
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
        >
          Search
        </button>
      </div>
    </form>
  )
}

