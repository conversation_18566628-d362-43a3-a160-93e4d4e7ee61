"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Bar<PERSON>hart3, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Target, CheckCircle2, Mail } from 'lucide-react'
import { useEffect, useState } from "react"
import { supabase } from "../../supabase/supabaseClient"
import { useAuth } from "@/context/AuthContext"

interface OutreachStats {
  total_contacts: number
  messages_sent: number
  responses_received: number
  response_rate: number
  weekly_stats: number[]
  monthly_stats: number[]
  by_category: {
    research: number
    internships: number
    startups: number
    clinics: number
    fulltime: number
    extracurriculars: number
  }
}

export default function OutreachStats() {
  const [stats, setStats] = useState<OutreachStats>({
    total_contacts: 0,
    messages_sent: 0,
    responses_received: 0,
    response_rate: 0,
    weekly_stats: [0, 0, 0, 0, 0, 0, 0],
    monthly_stats: [0, 0, 0],
    by_category: {
      research: 0,
      internships: 0,
      startups: 0,
      clinics: 0,
      fulltime: 0,
      extracurriculars: 0,
    },
  })
  const { user } = useAuth()

  useEffect(() => {
    const fetchStats = async () => {
      if (!user) return

      try {
        // Fetch email logs
        const { data: emailLogs, error: emailError } = await supabase
          .from("email_logs")
          .select("*")
          .eq("user_id", user.id)
          .eq("status", "success")

        if (emailError) throw emailError

        // Fetch unlocked contacts
        const { data: unlockedContacts, error: contactsError } = await supabase
          .from("user_unlocks")
          .select("listing_id, listing_type")
          .eq("user_id", user.id)

        if (contactsError) throw contactsError

        // Calculate stats
        const messagesSent = emailLogs?.length || 0
        const responsesReceived = emailLogs?.filter(log => log.response_received)?.length || 0
        const responseRate = messagesSent > 0 ? (responsesReceived / messagesSent) * 100 : 0

        // Calculate weekly stats (last 7 days)
        const weeklyStats = Array(7).fill(0)
        const today = new Date()
        emailLogs?.forEach(log => {
          const logDate = new Date(log.created_at)
          const daysDiff = Math.floor((today.getTime() - logDate.getTime()) / (1000 * 60 * 60 * 24))
          if (daysDiff < 7) {
            weeklyStats[6 - daysDiff]++
          }
        })

        // Calculate monthly stats (last 3 months)
        const monthlyStats = Array(3).fill(0)
        emailLogs?.forEach(log => {
          const logDate = new Date(log.created_at)
          const monthsDiff = (today.getFullYear() - logDate.getFullYear()) * 12 + 
            today.getMonth() - logDate.getMonth()
          if (monthsDiff < 3) {
            monthlyStats[2 - monthsDiff]++
          }
        })

        // Calculate category breakdown
        const categoryCounts = {
          research: 0,
          internships: 0,
          startups: 0,
          clinics: 0,
          fulltime: 0,
          extracurriculars: 0,
        }

        unlockedContacts?.forEach(contact => {
          const type = contact.listing_type?.toLowerCase()
          if (type in categoryCounts) {
            categoryCounts[type as keyof typeof categoryCounts]++
          }
        })

        setStats({
          total_contacts: unlockedContacts?.length || 0,
          messages_sent: messagesSent,
          responses_received: responsesReceived,
          response_rate: responseRate,
          weekly_stats: weeklyStats,
          monthly_stats: monthlyStats,
          by_category: categoryCounts,
        })
      } catch (error) {
        console.error("Error fetching outreach stats:", error)
      }
    }

    fetchStats()
  }, [user])

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Outreach Analytics</h2>
          <p className="text-gray-500 mt-1">Track your outreach performance and engagement</p>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700 px-3 py-1">
            <Mail className="h-3.5 w-3.5 mr-1.5" />
            <span>{stats.messages_sent} Messages Sent</span>
          </Badge>
          <Badge variant="outline" className="bg-green-50 border-green-200 text-green-700 px-3 py-1">
            <CheckCircle2 className="h-3.5 w-3.5 mr-1.5" />
            <span>{stats.response_rate}% Response Rate</span>
          </Badge>
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              <span>Outreach Statistics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="font-medium">Total Contacts</span>
                <span className="text-xl font-bold">{stats.total_contacts}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="font-medium">Messages Sent</span>
                <span className="text-xl font-bold">{stats.messages_sent}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                <span className="font-medium">Responses Received</span>
                <span className="text-xl font-bold">{stats.responses_received}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                <span className="font-medium">Response Rate</span>
                <span className="text-xl font-bold">{stats.response_rate}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LineChart className="h-5 w-5 text-blue-500" />
              <span>Weekly Activity</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[220px] flex items-end justify-between gap-2">
              {stats.weekly_stats.map((count, i) => (
                <div key={i} className="relative flex flex-col items-center flex-1">
                  <div
                    className="w-full bg-blue-500 rounded-t-md transition-all duration-500"
                    style={{
                      height: `${Math.max(count * 20, 4)}px`,
                      opacity: count > 0 ? 1 : 0.3,
                    }}
                  ></div>
                  <div className="absolute -top-6 text-sm font-medium text-blue-600">{count}</div>
                  <div className="mt-2 text-xs text-gray-500">
                    {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"][i]}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5 text-blue-500" />
            <span>Outreach by Category</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-6 gap-4">
            {Object.entries(stats.by_category).map(([category, count]) => (
              <div key={category} className="flex flex-col items-center">
                <div className="relative w-full h-4 bg-gray-100 rounded-full mb-2 overflow-hidden">
                  <div
                    className={`absolute left-0 top-0 h-full rounded-full ${
                      category === "research"
                        ? "bg-blue-500"
                        : category === "internships"
                          ? "bg-green-500"
                          : category === "startups"
                            ? "bg-purple-500"
                            : category === "clinics"
                              ? "bg-red-500"
                              : category === "fulltime"
                                ? "bg-orange-500"
                                : "bg-teal-500"
                    }`}
                    style={{ width: `${(count / stats.messages_sent) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium capitalize">{category}</span>
                <span className="text-xs text-gray-500">{count} emails</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-500" />
            <span>Outreach Goals</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm font-medium">Weekly outreach goal (15 emails)</span>
              <span className="text-sm text-gray-500">{stats.weekly_stats.reduce((a, b) => a + b, 0)}/15</span>
            </div>
            <Progress value={(stats.weekly_stats.reduce((a, b) => a + b, 0) / 15) * 100} className="h-2" />
          </div>

          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm font-medium">Response rate goal (40%)</span>
              <span className="text-sm text-gray-500">{stats.response_rate}%/40%</span>
            </div>
            <Progress value={(stats.response_rate / 40) * 100} className="h-2" />
          </div>

          <div>
            <div className="flex justify-between mb-2">
              <span className="text-sm font-medium">Research connections (20)</span>
              <span className="text-sm text-gray-500">{stats.by_category.research}/20</span>
            </div>
            <Progress value={(stats.by_category.research / 20) * 100} className="h-2" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-500" />
            <span>Monthly Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[220px] flex items-end justify-center gap-12">
            {stats.monthly_stats.map((count, i) => (
              <div key={i} className="relative flex flex-col items-center">
                <div
                  className="w-16 bg-blue-500 rounded-t-md transition-all duration-500"
                  style={{
                    height: `${Math.max(count * 5, 4)}px`,
                  }}
                ></div>
                <div className="absolute -top-6 text-sm font-medium text-blue-600">{count}</div>
                <div className="mt-2 text-sm font-medium text-gray-700">
                  {["January", "February", "March"][i]}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
