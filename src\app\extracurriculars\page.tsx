"use client"
import React, { useEffect, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, ChevronDown, BookmarkIcon, ChevronsDown, ArrowRight } from 'lucide-react';
import { Card } from '@/components/ui/card';
import Navbar from '@/components/Navbar';
import Link from 'next/link';
import Footer from '@/components/Footer';
import OpportunityCard from '@/components/OpportunityCard';
import OpportunitySubmissionForm from '@/components/OpportunitySubmissionForm';
import { SparklesCore } from '@/components/ui/sparkles';
import { ExtracurricularLimits } from '@/components/ExtracurricularLimits';
import { useSubscription } from '@/hooks/useSubscription';

// Firebase imports
import { useAuth } from '@/context/AuthContext';
import { supabase } from '../../../supabase/supabaseClient';

// // Initialize Supabase client
// const supabase = createClient(
//   process.env.NEXT_PUBLIC_SUPABASE_URL!,
//   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
// );

// ---------------------------------------------
// Interface & Helper Functions
// ---------------------------------------------
interface Opportunity {
  Name: string;
  Location: string;
  Ages: string;
  Grades: string;
  Website: string;
  "Application/Submission Date": string;
  "Opportunity Date": string;
  "Opportunity Type": string;
  Contact: string;
  Description: string;
  "Fee ($)": string;
  Compensation: string;
  "Prestige/Competitiveness": string;
}

const compOptions = ['Yes', 'No'];
const feeOptions = ['Yes', 'No'];

const fetchOpportunities = async (): Promise<Opportunity[]> => {
  try {
    // call our Next-route proxy instead of Google directly
    const response = await fetch("/api/sheets/med", { cache: "no-store" });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.values && result.values.length > 0) {
      const headers = result.values[0];
      return result.values.slice(1).map((row: string[]) => {
        return headers.reduce((acc: Opportunity, header: string, idx: number) => {
          acc[header as keyof Opportunity] = row[idx] || "";
          return acc;
        }, {} as Opportunity);
      });
    }

    console.error("No data found in the spreadsheet");
    return [];
  } catch (err) {
    console.error("Error fetching data from Sheets proxy:", err);
    return [];
  }
};

function hasFee(opp: Opportunity): 'Yes' | 'No' {
  const feeField = opp['Fee ($)'];
  if (!feeField || feeField.trim() === '') {
    return 'No';
  }
  const lowerCaseFeeField = feeField.toLowerCase();
  return lowerCaseFeeField.includes('free') || lowerCaseFeeField.includes('no') ? 'No' : 'Yes';
}

function hasCompensation(opp: Opportunity): 'Yes' | 'No' {
  const compField = opp['Compensation'];
  if (!compField || compField.trim() === '') {
    return 'Yes';
  }
  return compField.toLowerCase().includes('no') ? 'No' : 'Yes';
}

// Filter Options
const ageOptions = ['1-12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', 'Any'];
const gradeOptions = ['9', '10', '11', '12', 'Any'];
const stateOptions = [
  'Virtual', 'International',
  'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware', 'Florida', 'Georgia',
  'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky', 'Louisiana', 'Maine', 'Maryland',
  'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi', 'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey',
  'New Mexico', 'New York', 'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania', 'Rhode Island', 'South Carolina',
  'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont', 'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming'
];
const programTypeOptions = [
  'Research', 'Internship', 'Summer', 'School Club', 'Competition',
  'Pre-College', 'STEM', 'Volunteer', 'Nonprofit Organization', 'Educational',
  'Lab', 'Mentorship', 'Shadowing', 'Writing', 'Camp', 'Academic Journal',
  'Event', 'Course', 'Conference'
];
const timelineOptions = ['Summer', 'Winter', 'Spring', 'Fall', 'School Year'];

// Add new interface for plan types
type PlanType = 'free' | 'basic_monthly' | 'premium_monthly' | null;

// Add new interface for user profile
interface UserProfile {
  plan_type: PlanType;
}

// ---------------------------------------------
// Main Component
// ---------------------------------------------
export default function ExtracurricularsDashboard() {
  // Auth state
  const { user, isAuthenticated } = useAuth();
  
  // Add state for user's plan
  const [userPlan, setUserPlan] = useState<PlanType>(null);

  // Local States
  const [showSubmissionForm, setShowSubmissionForm] = useState(false);
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [filteredOpportunities, setFilteredOpportunities] = useState<Opportunity[]>([]);
  const [displayedOpportunities, setDisplayedOpportunities] = useState<Opportunity[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    age: new Set<string>(),
    grade: new Set<string>(),
    location: new Set<string>(),
    attendanceType: new Set<string>(),
    programType: new Set<string>(),
    timeline: new Set<string>(),
    comp: new Set<string>(),
    fee: new Set<string>(),
  });

  // Get subscription info to determine if user has premium
  const { planType } = useSubscription();
  // Premium users have unlimited access
  const hasUnlimitedAccess = planType === 'premium_monthly' || planType === 'premium_yearly';
  // Set a reasonable limit based on plan type
  const medicalECsLimit = hasUnlimitedAccess ? 999999 : planType === 'basic_monthly' || planType === 'basic_yearly' ? 100 : 30;

  // Modal States - initialize premium overlay to false for premium users
  const [showLoginOverlay, setShowLoginOverlay] = useState(false);
  // Never show premium overlay for premium users, even during initial render
  const [showPremiumOverlay, setShowPremiumOverlay] = useState(false);

  // Force premium overlay to always be false for premium users
  const actualShowPremiumOverlay = hasUnlimitedAccess ? false : showPremiumOverlay;
  const [activeFilter, setActiveFilter] = useState<string | null>(null);

  // Immediately disable premium overlay for premium users
  useEffect(() => {
    if (hasUnlimitedAccess) {
      setShowPremiumOverlay(false);

      // Add a direct DOM manipulation as a last resort
      // This will forcibly remove any premium overlay that might appear
      const removeOverlay = () => {
        const overlays = document.querySelectorAll('.fixed.inset-0.bg-blue-900\\/80');
        overlays.forEach(overlay => {
          if (overlay.innerHTML.includes('Unlock Premium Access')) {
            overlay.remove();
          }
        });
      };

      // Run immediately
      removeOverlay();

      // Also run after a short delay to catch any that appear during loading
      const timerId = setTimeout(removeOverlay, 100);
      const timerId2 = setTimeout(removeOverlay, 500);
      const timerId3 = setTimeout(removeOverlay, 1000);

      // Set up an interval to keep checking
      const intervalId = setInterval(removeOverlay, 2000);

      return () => {
        clearTimeout(timerId);
        clearTimeout(timerId2);
        clearTimeout(timerId3);
        clearInterval(intervalId);
      };
    }
  }, [hasUnlimitedAccess]);

  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 0);

  // Refs
  const opportunitiesSectionRef = useRef<HTMLDivElement>(null);
  const listingsEndRef = useRef<HTMLDivElement>(null);


  // Fetch user's plan type when authenticated
  useEffect(() => {
    const fetchUserPlan = async () => {
      if (!isAuthenticated || !user) return;
      
      try {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('plan_type')
          .eq('id', user.id)
          .maybeSingle();
          
          if (error) console.error(error);
          console.log('Profile row:', profile);          // <-- see what comes back
          
          setUserPlan(profile?.plan_type?.toLowerCase() as PlanType ?? 'free');
      } catch (err) {
        console.error('Error fetching user plan:', err);
        setUserPlan('free'); // Default to free plan on error
      }
    };

    fetchUserPlan();
  }, [isAuthenticated, user]);

  // Get view limit based on authentication and plan type
  const getViewLimit = () => {
    if (!isAuthenticated) return 15;
    if (!userPlan) return 15;
    
    switch (userPlan) {
      case 'free':
        return 30;
      case 'basic_monthly':
        return 60;
      case 'premium_monthly':
        return Infinity;
      default:
        return 15;
    }
  };

  // Completely disable premium overlay for premium users with MutationObserver
  useEffect(() => {
    // If user has unlimited access, never show premium overlay
    if (hasUnlimitedAccess) {
      // Create a MutationObserver to ensure the overlay stays hidden
      const observer = new MutationObserver(() => {
        if (showPremiumOverlay) {
          setShowPremiumOverlay(false);
        }
      });

      // Start observing the document
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      return () => observer.disconnect();
    }
  }, [hasUnlimitedAccess, showPremiumOverlay]);


  // Intersection observer to trigger modals when scrolling to end
  useEffect(() => {
    // Skip completely for premium users
    if (hasUnlimitedAccess) return;

    if (!listingsEndRef.current) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {

          // Don't show any overlays if opportunities are still loading or being filtered
          if (opportunities.length === 0 || filteredOpportunities.length === 0 || displayedOpportunities.length === 0) {
            return;
          }

          if (!isAuthenticated && filteredOpportunities.length > 30) {
            setShowLoginOverlay(true);
          } else if (isAuthenticated && !hasUnlimitedAccess && filteredOpportunities.length > 60) {
            // Only show premium overlay for non-premium users
            if (!hasUnlimitedAccess) {
              setShowPremiumOverlay(true);
            }
          }
        }
      });
    }, { threshold: 0.1 });

    observer.observe(listingsEndRef.current);
    return () => observer.disconnect();
  }, [isAuthenticated, filteredOpportunities.length, hasUnlimitedAccess, opportunities.length]);

  // Update displayed opportunities based on view limit
  useEffect(() => {
    const viewLimit = getViewLimit();
    setDisplayedOpportunities(filteredOpportunities.slice(0, viewLimit));
  }, [filteredOpportunities, isAuthenticated, userPlan]);

  // Handle login click from overlay
  const handleLoginClick = () => {
    setShowLoginOverlay(false);
  };

  // Update filter selections
  const updateFilter = (category: keyof typeof filters, value: string) => {
    setFilters((prev) => {
      const newSet = new Set(prev[category]);
      if (newSet.has(value)) {
        newSet.delete(value);
      } else {
        newSet.add(value);
      }
      return { ...prev, [category]: newSet };
    });
  };

  const resetFilters = () => {
    setFilters({
      age: new Set<string>(),
      grade: new Set<string>(),
      location: new Set<string>(),
      attendanceType: new Set<string>(),
      programType: new Set<string>(),
      timeline: new Set<string>(),
      comp: new Set<string>(),
      fee: new Set<string>(),
    });
  };

  const scrollToOpportunitiesSection = () => {
    opportunitiesSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Fetch opportunities from Google Sheets
  useEffect(() => {
    fetchOpportunities().then(setOpportunities);
  }, []);

  // Window resize handler
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Filter opportunities based on search and selected filters
  useEffect(() => {
    setFilteredOpportunities(
      opportunities.filter((opp) => {
        const matchesSearch = Object.values(opp).some((value) =>
          value.toLowerCase().includes(searchTerm.toLowerCase())
        );

        const matchesAge =
          filters.age.size === 0 ||
          filters.age.has('Any') ||
          opp.Ages.split(',').some((age) => filters.age.has(age.trim()));

        const matchesGrade =
          filters.grade.size === 0 ||
          filters.grade.has('Any') ||
          opp.Grades.split(',').some((grade) => filters.grade.has(grade.trim()));

        const matchesLocation =
          filters.location.size === 0 ||
          stateOptions.some((state) => filters.location.has(state) && opp.Location.includes(state));

        const matchesAttendanceType =
          filters.attendanceType.size === 0 ||
          (opp.Location.toLowerCase().includes('virtual') &&
            filters.attendanceType.has('Virtual')) ||
          (!opp.Location.toLowerCase().includes('virtual') &&
            filters.attendanceType.has('In-person')) ||
          (opp.Location.toLowerCase().includes('virtual') &&
            opp.Location.split(',').length > 1 &&
            filters.attendanceType.has('Hybrid'));

        const matchesProgramType =
          filters.programType.size === 0 ||
          Array.from(filters.programType).some((type) =>
            opp['Opportunity Type'].toLowerCase().includes(type.toLowerCase())
          );

        const matchesTimeline =
          filters.timeline.size === 0 ||
          Array.from(filters.timeline).some((timelineOption) =>
            opp['Opportunity Date'].toLowerCase().includes(timelineOption.toLowerCase())
          );

        const matchesFee =
          filters.fee.size === 0 || filters.fee.has(hasFee(opp));

        const matchesComp =
          filters.comp.size === 0 || filters.comp.has(hasCompensation(opp));

        return (
          matchesSearch &&
          matchesAge &&
          matchesGrade &&
          matchesLocation &&
          matchesAttendanceType &&
          matchesProgramType &&
          matchesTimeline &&
          matchesComp &&
          matchesFee
        );
      })
    );
  }, [opportunities, searchTerm, filters]);

  // Limit displayed opportunities based on subscription plan
  useEffect(() => {
    // If not authenticated, show limited opportunities
    if (!isAuthenticated) {
      setDisplayedOpportunities(filteredOpportunities.slice(0, 30));
      return;
    }

    // If authenticated, use the subscription limit
    const limit = typeof medicalECsLimit === 'number' ? medicalECsLimit : 999999;
    setDisplayedOpportunities(filteredOpportunities.slice(0, limit));
  }, [filteredOpportunities, isAuthenticated, medicalECsLimit]);

  useEffect(() => {
    document.title = 'Klinn | Extracurriculars';
  }, []);

  const isSmallScreen = windowWidth < 640;

  const toggleFilter = (filterName: string) => {
    setActiveFilter(activeFilter === filterName ? null : filterName);
  };

  const countAppliedFilters = () => {
    return Object.values(filters).reduce((acc, set) => acc + set.size, 0);
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <Navbar />

      {/* Hero Section */}
      <div className="relative min-h-screen bg-gradient-to-b from-blue-600 to-blue-800">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff0a_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0a_1px,transparent_1px)] bg-[size:44px_44px]" />
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
            <div className="relative">
              <div className="absolute -left-40 -top-40 h-80 w-80 rounded-full bg-indigo-400/30 blur-[120px]" />
              <div className="absolute -right-40 -bottom-40 h-80 w-80 rounded-full bg-blue-400/30 blur-[120px]" />
            </div>
          </div>
          <SparklesCore
            id="tsparticlesfullpage"
            background="transparent"
            minSize={0.4}
            maxSize={0.8}
            particleDensity={40}
            className="w-full h-full"
            particleColor="#ffffff"
          />
        </div>

        <div className="relative container mx-auto px-4">
          <section className="relative h-screen flex items-center justify-center">
            <div className="max-w-4xl mx-auto text-center space-y-10">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/5 backdrop-blur-sm border border-white/10"
              >
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-blue-200 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-blue-100"></span>
                </span>
                <span className="text-sm text-blue-50 tracking-wide">Medical Opportunities</span>
              </motion.div>

              {/* Main Heading */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="space-y-4"
              >
                <h1 className="text-6xl sm:text-7xl md:text-8xl font-light text-white tracking-tight">
                  Shape Your
                  <span className="block mt-2 font-normal bg-gradient-to-r from-blue-100 via-white to-blue-100 bg-clip-text text-transparent">
                    Future Today
                  </span>
                </h1>
                <p className="text-xl text-blue-100/80 font-light max-w-2xl mx-auto leading-relaxed">
                  Access extraordinary opportunities curated to elevate your academic journey and career path.
                </p>
              </motion.div>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="pt-6"
              >
                <button
                  onClick={scrollToOpportunitiesSection}
                  className="group inline-flex items-center gap-2 px-6 py-3 text-sm sm:text-base text-white border border-white/10 rounded-full hover:bg-white/5 transition-all duration-300 backdrop-blur-sm bg-white/20"
                >
                  Browse Opportunities
                  <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-0.5 transition-transform" />
                </button>
              </motion.div>

            </div>

            {/* Scroll Indicator */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 1 }}
              className="absolute bottom-12 left-1/2 -translate-x-1/2"
            >
              <motion.div
                animate={{ y: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <ChevronsDown className="w-6 h-6 text-white/30" />
              </motion.div>
            </motion.div>
          </section>
        </div>
      </div>

      {/* Main Content */}
      <main ref={opportunitiesSectionRef} className="relative bg-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-screen-xl mx-auto">
          {/* Page Header */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-extralight text-blue-800 mb-4 md:mb-0">
              Extracurricular <span className="font-light">Opportunities</span>
            </h2>
            <button
              onClick={() => setShowSubmissionForm(!showSubmissionForm)}
              className="bg-blue-500 hover:bg-blue-600 text-white font-light py-2 px-6 rounded-full transition-colors"
            >
              {showSubmissionForm ? 'Hide Form' : 'Submit an Opportunity'}
            </button>
          </div>

          {/* Opportunity Submission Form */}
          {showSubmissionForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-12"
            >
              <OpportunitySubmissionForm />
            </motion.div>
          )}

          {/* Subscription Limits Alert */}
          {isAuthenticated && (
            <div className="mb-6">
              <ExtracurricularLimits type="medicalECs" />
            </div>
          )}

          {/* Search and Filter UI */}
          <div className="mb-12">
            {/* Search Bar  */}
            <div className="mb-6">
              <div className="flex items-center relative">
                <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-blue-300 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search opportunities..."
                  className="w-full pl-12 pr-4 py-3 rounded-full border border-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-400 bg-blue-50/50 text-blue-800 font-light"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Filter System */}
            <div className="flex flex-wrap items-center gap-2 mb-4">
              {/* Filter Dropdowns */}
              {[
                { name: 'age', label: 'Age', options: ageOptions },
                { name: 'grade', label: 'Grade', options: gradeOptions },
                { name: 'location', label: 'Location', options: stateOptions },
                { name: 'attendanceType', label: 'Format', options: ['Virtual', 'In-person', 'Hybrid'] },
                { name: 'programType', label: 'Type', options: programTypeOptions },
                { name: 'timeline', label: 'When', options: timelineOptions },
                { name: 'comp', label: 'Paid', options: compOptions },
                { name: 'fee', label: 'Free', options: feeOptions },
              ].map((filterItem) => (
                <div key={filterItem.name} className="relative">
                  <button
                    onClick={() => toggleFilter(filterItem.name)}
                    className={`px-5 py-2 rounded-full text-sm font-light transition-colors flex items-center ${
                      filters[filterItem.name as keyof typeof filters].size > 0
                        ? 'bg-blue-500 text-white'
                        : 'bg-blue-50 text-blue-800 hover:bg-blue-100'
                    }`}
                  >
                    {filterItem.label}
                    {filters[filterItem.name as keyof typeof filters].size > 0 && (
                      <span className="ml-2 bg-white text-blue-500 rounded-full w-5 h-5 inline-flex items-center justify-center text-xs">
                        {filters[filterItem.name as keyof typeof filters].size}
                      </span>
                    )}
                    <ChevronDown className="w-4 h-4 ml-1" />
                  </button>

                  {activeFilter === filterItem.name && (
                    <div className="absolute z-10 mt-1 bg-white rounded-lg shadow-lg py-2 w-48 max-h-60 overflow-y-auto border border-blue-100">
                      {filterItem.options.map((option) => (
                        <div
                          key={option}
                          className="px-4 py-2 hover:bg-blue-50 cursor-pointer flex items-center"
                          onClick={() => updateFilter(filterItem.name as keyof typeof filters, option)}
                        >
                          <div className={`w-4 h-4 rounded ${
                            filters[filterItem.name as keyof typeof filters].has(option)
                              ? 'bg-blue-500'
                              : 'border border-blue-300'
                          } mr-3`}></div>
                          <span className="font-light">{option}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}

              {/* Clear Filters Button */}
              {countAppliedFilters() > 0 && (
                <button
                  onClick={resetFilters}
                  className="px-4 py-2 rounded-full text-sm font-light border border-red-100 text-red-500 hover:bg-red-50 transition-colors ml-auto"
                >
                  Clear all ({countAppliedFilters()})
                </button>
              )}
            </div>
          </div>

          {/* Results Count */}
          <div className="mb-8 text-sm font-light text-blue-600">
            {filteredOpportunities.length} opportunities found
          </div>

          {/* Opportunities Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            {opportunities.length === 0 ? (
              <div className="text-center py-16">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-lg font-light text-blue-500">Loading opportunities...</p>
              </div>
            ) : filteredOpportunities.length === 0 ? (
              <div className="text-center py-16">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-lg font-light text-blue-500">Processing results...</p>
              </div>
            ) : displayedOpportunities.length > 0 ? (
              displayedOpportunities.map((opp, index) => (
                <OpportunityCard key={index} opportunity={opp} />
              ))
            ) : (
              <div className="text-center py-16">
                <p className="text-lg font-light text-blue-500">No opportunities match your current filters.</p>
                <button
                  onClick={resetFilters}
                  className="mt-4 text-blue-600 underline"
                >
                  Clear all filters
                </button>
              </div>
            )}

            {/* View Count Message */}
            {filteredOpportunities.length > 0 && (
              <div className="text-center text-blue-400 font-extralight pt-6 pb-4">
                Showing {displayedOpportunities.length} of {filteredOpportunities.length} opportunities
              </div>
            )}
          </motion.div>

          {((!isAuthenticated && filteredOpportunities.length > 30) ||
            (isAuthenticated && filteredOpportunities.length > 60)) && (
            <div ref={listingsEndRef} className="h-4 w-full" />
          )}

          {/* Login Overlay */}
          {showLoginOverlay && !isAuthenticated && filteredOpportunities.length > 30 && (
            <div className="fixed inset-0 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-white rounded-2xl shadow-xl max-w-md w-full p-8"
              >
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BookmarkIcon className="w-8 h-8 text-blue-500" />
                  </div>
                  <h3 className="text-2xl font-light text-blue-800">Discover More Opportunities</h3>
                  <p className="text-blue-600 mt-2 font-light">
                    Create an account to access our full database of opportunities.
                  </p>
                </div>
                <div className="flex flex-col gap-3">
                  <Link href="/auth" onClick={handleLoginClick} className="w-full">
                    <button className="w-full bg-blue-500 hover:bg-blue-600 text-white font-light py-3 px-4 rounded-full transition-colors">
                      Sign in
                    </button>
                  </Link>
                  <Link href="/auth" onClick={handleLoginClick} className="w-full">
                    <button className="w-full bg-white border border-blue-200 hover:bg-blue-50 text-blue-700 font-light py-3 px-4 rounded-full transition-colors">
                      Create account
                    </button>
                  </Link>
                  <button
                    onClick={() => setShowLoginOverlay(false)}
                    className="text-blue-400 hover:text-blue-600 text-sm font-light mt-2"
                  >
                    Continue browsing
                  </button>
                </div>
              </motion.div>
            </div>
          )}

          {/* Premium Overlay - Only shown for non-premium users */}
          {actualShowPremiumOverlay && isAuthenticated && filteredOpportunities.length > 60 && opportunities.length > 0 && displayedOpportunities.length > 0 && (
            <div className="fixed inset-0 bg-blue-900/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-white rounded-2xl shadow-xl max-w-md w-full p-8"
              >
                <div className="text-center mb-8">
                  <div className="w-16 h-16 bg-indigo-50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BookmarkIcon className="w-8 h-8 text-indigo-500" />
                  </div>
                  <h3 className="text-2xl font-light text-indigo-800">Unlock Premium Access</h3>
                  <p className="text-indigo-600 mt-2 font-light">
                    Upgrade to view all opportunities and access exclusive features.
                  </p>
                </div>
                <div className="flex flex-col gap-3">
                  <Link href="/pricing" className="w-full">
                    <button className="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-light py-3 px-4 rounded-full transition-colors">
                      View Plans
                    </button>
                  </Link>
                  <button
                    onClick={() => setShowPremiumOverlay(false)}
                    className="text-indigo-400 hover:text-indigo-600 text-sm font-light mt-2"
                  >
                    Continue browsing
                  </button>
                </div>
              </motion.div>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <Footer className="bg-blue-600" />
    </div>
  );
}