"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Portal } from "@/components/ui/portal";

export interface Step {
  targetId: string;
  title: string;
  message: string;
  placement?: "top" | "bottom" | "left" | "right";
  // You might add a field for switching tabs if needed,
  // but for this outreach page, we'll simply guide the user.
}

export interface OutreachOnboardingPopupProps {
  onComplete: () => void;
  // Optional callback that fires on step change.
  onStepChange?: (currentStep: number, step: Step) => void;
  steps?: Step[];
}

const defaultSteps: Step[] = [
  {
    targetId: "outreach-header",
    title: "Welcome to Klinn Outreach",
    message:
      "This is your hub for discovering and connecting with researchers, startups, and clinics. Get started by exploring the opportunities.",
    placement: "bottom",
  },
  {
    targetId: "filter-panel",
    title: "Refine Your Search",
    message:
      "Use these filters—such as opportunity type, search input, and ZIP code (for clinics)—to narrow down your results.",
    placement: "right",
  },
  {
    targetId: "outreach-tabs",
    title: "Switch Between Listings",
    message:
      "Use these tabs to switch between browsing new opportunities and viewing your unlocked contacts.",
    placement: "bottom",
  },
  {
    targetId: "result-container",
    title: "Results Area",
    message:
      "Here you can see the list of matching opportunities. Scroll down to load more listings as they become available.",
    placement: "top",
  },
];

export default function OutreachOnboardingPopup({
  onComplete,
  onStepChange,
  steps = defaultSteps,
}: OutreachOnboardingPopupProps) {
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== "undefined" ? window.innerWidth : 0,
    height: typeof window !== "undefined" ? window.innerHeight : 0,
  });
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Update window size on resize
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Notify parent of step change
  useEffect(() => {
    if (onStepChange) {
      onStepChange(currentStep, steps[currentStep]);
    }
  }, [currentStep, steps, onStepChange]);

  // Update the target element's rect when step changes or window resizes
  useEffect(() => {
    const updateTargetRect = () => {
      const currentStepInfo = steps[currentStep];
      const targetEl = document.getElementById(currentStepInfo.targetId);
      if (targetEl) {
        const rect = targetEl.getBoundingClientRect();
        setTargetRect(rect);
        targetEl.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    };

    updateTargetRect();
    const timer = setTimeout(updateTargetRect, 300);
    return () => clearTimeout(timer);
  }, [currentStep, steps, windowSize]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Mark onboarding as complete
      localStorage.setItem("outreachOnboardingComplete", "true");
      onComplete();
    }
  };

  const handleSkip = () => {
    localStorage.setItem("outreachOnboardingComplete", "true");
    onComplete();
  };

  // Calculate tooltip position
  const getTooltipPosition = () => {
    if (!targetRect) return { top: 0, left: 0 };
    const currentStepInfo = steps[currentStep];
    const placement = currentStepInfo.placement || "bottom";
    const tooltipWidth = tooltipRef.current?.offsetWidth || 300;
    const tooltipHeight = tooltipRef.current?.offsetHeight || 120;
    const margin = 12;

    let top = 0;
    let left = 0;
    switch (placement) {
      case "top":
        top = targetRect.top - tooltipHeight - margin;
        left = targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2);
        break;
      case "bottom":
        top = targetRect.bottom + margin;
        left = targetRect.left + (targetRect.width / 2) - (tooltipWidth / 2);
        break;
      case "left":
        top = targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2);
        left = targetRect.left - tooltipWidth - margin;
        break;
      case "right":
        top = targetRect.top + (targetRect.height / 2) - (tooltipHeight / 2);
        left = targetRect.right + margin;
        break;
    }

    // Keep tooltip within viewport bounds
    if (left < 16) left = 16;
    if (left + tooltipWidth > windowSize.width - 16) {
      left = windowSize.width - tooltipWidth - 16;
    }
    if (top < 16) top = 16;
    if (top + tooltipHeight > windowSize.height - 16) {
      top = windowSize.height - tooltipHeight - 16;
    }

    return { top, left };
  };

  const tooltipPosition = getTooltipPosition();

  if (!targetRect) return null;

  return (
    <Portal>
      {/* Semi-transparent overlay */}
      <div
        className="fixed inset-0 bg-black/50 z-[999]"
        onClick={handleSkip}
        aria-hidden="true"
      />
      {/* Highlight box */}
      <div
        className="fixed pointer-events-none z-[1000] transition-all duration-300 ease-in-out"
        style={{
          top: targetRect.top - 4,
          left: targetRect.left - 4,
          width: targetRect.width + 8,
          height: targetRect.height + 8,
          boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.5), 0 0 15px rgba(0, 112, 243, 0.7)",
          borderRadius: "4px",
          border: "2px solid #0070f3",
        }}
      />
      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-[1001] bg-white rounded-lg shadow-xl p-4 w-[300px] max-w-[calc(100vw-32px)] transition-all duration-300 ease-in-out"
        style={{
          top: tooltipPosition.top,
          left: tooltipPosition.left,
        }}
      >
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-medium text-gray-900">{steps[currentStep].title}</h3>
          <div className="text-sm text-gray-500">
            {currentStep + 1}/{steps.length}
          </div>
        </div>
        <p className="text-gray-600 mb-4">{steps[currentStep].message}</p>
        <div className="flex justify-between items-center">
          <Button variant="ghost" size="sm" onClick={handleSkip}>
            Skip tour
          </Button>
          <Button onClick={handleNext}>
            {currentStep < steps.length - 1 ? "Next" : "Finish"}
          </Button>
        </div>
      </div>
    </Portal>
  );
}
