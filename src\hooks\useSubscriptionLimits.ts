import { useState, useEffect, useCallback } from 'react';
import { useSubscription } from './useSubscription';
import { hasReachedLimit, getLimit, getRemainingUsage, hasAccess } from '@/lib/subscription-limits';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export function useSubscriptionLimits() {
  const { planType, isLoading: isSubscriptionLoading } = useSubscription();
  const [usage, setUsage] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClientComponentClient();

  // Fetch the current usage for the user
  const fetchUsage = useCallback(async () => {
    try {
      setIsLoading(true);
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.user) {
        setIsLoading(false);
        return;
      }

      // Set default values first in case of error
      const defaultUsage = {
        coldCallScripts: 0,
        nonmedECs: 0,
        medicalECs: 0,
        jobApplications: 0,
        aiResumeScorer: 0,
        automatedEmails: 0,
      };

      // Try to get usage data from API, but don't throw if it fails
      try {
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/subscription/usage?t=${timestamp}`);

        if (response.ok) {
          const userUsage = await response.json();
          setUsage({
            coldCallScripts: userUsage.coldCallScripts || 0,
            nonmedECs: userUsage.nonmedECs || 0,
            medicalECs: userUsage.medicalECs || 0,
            jobApplications: userUsage.jobApplications || 0,
            aiResumeScorer: userUsage.aiResumeScorer || 0,
            automatedEmails: userUsage.automatedEmails || 0,
          });
          setIsLoading(false);
          return;
        }
      } catch (innerError) {
        console.log('Could not fetch usage data, using defaults');
      }

      // If we get here, use default values
      setUsage(defaultUsage);
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  // Increment usage for a specific feature
  const incrementUsage = useCallback(async (feature: string, amount: number = 1) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.user) {
        return false;
      }

      // Use the API endpoint to increment usage
      const response = await fetch('/api/subscription/usage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          feature,
          amount,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to increment usage');
      }

      // Update the local state
      setUsage(prev => ({
        ...prev,
        [feature]: (prev[feature] || 0) + amount
      }));

      return true;
    } catch (error) {
      console.error(`Error incrementing usage for ${feature}:`, error);
      return false;
    }
  }, [supabase]);

  // Check if the user has reached their limit for a specific feature
  const checkLimit = useCallback((feature: string) => {
    if (isSubscriptionLoading || isLoading) {
      return { hasReached: false, limit: 0, remaining: 0, hasAccess: true };
    }

    const currentUsage = usage[feature] || 0;
    const limit = getLimit(planType, feature as any);
    const remaining = typeof limit === 'number' ? getRemainingUsage(planType, feature as any, currentUsage) : 0;
    const featureHasAccess = hasAccess(planType, feature as any);
    const hasReached = hasReachedLimit(planType, feature as any, currentUsage);

    return {
      hasReached,
      limit,
      remaining,
      hasAccess: featureHasAccess,
      usage: currentUsage
    };
  }, [planType, usage, isSubscriptionLoading, isLoading]);

  // Fetch usage when the component mounts or when the user changes
  useEffect(() => {
    if (!isSubscriptionLoading) {
      fetchUsage();
    }
  }, [fetchUsage, isSubscriptionLoading]);

  return {
    usage,
    checkLimit,
    incrementUsage,
    refreshUsage: fetchUsage,
    isLoading: isLoading || isSubscriptionLoading
  };
}
