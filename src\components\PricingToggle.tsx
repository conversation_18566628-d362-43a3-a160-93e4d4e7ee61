'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';

type ToggleValue = 'monthly' | 'yearly';
interface PricingToggleProps {
  onChange: (value: ToggleValue) => void;
  initialValue?: ToggleValue;
  yearlyDiscountText?: string;
}

const PricingToggle: React.FC<PricingToggleProps> = ({
  onChange,
  initialValue = 'monthly',
  yearlyDiscountText = '-20%',
}) => {
  const [selectedValue, setSelectedValue] = useState<ToggleValue>(initialValue);
  const isYearly = selectedValue === 'yearly';

  const handleToggle = () => {
    const newValue = isYearly ? 'monthly' : 'yearly';
    setSelectedValue(newValue);
    onChange(newValue);
  };

  return (
    <div className="flex flex-col items-center justify-center mb-10">
      <button
        type="button"
        className="relative flex h-11 w-56 items-center rounded-full bg-slate-200 p-1 shadow-inner focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 focus-visible:ring-offset-slate-100 transition-colors duration-200 ease-in-out hover:bg-slate-300"
        onClick={handleToggle}
        aria-pressed={isYearly}
        aria-label={`Switch to ${isYearly ? 'monthly' : 'yearly'} pricing`}
      >
        {/* Sliding Thumb */}
        <motion.div
          className="absolute h-9 w-[calc(50%-4px)] rounded-full bg-blue-500 shadow-md"
          layout
          transition={{ type: 'spring', stiffness: 500, damping: 40 }}
          initial={false}
          animate={{ x: isYearly ? '100%' : '0%' }}
        />

        {/* Text Labels */}
        <div className="relative z-10 flex w-full justify-around">
          {/* Monthly Label */}
          <span
            className={`flex-1 cursor-pointer select-none rounded-full py-1.5 text-center text-sm font-medium transition-colors duration-300 ease-in-out ${
              !isYearly ? 'text-white' : 'text-slate-500 hover:text-slate-700'
            }`}
            onClick={() => {
              if (isYearly) handleToggle();
            }}
          >
            Monthly
          </span>

          {/* Yearly Label */}
          <span
            className={`flex-1 cursor-pointer select-none rounded-full py-1.5 text-center text-sm font-medium transition-colors duration-300 ease-in-out ${
              isYearly ? 'text-white' : 'text-slate-500 hover:text-slate-700'
            }`}
            onClick={() => {
              if (!isYearly) handleToggle();
            }}
          >
            Yearly
            {/* Discount Badge */}
            {yearlyDiscountText && (
              <motion.span
                className={`ml-1.5 inline-block text-xs font-semibold px-1.5 py-0.5 rounded-full align-middle transition-colors duration-300 ease-in-out ${
                  isYearly
                    ? 'bg-blue-700 text-white'
                    : 'bg-slate-300 text-slate-500' 
                }`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2, delay: 0.1 }}
                key={isYearly ? 'badge-active' : 'badge-inactive'} 
              >
                {yearlyDiscountText}
              </motion.span>
            )}
          </span>
        </div>
      </button>
    </div>
  );
};

export default PricingToggle;
