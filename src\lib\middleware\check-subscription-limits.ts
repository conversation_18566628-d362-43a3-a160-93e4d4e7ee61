import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { getUserSubscription } from '@/lib/subscription';
import { hasReachedLimit, hasAccess } from '@/lib/subscription-limits';
import { createClient } from '@supabase/supabase-js';

// For admin operations that bypass RLS
const getSupabaseAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

export async function checkSubscriptionLimits(
  req: NextRequest,
  feature: string,
  incrementAmount: number = 1
) {
  try {
    // Get the current user from the session
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('No active session found:', sessionError);
      return {
        allowed: false,
        response: NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 }),
        userId: null
      };
    }

    const user = session.user;
    if (!user) {
      console.error('No user found in session');
      return {
        allowed: false,
        response: NextResponse.json({ error: "Unauthorized - No user found" }, { status: 401 }),
        userId: null
      };
    }

    // Get the user's subscription
    const subscription = await getUserSubscription(user.id);
    const planType = subscription?.planType || 'free';

    // Check if the user has access to this feature
    if (!hasAccess(planType, feature as any)) {
      return {
        allowed: false,
        response: NextResponse.json({
          error: "Subscription required",
          message: `Your current plan does not include access to this feature. Please upgrade your subscription.`,
          planType,
          feature
        }, { status: 403 }),
        userId: user.id
      };
    }

    // Get the user's current usage
    const adminClient = getSupabaseAdminClient();

    // Get the current month and year
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
    const currentYear = now.getFullYear();

    // Map the feature name to the database column name
    const columnMap: Record<string, string> = {
      coldCallScripts: 'cold_call_scripts',
      tokens: 'tokens',
      jobApplications: 'job_applications',
      aiResumeScorer: 'ai_resume_scorer',
      automatedEmails: 'automated_emails',
      nonmedECs: 'nonmed_ecs',
      medicalECs: 'medical_ecs'
    };

    const columnName = columnMap[feature] || feature;

    // Query the usage table for the current month
    const { data, error } = await adminClient
      .from('user_usage')
      .select(columnName)
      .eq('user_id', user.id)
      .eq('month', currentMonth)
      .eq('year', currentYear)
      .single();

    // Default to 0 if no record exists or there's an error
    const featureUsage = data ? (data as any)[columnName] || 0 : 0;

    // Check if the user has reached their limit
    if (hasReachedLimit(planType, feature as any, featureUsage)) {
      return {
        allowed: false,
        response: NextResponse.json({
          error: "Usage limit reached",
          message: `You have reached your usage limit for this feature. Please upgrade your subscription for higher limits.`,
          planType,
          feature,
          currentUsage: featureUsage
        }, { status: 403 }),
        userId: user.id
      };
    }

    // If we get here, the user is allowed to use the feature
    return {
      allowed: true,
      response: null,
      userId: user.id,
      currentUsage: featureUsage,
      newUsage: featureUsage + incrementAmount
    };
  } catch (error) {
    console.error('Error checking subscription limits:', error);
    return {
      allowed: false,
      response: NextResponse.json({ error: "Server error", message: "An error occurred while checking subscription limits" }, { status: 500 }),
      userId: null
    };
  }
}
