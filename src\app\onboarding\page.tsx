'use client';

import { useEffect, useState } from 'react';
import { useRouter, notFound } from 'next/navigation';
import { auth, db } from '../../../firebase/clientApp';
import { doc, getDoc } from 'firebase/firestore';

interface SignupParams {
  firstName: string;
  lastName: string;
  email: string;
}

export default function OnboardingPage() {
  const router = useRouter();
  const [signupParams, setSignupParams] = useState<SignupParams | null>(null);

  // If the user is not signed in, throw a 404.
  if (!auth.currentUser) {
    notFound();
  }

  // On mount, pull the current user’s Firestore document.
  useEffect(() => {
    if (typeof window !== 'undefined' && auth.currentUser) {
      const userDocRef = doc(db, 'users', auth.currentUser.uid);
      getDoc(userDocRef).then((docSnap) => {
        if (docSnap.exists()) {
          const data = docSnap.data();
          // Allow linking if linked is false or doesn't exist.
          if (data.linked === false || typeof data.linked === 'undefined') {
            setSignupParams({
              firstName: data.firstName,
              lastName: data.lastName,
              email: data.email,
            });
          } else {
            // If already linked, send the user to the homepage.
            router.push('/');
          }
        }
      });
    }
  }, [router]);

  const handlePopup = () => {
    if (!signupParams) return;
    // Build a query string with URL-encoded params.
    const paramString = `firstname=${encodeURIComponent(
      signupParams.firstName
    )}&lastname=${encodeURIComponent(
      signupParams.lastName
    )}&email=${encodeURIComponent(
      signupParams.email
    )}`;
    // Base64 encode the entire string.
    const base64Params = btoa(paramString);

    //THIS IS THE URL THAT SHOULD BE USED FOR PRODUCTION
    // const popupUrl = `https://schoolnest.org/klnambassador?params=${base64Params}`;
    const popupUrl = `https://schoolnest.org/klnambassador?params=${base64Params}`;

    // const popupUrl = `http://localhost:3000/ambassador`;
    const popup = window.open(popupUrl, 'popup', 'width=600,height=500');

    // Poll to check if the popup is closed.
    const interval = setInterval(() => {
      if (popup?.closed) {
        clearInterval(interval);
        router.push('/'); // Redirect to homepage after popup closes.
      }
    }, 1000);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-indigo-900 to-indigo-600 text-white">
      {/* Faded X button at top right */}
      <button
        onClick={() => router.push('/')}
        className="absolute top-4 right-4 text-gray-400 hover:text-white transition-opacity duration-300"
        style={{ opacity: 0.5 }}
      >
        X
      </button>
      <div className="flex-grow flex items-center justify-center p-6">
        <div className="w-full text-center space-y-4">
          <div className="items-start mb-12">
            <h1 className="text-5xl md:text-7xl font-bold mb-4">
              We&apos;re Bringing Klinn to Schools.
            </h1>
            <h2 className="text-xl md:text-3xl font-bold mb-4">
              And we need your help.
            </h2>
          </div>
          <div className="flex flex-col md:flex-row justify-center text-start gap-8 mx-12 p-8 bg-indigo-950 m-4 rounded-md">
            <div className="md:w-1/2 space-y-4">
              <p className="text-md md:text-xl">
                Congratulations on creating an account on Klinn. You&apos;ve just put yourself ahead of so many other students.
              </p>
              <p className="text-md md:text-xl">
                To complete the registration process, you need to link your Klinn account with SchoolNest.
              </p>
              <p className="text-md md:text-xl">
                By linking your Klinn account to SchoolNest, you&apos;ll be able to access Klinn resources that interface <strong>directly</strong> with your school.
              </p>
            </div>
            <div className="md:w-1/2">

              <p className="text-md md:text-xl">
                Students that connect to SchoolNest and become contact ambassadors for their schools have:
              </p>
              <ul className="list-disc list-inside space-y-2 mt-4">
                <li>Won <strong>large</strong> scholarships such as the Coca Cola Scholarship</li>
                <li>Had the opportunity to lead and mantain their school&apos;s data for <strong>1000+</strong> students</li>
                <li>Admitted to schools such as Brown, Caltech, UPenn, JHU, Georgia Tech, and more</li>
              </ul>
              <button
                onClick={handlePopup}
                className="mt-4 px-4 w-full mx-auto bg-blue-600 text-white font-bold py-2 rounded-lg hover:bg-blue-700 transition duration-300"
              >
                Open Ambassador Form
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}