// storageUtils.ts
import { supabase } from '../supabase/supabaseClient';

export async function getSignedResumeUrl(filePath: string, expiresInSeconds = 60): Promise<string | null> {
  const { data, error } = await supabase.storage
    .from('resumes')
    .createSignedUrl(filePath, expiresInSeconds);

  if (error) {
    console.error('Error generating signed URL:', error);
    return null;
  }
  return data.signedUrl;
}
