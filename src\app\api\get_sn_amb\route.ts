import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
    const { email } = await request.json();

    console.log("Email to check:", email);

    const linkingPassword = process.env.LINKING_PASSWORD;

    try {
        const response = await axios.post('https://schoolnest.org/api/get_ambassador/', {
            email,
            linkingPassword
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        console.log("API response status:", response.status);
        console.log("API response data:", response.data);

        if (response.status === 200) {
            return NextResponse.json({ 
                message: 'Found Account', 
                isAmbassador: true 
            }, { status: 200 });
        }

    } catch (error: any) {
        console.error("Error during API call:", error);
        
        // Handle specific axios error responses
        if (error.response) {
            console.log("Error response status:", error.response.status);
            console.log("Error response data:", error.response.data);
            
            // Return 200 with isAmbassador: false for both 401 and 404
            if (error.response.status === 401 || error.response.status === 404) {
                return NextResponse.json({ 
                    message: 'User not found', 
                    isAmbassador: false 
                }, { status: 200 });
            }
        }
        
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
    
    return NextResponse.json({ 
        message: 'Unknown Error', 
        isAmbassador: false 
    }, { status: 200 });
}