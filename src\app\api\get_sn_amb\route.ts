import { NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: Request) {
    const { email } = await request.json();

    console.log("Email to check:", email);

    const linkingPassword = process.env.LINKING_PASSWORD;


    let response = NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    try{
        response = await axios.post('https://schoolnest.org/api/get_ambassador/', {
            email,
            linkingPassword
        }, {
            headers: {
            'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error("Error during API call:", error);
        // return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
    

    console.log("API response status:", response.status);

    if (response.status === 200) {
        


        return NextResponse.json({ message: 'Found Account' }, { status: 200 });
    }


    if (response.status === 404) {
        return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Unknown Error' }, { status: 500 });

}