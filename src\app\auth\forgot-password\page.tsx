'use client';
import React, { useState } from 'react';
import { supabase } from '../../../../supabase/supabaseClient';
import { useRouter } from 'next/navigation';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const router = useRouter();
  
  const handlePasswordReset = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage('');
    setError('');
    
    try {
      // Use the full absolute URL based on your environment. Ensure this URL is whitelisted in Supabase.
      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${baseUrl}/auth/reset-password`,
      });
      
      if (error) throw error;
      
      setMessage('Password reset email sent. Please check your inbox.');
    } catch (err: any) {
      console.error('Password reset error:', err);
      setError(err.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-[#12100E] to-[#93C5FD]">
      <main className="flex-grow flex items-center justify-center p-6 pt-12 min-h-screen">
        <div className="container mx-auto flex flex-col items-center justify-center">
          <h1 className="text-3xl font-bold mb-6 text-white">Forgot Password</h1>
          <form onSubmit={handlePasswordReset} className="space-y-5 w-full max-w-md">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium text-white/90">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="<EMAIL>"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 rounded-lg bg-transparent border border-white/50 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/50 transition duration-300"
              />
            </div>
            
            {error && <p className="text-red-500 text-sm">{error}</p>}
            {message && <p className="text-green-500 text-sm">{message}</p>}
            
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-white text-[#1E3A8A] font-bold py-3 rounded-lg hover:bg-opacity-90 transition duration-300 disabled:opacity-50"
            >
              {isSubmitting ? 'Sending...' : 'Reset Password'}
            </button>
          </form>
          
          <p className="text-center text-white mt-4">
            Remembered your password?{' '}
            <button
              type="button"
              onClick={() => router.push('/auth')}
              className="text-white font-semibold hover:underline"
            >
              Log in
            </button>
          </p>
        </div>
      </main>
    </div>
  );
}
