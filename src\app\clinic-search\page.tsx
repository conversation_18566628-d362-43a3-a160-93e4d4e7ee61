"use client";
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Search, MapPin, ArrowRight, CheckCircle } from 'lucide-react';
import Navbar from '@/components/Navbar';
import AdNavbar from '@/components/NavbarAd';
import Footer from '@/components/Footer';
import ClientForm from '@/components/ClientForm';
import { Button } from '@/components/ui/button';

export default function ClinicSearch() {
  const router = useRouter();
  const [searchError, setSearchError] = useState('');

  const handleSearch = async (zipcode: string) => {
    try {
      const response = await fetch(`/api/clinics?zipcode=${zipcode}`);
      const clinics = await response.json();

      if (clinics.length === 0) {
        setSearchError('No clinics found. Please try a different ZIP code.');
      } else {
        router.push(
          `/results?clinics=${encodeURIComponent(JSON.stringify(clinics))}`,
        );
      }
    } catch (error) {
      setSearchError('An error occurred. Please try again.');
    }
  };

  useEffect(() => {
    document.title = 'Klinn | Clinic-Search';
  }, []);

  return (
    <div className="relative flex flex-col bg-gradient-to-br from-blue-900 via-blue-800 to-blue-700 font-thin">
      <Navbar />

      <Image
        src="/images/backgroundpic.jpg"
        alt="Medical background"
        layout="fill"
        objectFit="cover"
        className="absolute inset-0 w-full h-full z-0 mix-blend-overlay opacity-30"
      />

      <section className="relative flex items-center justify-center min-h-screen text-white pt-15 lg-mt-20 mt-8">
        <div className="relative z-0 w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="text-left"
            >
              <h1 className="text-5xl sm:text-6xl font-thin mb-6 leading-tight">
                Discover Your Ideal{' '}
                <span className="text-blue-300">Clinical Experience</span>
              </h1>
              <p className="text-xl sm:text-2xl mb-8 text-blue-100">
                Unlock opportunities to enhance your medical journey with our
                advanced clinic search.
              </p>
              <div className="space-y-4">
                {[
                  'Extensive database of clinics',
                  'Personalized recommendations',
                  'Easy search process',
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.2 + index * 0.1 }}
                    className="flex items-center space-x-2"
                  >
                    <CheckCircle className="text-green-400" />
                    <span>{feature}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg rounded-xl p-8 shadow-2xl"
            >
              <h2 className="text-3xl font-thin mb-6 text-center">
                Find Clinics Near You
              </h2>
              <ClientForm onSearch={handleSearch} />
              {searchError && (
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mt-4 text-red-400 text-center"
                >
                  {searchError}
                </motion.p>
              )}
            </motion.div>
          </div>
        </div>
      </section>
      <Footer className="bg-transparent text-white" />
    </div>
  );
}
