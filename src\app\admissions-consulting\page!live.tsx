'use client'
import React, { useState, useEffect } from 'react';
import Navbar from '@/components/Navbar';
import AdNavbar from '@/components/NavbarAd';

import Footer from '@/components/Footer';
import { motion } from 'framer-motion';
import { db } from '../../../firebase/clientApp';
import { collection, getDocs, doc, setDoc, getDoc, query, where } from 'firebase/firestore';
import ConsultantCard from '@/components/ConsultantCard';
import { Typewriter } from 'react-simple-typewriter';
import { Rubik } from 'next/font/google';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import Modal from '@/components/Modal';
import { SecureFileUpload } from '@/components/SecureFileUpload';
import ComingSoonModal from '@/components/ComingSoonModal'; // Import the new modal

const rubik = Rubik({ subsets: ['latin'] });

interface ConsultantData {
  firstName: string;
  lastName: string;
  profilePic: string;
  bio: string;
  college: string;
  email: string;
  acceptedColleges: string;
  attendedSchool: string;
  testScores: string;
  intendedMajor: string;
  location: string;
  education: string;
  website: string;
}

interface ConsultantFormData {
  fullName: string;
  email: string;
  resumeURL: string;
  resumeFileName: string;
  isMedicalConsultant: boolean;
  isCollegeConsultant: boolean;
  collegesAcceptedTo: string;
  medSchoolsAcceptedTo: string;
  agreedToTerms: boolean;
}

export default function AdmissionsConsulting() {
  const { user } = useAuth();
  const router = useRouter();
  const [consultants, setConsultants] = useState<ConsultantData[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [consultantFormData, setConsultantFormData] = useState<ConsultantFormData>({
    fullName: '',
    email: '',
    resumeURL: '',
    resumeFileName: '',
    isMedicalConsultant: false,
    isCollegeConsultant: false,
    collegesAcceptedTo: '',
    medSchoolsAcceptedTo: '',
    agreedToTerms: false,
  });

  const [showCollegeTextArea, setShowCollegeTextArea] = useState(false);
  const [showMedicalTextArea, setShowMedicalTextArea] = useState(false);
  const [isComingSoonModalOpen, setIsComingSoonModalOpen] = useState(false);

  useEffect(() => {
    const fetchConsultants = async () => {
      try {
        // 1. Reference your Firestore collection
        const consultantsRef = collection(db, "consultantProfiles");
  
        // 2. Build a query that matches only those with isPublished == true
        const publishedQuery = query(
          consultantsRef,
          where("isPublished", "==", true)
        );
  
        // 3. Get docs for that query
        const consultantSnapshot = await getDocs(publishedQuery);
  
        // 4. Map each doc into your desired data shape
        const consultantData: ConsultantData[] = consultantSnapshot.docs.map(
          (docSnapshot) => {
            const consultant = docSnapshot.data();
            return {
              id: docSnapshot.id,
              firstName: consultant.firstName || "",
              lastName: consultant.lastName || "",
              profilePic: consultant.profilePic || "/images/DefaultPhoto.jpg",
              bio: consultant.bio || "To Be Updated",
              college: consultant.college || "To Be Updated",
              email: consultant.email || "To Be Updated",
              acceptedColleges: consultant.acceptedColleges || "To Be Updated",
              attendedSchool: consultant.attendedSchool || "",
              testScores: consultant.testScores || "To Be Updated",
              intendedMajor: consultant.intendedMajor || "To Be Updated",
              location: consultant.location || "To Be Updated",
              education: consultant.education || "To Be Updated",
              website: consultant.website || "To Be Updated",
            };
          }
        );
  
        setConsultants(consultantData);
      } catch (error) {
        console.error("Error fetching consultants:", error);
      }
    };
  
    fetchConsultants();
  }, []);
  
  const handleConsultantCardClick = () => {
    setIsComingSoonModalOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setConsultantFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleFileUploadSuccess = (fileUrl: string, fileName: string) => {
    setConsultantFormData(prev => ({
      ...prev,
      resumeURL: fileUrl,
      resumeFileName: fileName
    }));
    setUploadError(null);
  };

  const handleFileUploadError = (error: string) => {
    setUploadError(error);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!user) {
      alert("You must be signed in to submit the application.");
      return;
    }

    if (!consultantFormData.resumeURL) {
      alert("Please upload a resume.");
      return;
    }

    try {
      // Prepare data for Firestore
      const formDataToSave = {
        ...consultantFormData,
        userId: user.uid,
        status: 'pending',
        appliedAt: new Date(),
      };

      // Save form data to Firestore
      const docRef = doc(db, 'consultantApplications', user.uid);
      await setDoc(docRef, formDataToSave);

      alert('Your application has been submitted successfully!');
      setIsModalOpen(false);
    } catch (error) {
      console.error("Error submitting application:", error);
      alert('There was an error submitting your application. Please try again.');
    }
  };

  const handleApplyClick = () => {
    if (user) {
      setIsModalOpen(true);
    } else {
      router.push('/auth');
    }
  };

  return (
    <div className={`min-h-screen flex flex-col bg-gradient-to-bl from-[#6495ED] to-[#7DF9FF] ${rubik.className}`}>
      <Navbar />

      <button
        onClick={handleApplyClick}
        className="lg:absolute right-8 top-64 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold rounded-lg lg:ml-6 w-fit mt-6 lg:mt-0 ml-12"
      >
        Apply to become a Consultant
      </button>

      <main className="flex-grow flex flex-col items-center justify-start p-6 pt-20 lg:pt-28">
        <div className="w-full max-w-3xl flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
          <motion.h1
            className="text-4xl lg:text-5xl font-bold mb-8 lg:mb-0 text-[#022B3A] leading-tight text-center lg:text-left"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <Typewriter
              words={['  Meet Our Expert Consultants', ' The Best Admissions Guidance', '  Build Your Path to Success']}
              loop={false}
              cursor
              cursorStyle="_"
              typeSpeed={70}
              deleteSpeed={50}
              delaySpeed={1000}
            />
          </motion.h1>
        </div>

        <motion.p
          className="text-xl text-[#022B3A] mb-12 text-center max-w-3xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          Our team of experienced consultants is here to guide you through every step of the admissions process.
        </motion.p>

        <div className="grid grid-cols-1 gap-10 w-full max-w-sm">
          {consultants.map((consultant, index) => (
            <ConsultantCard key={index} consultant={consultant} onClick={handleConsultantCardClick} />
          ))}
        </div>
      </main>

      {isModalOpen && (
        <Modal onClose={() => setIsModalOpen(false)}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <h2 className="text-3xl font-bold mb-6 text-center">Become a Consultant</h2>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="fullName" className="block font-medium mb-1">Full Name</label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={consultantFormData.fullName}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 rounded-lg border-2 border-white/30 focus:border-white bg-white/10 text-black placeholder-white/50 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
                  placeholder="John Doe"
                />
              </div>
              <div>
                <label htmlFor="email" className="block font-medium mb-1">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={consultantFormData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 rounded-lg border-2 border-white/30 focus:border-white bg-white/10 text-black placeholder-white/50 transition duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <SecureFileUpload
              onFileUpload={handleFileUploadSuccess}
              onError={handleFileUploadError}
              maxSize={5}
              allowedTypes={[
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
              ]}
              uploadPath="consultant-resumes"
            />
            {uploadError && (
              <p className="text-red-500 text-sm">{uploadError}</p>
            )}

            <div className="space-y-2">
              <label className="font-medium">Consultant Positions</label>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="isCollegeConsultant"
                  name="isCollegeConsultant"
                  checked={consultantFormData.isCollegeConsultant}
                  onChange={(e) => {
                    handleInputChange(e);
                    setShowCollegeTextArea(e.target.checked);
                  }}
                  className="form-checkbox"
                />
                <label htmlFor="isCollegeConsultant" className="text-sm">College Admissions Consultant</label>
              </div>
              {showCollegeTextArea && (
                <textarea
                  id="collegesAcceptedTo"
                  name="collegesAcceptedTo"
                  value={consultantFormData.collegesAcceptedTo}
                  onChange={handleInputChange}
                  placeholder="List the colleges you have been accepted to"
                  className="w-full p-3 border rounded-lg mt-2 text-black rounded border-white/50 bg-white/10 focus:ring-blue-400 focus:ring-opacity-25 placeholder-white/50"
                  rows={3}
                />
              )}

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="isMedicalConsultant"
                  name="isMedicalConsultant"
                  checked={consultantFormData.isMedicalConsultant}
                  onChange={(e) => {
                    handleInputChange(e);
                    setShowMedicalTextArea(e.target.checked);
                  }}
                  className="form-checkbox"
                />
                <label htmlFor="isMedicalConsultant" className="text-sm">Medical School Admissions Consultant</label>
              </div>
              {showMedicalTextArea && (
                <textarea
                  id="medSchoolsAcceptedTo"
                  name="medSchoolsAcceptedTo"
                  value={consultantFormData.medSchoolsAcceptedTo}
                  onChange={handleInputChange}
                  placeholder="List the medical schools you have been accepted to"
                  className="w-full p-3 border rounded-lg mt-2 text-black rounded border-white/50 bg-white/10 focus:ring-blue-400 focus:ring-opacity-25 placeholder-white/50"
                  rows={3}
                />
              )}
            </div>

            <div className="flex items-start space-x-2">
              <input
                type="checkbox"
                id="agreedToTerms"
                name="agreedToTerms"
                checked={consultantFormData.agreedToTerms}
                onChange={handleInputChange}
                required
                className="h-5 w-5 text-blue-600 rounded"
              />
              <label htmlFor="agreedToTerms" className="text-sm">
                I agree to the terms. I understand this is a part-time, commission-based position, not a salaried role.
              </label>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-600 text-white font-bold py-3 rounded-lg hover:bg-blue-700 transition duration-300"
            >
              Submit Application
            </button>
          </form>
        </Modal>
      )}
      
      {/* Coming Soon Modal */}
      {isComingSoonModalOpen && (
        <ComingSoonModal onClose={() => setIsComingSoonModalOpen(false)} />
      )}

      <Footer className="bg-transparent mt-20" />
    </div>
  );
}
