{"name": "klinn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.8", "@stripe/stripe-js": "^7.1.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tabler/icons-react": "^3.28.1", "@tsparticles/engine": "^3.7.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.7.1", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "csv-parse": "^5.5.6", "dotenv": "^16.4.5", "express": "^4.21.0", "firebase": "^10.13.2", "framer-motion": "^11.18.2", "geolib": "^3.3.4", "googleapis": "^144.0.0", "groq-sdk": "^0.9.0", "install": "^0.13.0", "lucide-react": "^0.438.0", "motion": "^12.6.3", "next": "15.0.3", "nextstepjs": "^2.0.0", "node-fetch": "^3.3.2", "openai": "^5.0.2", "react": "^18.3.1", "react-dom": "^18", "react-dropzone": "^14.3.5", "react-icons": "^5.3.0", "react-intersection-observer": "^9.13.1", "react-particles": "^2.12.2", "react-simple-typewriter": "^5.0.1", "react-spring": "^9.7.4", "react-typical": "^0.1.3", "resend": "^4.1.2", "sonner": "^2.0.5", "stripe": "^18.0.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tsparticles": "^3.7.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^8", "eslint-config-next": "14.2.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}