"use client";
import React, { useState, ChangeEvent, FormEvent } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { getFirestore, collection, addDoc } from "firebase/firestore";
import { useToast } from "@/hooks/use-toast";
import { getApp } from "firebase/app";
import Navbar from "@/components/Navbar";
import AdNavbar from "@/components/NavbarAd";

import Footer from "@/components/Footer";

const app = getApp();
const db = getFirestore(app);

interface FormData {
  name: string;
  email: string;
  graduationYear: string;
  status: string;
}

export default function CollegeConsulting() {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    graduationYear: "",
    status: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [id]: value
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const submissionData = {
        ...formData,
        timestamp: new Date().toISOString(),
      };

      await addDoc(collection(db, "college_consulting_waitlist"), submissionData);

      toast({
        title: "Successfully joined waitlist!",
        description: "We'll contact you soon with more information.",
      });

      setFormData({
        name: "",
        email: "",
        graduationYear: "",
        status: ""
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Error",
        description: "There was an error submitting your information. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-emerald-300 to-teal-100">
      <Navbar />
      <main className="flex-grow py-4 sm:py-8 mt-20">
        <div className="container mx-auto px-4 sm:px-32">
          <div className="flex flex-col sm:flex-row justify-between items-center mb-4 sm:mb-8 w-full max-w-7xl mx-auto">
            <h1 
              className="text-3xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-b from-emerald-600 to-emerald-500 
              mb-4 sm:mb-0 h-auto sm:h-24 leading-tight sm:leading-none text-center sm:text-left">
              College Admissions Consulting
            </h1>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-7xl mx-auto">
            <Card className="p-6 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <h2 className="text-2xl font-bold text-emerald-600">Your Path to College Success</h2>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700">
                    Our comprehensive college consulting service is designed to guide you through every step of the college admissions process, helping you stand out and gain acceptance to your dream schools.
                  </p>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold text-emerald-500">Application Strategy</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>Common App and Coalition App guidance</li>
                        <li>Personal statement development</li>
                        <li>Supplemental essay coaching</li>
                        <li>Activity list and resume crafting</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-emerald-500">College Selection</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>School selection strategy</li>
                        <li>Major and program guidance</li>
                        <li>Application timeline planning</li>
                      </ul>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-emerald-500">Academic Planning</h3>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 ml-4">
                        <li>SAT/ACT test prep strategy</li>
                        <li>Course selection guidance</li>
                        <li>Extracurricular activity planning</li>
                        <li>Summer program recommendations</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white/80 backdrop-blur-sm shadow-lg">
              <CardHeader>
                <h2 className="text-2xl font-bold text-emerald-600">Join the College Consulting Waitlist</h2>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <Input
                      id="name"
                      type="text"
                      placeholder="Enter your full name"
                      className="w-full"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      className="w-full"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="graduationYear" className="block text-sm font-medium text-gray-700 mb-1">
                      High School Graduation Year
                    </label>
                    <select
                      id="graduationYear"
                      className="w-full p-2 border rounded-md border-gray-300 focus:ring-emerald-500 focus:border-emerald-500"
                      value={formData.graduationYear}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Select graduation year</option>
                      <option value="2024">2024</option>
                      <option value="2025">2025</option>
                      <option value="2026">2026</option>
                      <option value="2027">2027</option>
                      <option value="later">2028 or later</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                      Current Status
                    </label>
                    <select
                      id="status"
                      className="w-full p-2 border rounded-md border-gray-300 focus:ring-emerald-500 focus:border-emerald-500"
                      value={formData.status}
                      onChange={handleInputChange}
                      required
                    >
                      <option value="">Select your current status</option>
                      <option value="freshman">High School Freshman</option>
                      <option value="sophomore">High School Sophomore</option>
                      <option value="junior">High School Junior</option>
                      <option value="senior">High School Senior</option>
                      <option value="gap">Gap Year</option>
                      <option value="transfer">Transfer Student</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white transition-colors duration-300"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Join Waitlist"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
      <div className="relative bottom-0 left-0 right-0 h-12 sm:h-24 bg-gradient-to-b from-transparent to-teal-100"></div>
      <Footer className="bg-teal-100"/>
    </div>
  );
}