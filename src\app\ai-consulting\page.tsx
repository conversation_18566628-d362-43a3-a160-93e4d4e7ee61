"use client"

import { type FormEvent, useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Navbar from "@/components/Navbar"
import Footer from "@/components/Footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { toast } from "sonner"
import { supabase } from "../../../supabase/supabaseClient" // adjust the path if needed
import { CheckCircle, X } from "lucide-react"

export default function AIConsulting() {
  const [email, setEmail] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!email.trim()) return

    setIsSubmitting(true)

    // insert the email into Supabase
    const { error } = await supabase.from("ai_consult_leads").insert({ email: email.trim().toLowerCase() })

    if (error) {
      // duplicate emails throw code 23505 (unique_violation)
      if (error.code === "23505") {
        toast.info("Looks like you&apos;re already on the list!")
      } else {
        toast.error("Something went wrong. Please try again.")
        console.error(error)
      }
    } else {
      setShowSuccessModal(true)
      setEmail("")
    }

    setIsSubmitting(false)
  }

  const closeModal = () => {
    setShowSuccessModal(false)
  }

  return (
    <div className="flex flex-col bg-gradient-to-br from-[#0F172A] via-[#1E40AF] to-[#3B82F6]">
      <Navbar />

      <main className="min-h-screen flex items-center justify-center px-6">
        <div className="container mx-auto max-w-6xl">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center">
            {/* Left side */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7 }}
              className="text-white space-y-6"
            >
              <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                AI-Powered
                <span className="block text-blue-200">Med School</span>
                <span className="block">Consulting</span>
              </h1>

              <p className="text-xl text-blue-100 leading-relaxed max-w-lg">
                AI-powered med-school application guidance trained on data from real acceptees. 99% cheaper than
                traditional consulting.
              </p>
            </motion.div>

            {/* Right side – form */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
              className="lg:justify-self-end w-full max-w-md"
            >
              <div className="backdrop-blur-lg bg-gradient-to-br from-white/10 to-white/20 rounded-2xl p-8 shadow-2xl border border-white/20">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-white">Get Started Today</h3>
                    <p className="text-sm text-white/80">Join our waitlist to be the first to know when we launch our AI-powered med school consulting service.</p>
                  </div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-3 rounded-lg bg-white/10 text-white placeholder:text-white/70 border-white/20 focus:border-white"
                  />

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-white text-blue-900 hover:bg-white/90 py-3 rounded-lg transition duration-300 font-medium"
                  >
                    {isSubmitting ? "Processing..." : "Get Started"}
                  </Button>

                  <p className="text-center text-xs text-white/70">No credit card required</p>
                </form>
              </div>
            </motion.div>
          </div>
        </div>
      </main>

      <Footer className="bg-black/20 backdrop-blur-sm border-t border-white/10" />

      {/* Success Modal */}
      <AnimatePresence>
        {showSuccessModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
            onClick={closeModal}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: "spring", duration: 0.5 }}
              className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center space-y-4">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>

                <h3 className="text-2xl font-bold text-gray-900">Thank You!</h3>

                <p className="text-gray-600 leading-relaxed">
                  We&apos;ve received your interest in our AI-powered med school consulting. We&apos;ll be in touch soon with more
                  details!
                </p>

                <Button
                  onClick={closeModal}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg transition duration-300 font-medium"
                >
                  Got it!
                </Button>
              </div>

              <button
                onClick={closeModal}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
