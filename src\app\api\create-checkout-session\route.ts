import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createClient } from '@supabase/supabase-js';
import { PLAN_TO_PRICE_ID } from "@/lib/subscription";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";

// For admin operations that bypass RLS
const getSupabaseAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

const getStripeClient = () => {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

  if (!stripeSecretKey) {
    console.error('Missing Stripe secret key');
    throw new Error('Missing Stripe secret key');
  }

  return new Stripe(stripeSecretKey, {
    apiVersion: '2023-10-16' as Stripe.LatestApiVersion,
  });
};

export async function POST(req: NextRequest) {
  try {
    // Get the current user from the session using the route client
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('No active session found:', sessionError);
      return NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 });
    }

    const user = session.user;
    if (!user) {
      console.error('No user found in session');
      return NextResponse.json({ error: "Unauthorized - No user found" }, { status: 401 });
    }

    // Check if user already has an active subscription
    const { data: userProfile, error: profileFetchError } = await supabase
      .from('profiles')
      .select('stripe_customer_id, subscription_status, email')
      .eq('id', user.id)
      .single();

    if (profileFetchError) {
      console.error('Error fetching user profile for subscription check:', profileFetchError);
      return NextResponse.json({ error: "Error fetching user profile" }, { status: 500 });
    }

    console.log(`User profile check - Customer ID: ${userProfile?.stripe_customer_id}, Status: ${userProfile?.subscription_status}`);

    // If user has an active subscription, redirect them to the customer portal
    if (userProfile?.stripe_customer_id && userProfile?.subscription_status === 'active') {
      const stripe = getStripeClient();

      try {
        const portalSession = await stripe.billingPortal.sessions.create({
          customer: userProfile.stripe_customer_id,
          return_url: `${req.headers.get('origin')}/profile-dashboard`,
        });
        return NextResponse.json({
          error: "You already have an active subscription",
          redirectToPortal: true,
          url: portalSession.url
        }, { status: 400 });
      } catch (stripeError: any) {
        // Handle invalid customer ID
        if (stripeError.type === 'StripeInvalidRequestError' && stripeError.code === 'resource_missing') {
          console.log(`Invalid Stripe customer ${userProfile.stripe_customer_id} found during checkout, clearing from database`);

          // Clear the invalid customer ID from the database
          const { error: clearError } = await supabase
            .from('profiles')
            .update({
              stripe_customer_id: null,
              subscription_status: null,
              subscription_plan_type: 'free',
              subscription_current_period_end: null
            })
            .eq('id', user.id);

          if (clearError) {
            console.error('Error clearing invalid customer data in checkout:', clearError);
          } else {
            console.log('Successfully cleared invalid customer data in checkout, proceeding with new checkout session');
          }
          // Continue with creating a new checkout session below
        } else {
          // Re-throw other Stripe errors
          throw stripeError;
        }
      }
    }

    // Get the plan from the request body
    const { plan } = await req.json();

    if (!plan || !PLAN_TO_PRICE_ID[plan as keyof typeof PLAN_TO_PRICE_ID]) {
      return NextResponse.json({ error: "Invalid plan selected" }, { status: 400 });
    }

    // Use pre-defined price IDs from the subscription mapping
    const stripe = getStripeClient();
    const priceId = PLAN_TO_PRICE_ID[plan as keyof typeof PLAN_TO_PRICE_ID];

    if (!priceId) {
      console.error('No price ID found for plan:', plan);
      return NextResponse.json({ error: "Invalid plan configuration" }, { status: 500 });
    }

    console.log('Using pre-defined price ID:', priceId, 'for plan:', plan);

    // Use admin client to bypass RLS for database operations
    const adminClient = getSupabaseAdminClient();

    // Check if user already has a Stripe customer ID
    const { data: profile, error: profileError } = await adminClient
      .from('profiles')
      .select('stripe_customer_id, email')
      .eq('id', user.id)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json({ error: "Error fetching user profile" }, { status: 500 });
    }

    let customerId = profile?.stripe_customer_id;
    const userEmail = profile?.email || user.email;

    // If user doesn't have a customer ID, create one
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: userEmail,
        metadata: {
          userId: user.id
        }
      });

      customerId = customer.id;

      // Update the user's profile with the customer ID using admin client
      const { error: updateError } = await adminClient
        .from('profiles')
        .update({ stripe_customer_id: customerId })
        .eq('id', user.id);

      if (updateError) {
        console.error('Error updating user profile with customer ID:', updateError);
        return NextResponse.json({ error: "Failed to update user profile" }, { status: 500 });
      }
    } else {
      // Validate existing customer ID with Stripe
      try {
        await stripe.customers.retrieve(customerId);
        console.log(`Validated existing customer: ${customerId}`);
      } catch (stripeError: any) {
        if (stripeError.type === 'StripeInvalidRequestError' && stripeError.code === 'resource_missing') {
          console.log(`Invalid customer ID ${customerId} found, creating new customer`);

          // Create a new customer
          const customer = await stripe.customers.create({
            email: userEmail,
            metadata: {
              userId: user.id
            }
          });

          customerId = customer.id;

          // Update the user's profile with the new customer ID using admin client
          const { error: updateError } = await adminClient
            .from('profiles')
            .update({
              stripe_customer_id: customerId,
              subscription_status: null,
              subscription_plan_type: 'free',
              subscription_current_period_end: null
            })
            .eq('id', user.id);

          if (updateError) {
            console.error('Error updating user profile with new customer ID:', updateError);
            return NextResponse.json({ error: "Failed to update user profile" }, { status: 500 });
          }
        } else {
          // Re-throw other Stripe errors
          throw stripeError;
        }
      }
    }

    // Create the checkout session with subscription settings
    // Define session parameters with proper typing
    const sessionParams: Omit<Stripe.Checkout.SessionCreateParams, 'mode'> & { mode: 'subscription' } = {
      customer: customerId,
      client_reference_id: user.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${req.headers.get('origin')}/subscription-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.get('origin')}/pricing`,
      subscription_data: {
        metadata: {
          userId: user.id
        }
      },
      billing_address_collection: 'required',
      allow_promotion_codes: false
    };

    const checkoutSession = await stripe.checkout.sessions.create(sessionParams);

    // Log the checkout session for debugging
    console.log('Created checkout session:', {
      sessionId: checkoutSession.id,
      customerId,
      userId: user.id,
      planType: plan,
      priceId
    });

    return NextResponse.json({ sessionId: checkoutSession.id, url: checkoutSession.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: "Failed to create checkout session", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
