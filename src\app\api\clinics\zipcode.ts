import fetch from 'node-fetch';

const LOCATIONIQ_API_KEY = process.env.LOCATIONIQ_API_KEY;

interface LocationIQResponse {
  lat: string;
  lon: string;
}

export async function geocodeZipcode(zipcode: string): Promise<{ latitude: number, longitude: number } | null> {
  const url = `https://us1.locationiq.com/v1/search.php?key=${LOCATIONIQ_API_KEY}&postalcode=${zipcode}&country=US&format=json`;

  try {
    const response = await fetch(url);
    
    const data = await response.json() as LocationIQResponse[]; 

    if (data && data.length > 0) {
      return {
        latitude: parseFloat(data[0].lat),
        longitude: parseFloat(data[0].lon),
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching coordinates from LocationIQ:', error);
    return null;
  }
}
