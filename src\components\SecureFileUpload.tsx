import React, { useState, useEffect } from 'react';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { useAuth } from '@/context/AuthContext';

interface FileUploadProps {
  onFileUpload: (fileUrl: string, fileName: string) => void;
  onError: (error: string) => void;
  maxSize?: number; // in MB
  allowedTypes?: string[];
  uploadPath: string;
}

const DEFAULT_ALLOWED_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

const DEFAULT_MAX_SIZE = 5; // 5MB

export const SecureFileUpload: React.FC<FileUploadProps> = ({
  onFileUpload,
  onError,
  maxSize = DEFAULT_MAX_SIZE,
  allowedTypes = DEFAULT_ALLOWED_TYPES,
  uploadPath
}) => {
  const { user } = useAuth();
  const [file, setFile] = useState<File | null>(null);
  const [filePreviewURL, setFilePreviewURL] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    return () => {
      if (filePreviewURL) {
        URL.revokeObjectURL(filePreviewURL);
      }
    };
  }, [filePreviewURL]);

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `Invalid file type. Allowed types: ${allowedTypes.map(type => type.split('/')[1]).join(', ')}`
      };
    }

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return {
        isValid: false,
        error: `File size exceeds ${maxSize}MB limit.`
      };
    }

    // Check file name length and characters
    if (file.name.length > 100) {
      return {
        isValid: false,
        error: 'File name is too long'
      };
    }

    // Check for suspicious file characteristics
    if (file.name.match(/\.(exe|bat|cmd|sh|php|pl|py|js|jar)$/i)) {
      return {
        isValid: false,
        error: 'File type not allowed for security reasons'
      };
    }

    return { isValid: true };
  };

  const sanitizeFileName = (fileName: string): string => {
    // Remove any path traversal attempts
    const baseName = fileName.replace(/^.*[\\\/]/, '');
    
    // Remove special characters and spaces
    const nameWithoutExtension = baseName.substring(0, baseName.lastIndexOf('.'));
    const extension = baseName.substring(baseName.lastIndexOf('.'));
    
    // Replace unsafe characters with underscores
    const sanitizedName = nameWithoutExtension.replace(/[^a-zA-Z0-9-_]/g, '_');
    
    // Add timestamp to ensure uniqueness
    return `${Date.now()}_${sanitizedName}${extension}`;
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    // Validate file
    const validation = validateFile(selectedFile);
    if (!validation.isValid) {
      onError(validation.error || 'Invalid file');
      return;
    }

    // Create safe file object
    const sanitizedFileName = sanitizeFileName(selectedFile.name);
    const safeFile = new File([selectedFile], sanitizedFileName, { type: selectedFile.type });

    setIsUploading(true);
    try {
      // Update state
      setFile(safeFile);
      const objectURL = URL.createObjectURL(safeFile);
      setFilePreviewURL(objectURL);

      // Upload to Firebase
      await handleFirebaseUpload(safeFile);
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Error uploading file');
    } finally {
      setIsUploading(false);
    }
  };

  const handleFirebaseUpload = async (file: File) => {
    if (!user) throw new Error('User must be authenticated to upload files');

    const storage = getStorage();
    const fileRef = ref(storage, `${uploadPath}/${file.name}`);

    // Add security metadata
    const metadata = {
      contentType: file.type,
      customMetadata: {
        uploadedBy: user.uid,
        uploadTimestamp: new Date().toISOString(),
        originalFileName: file.name
      }
    };

    await uploadBytes(fileRef, file, metadata);
    const downloadURL = await getDownloadURL(fileRef);
    onFileUpload(downloadURL, file.name);
  };

  return (
    <div className="space-y-2">
      <label htmlFor="file-upload" className="block font-medium mb-1">
        Upload Resume
      </label>
      <div className="border-2 border-dashed rounded-lg p-4 text-center">
        {isUploading ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2">Processing file...</p>
          </div>
        ) : file ? (
          <div>
            <p className="text-sm">File uploaded: {file.name}</p>
            {filePreviewURL && (
              <a
                href={filePreviewURL}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                View File
              </a>
            )}
            <label
              htmlFor="file-upload"
              className="cursor-pointer text-blue-600 hover:underline ml-4"
            >
              Reupload
            </label>
          </div>
        ) : (
          <div>
            <label
              htmlFor="file-upload"
              className="cursor-pointer text-blue-600 hover:underline"
            >
              <span>Click to upload</span> or drag and drop
            </label>
            <p className="text-sm mt-2 text-gray-500">
              Allowed types: {allowedTypes.map(type => type.split('/')[1]).join(', ')} (MAX. {maxSize}MB)
            </p>
          </div>
        )}
        <input
          type="file"
          id="file-upload"
          onChange={handleFileChange}
          accept={allowedTypes.join(',')}
          className="hidden"
        />
      </div>
    </div>
  );
};