// src/components/AmbassadorGuard.tsx
'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { createClient } from '@supabase/supabase-js';
import { Button } from "@/components/ui/button";
import Link from 'next/link';
import axios from 'axios';
import { useAuth } from "@/context/AuthContext"


interface SignupParams {
    firstName: string;
    lastName: string;
    email: string;
}

// Create a Supabase client
const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
);

export default function AmbassadorGuard({ children }: { children: React.ReactNode }) {
    const pathname = usePathname();
    const [isLoading, setIsLoading] = useState(true);
    const [isAmbassador, setIsAmbassador] = useState<boolean | null>(null);
    const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
    const [signupParams, setSignupParams] = useState<SignupParams>({ firstName: "", lastName: "", email: "" });
    const { user, loading: authLoading } = useAuth()

    // console.log("User:", user)


    // Function to check ambassador status - no localStorage caching
    const checkAmbassadorStatus = async () => {
        try {
            console.log("Checking ambassador status...");
            setIsLoading(true);

            // Get current user
            console.log("Auth result:", user ? "User found" : "No user");

            if (!user) {
                console.log("Not authenticated or auth error");
                setIsAuthenticated(false);
                setIsAmbassador(null);
                setIsLoading(false);
                return;
            }

            setIsAuthenticated(true);

            // console.log("Authenticated user:", user);

            // const { data, error } = await supabase
            //     .from("profiles")
            //     .select("*")
            //     .eq("id", user?.id)
            //     .maybeSingle();

            
            const email = user?.email

            // console.log("User data from Supabase:", data);

            const name = user?.user_metadata.full_name.split(" ")
            console.log("Name:", name)
            const firstName = name[0]
            const lastName = name[1]



            setSignupParams({
                firstName: firstName,
                lastName: lastName,
                email: user?.email,
            });

            if (!email) {
                console.log("No email found for user");
                setIsAmbassador(false); // Assume not ambassador if no email
                setIsLoading(false);
                return;
            }

            console.log("Calling ambassador check API for:", email);

            // Call the API to check if the user is an ambassador
            const response = await axios.post('/api/get_sn_amb', {
                email,
            }, {
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            console.log("API response status:", response.status);

            // Set ambassador status based on API response
            setIsAmbassador(response.status === 200);
            setIsLoading(false);
        } catch (error) {
            console.error("Error checking ambassador status:", error);
            setIsLoading(false);
            setIsAmbassador(false);

        }
    };

    // Check ambassador status on component mount and when pathname changes
    useEffect(() => {
        // Set a timeout to prevent infinite loading
        // const loadingTimeout = setTimeout(() => {
        //     if (isLoading) {
        //         console.warn("Ambassador check timed out - showing content anyway");
        //         setIsLoading(false);
        //     }
        // }, 5000); // 5 seconds timeout

        checkAmbassadorStatus();

        // return () => clearTimeout(loadingTimeout);
    }, [pathname]); // Re-check when pathname changes

    // Open the ambassador registration popup
    const openAmbassadorPopup = (scholar: boolean) => {
        const popupWidth = 600;
        const popupHeight = 500;
        const left = (window.innerWidth - popupWidth) / 2;
        const top = (window.innerHeight - popupHeight) / 2;

        const paramString = `firstname=${encodeURIComponent(
            signupParams.firstName
        )}&lastname=${encodeURIComponent(
            signupParams.lastName
        )}&email=${encodeURIComponent(
            signupParams.email
        )}&scholar=${encodeURIComponent(
            scholar)}`;
            
        // Base64 encode the entire string.
        const base64Params = btoa(paramString);

        const popup = window.open(
            `https://schoolnest.org/ambassadorkln?params=${base64Params}`,
            'SchoolNest Ambassador Registration',
            `width=${popupWidth},height=${popupHeight},top=${top},left=${left},resizable=yes,scrollbars=yes`
        );

        // Listen for messages from the popup
        window.addEventListener('message', (event) => {
            // Check origin for security
            if (event.origin === 'https://klinn.works' || event.origin === 'http://localhost') {
                if (event.data === 'ambassador_registered') {
                    // Recheck ambassador status
                    checkAmbassadorStatus();
                }
            }
        }, false);

        // Poll to check if the popup is closed
        const checkPopupInterval = setInterval(() => {
            if (popup?.closed) {
                clearInterval(checkPopupInterval);
                // Recheck ambassador status when popup is closed
                checkAmbassadorStatus();
            }
        }, 1000);
    };

    // Show loading state
    if (isLoading) {
        return (
            <div className="flex h-screen w-full items-center justify-center">
                <div className="flex flex-col items-center gap-4">
                    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"></div>
                    {/* <p className="text-lg text-gray-600">Checking ambassador status...</p>
                    <Button
                        variant="outline"
                        onClick={() => {
                            setIsLoading(false);
                            setIsAmbassador(true); // For debugging
                        }}
                    >
                        Skip Check (Debug)
                    </Button> */}
                </div>
            </div>
        );
    }

    // Handle unauthenticated users - show them regular content
    if (isAuthenticated === false) {
        return <>{children}</>;
    }

    // If user is not an ambassador, show the ambassador page
    if (isAmbassador === false) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-indigo-900 to-indigo-600 flex flex-col items-center justify-center text-white px-4">
                <div className="max-w-3xl w-full text-center space-y-8">
                    <h1 className="text-4xl md:text-6xl font-bold pt-12">Finish Registration</h1>
                    <p className="text-xl md:text-2xl">
                        To verify that you&apos;re affiliated with an institution (as a student or an administrator), please upload your school&apos;s <strong>bell schedule</strong>. Here&apos;s an <Link href="https://docs.google.com/document/d/1EVkY-H1Y7afI3xvAx_k1oLukYcfEFFlTtWwHy2o5pW0/edit?tab=t.0" className='text-blue-500 underline'>example</Link> of what we&apos;re looking for.
                    </p>
                    

                    <div className='flex flex-col gap-4'>
                        <div className="bg-indigo-800 p-8 rounded-lg shadow-xl">
                            <h2 className="text-2xl font-bold mb-4">Join the SchoolNest + Klinn Scholars Network!</h2>
                            <ul className="text-left list-inside space-y-2 mb-6">
                                <li className="flex">
                                    <span className="mr-2 text-green-500">✔</span>
                                    Boost Your College Applications: Obtain a leadership position and manage your school&apos;s infrastructure
                                </li>
                                <li className="flex">
                                    <span className="mr-2 text-green-500">✔</span>
                                    Exclusive Opportunities: Networking with professionals, access to events, or recommendation letters.
                                </li>
                                <li className="flex">
                                    <span className="mr-2 text-green-500">✔</span>
                                    Skill Development: Public speaking, teamwork, and organizing events
                                </li>
                                <li className="flex">
                                    <span className="mr-2 text-green-500">✔</span>
                                    Community Impact: Being a leader and helping others.
                                </li>
                            </ul>

                            <div className='text-left my-4'>
                                <p>SchoolNest scholars have been admitted to schools such as <strong>Caltech, Brown, UPenn, Georgia Tech, JHU, and Carnegie Mellon!</strong> Klinn scholars have also won multiple <strong>Coca Cola Scholarships</strong>. Do not miss out on this opportunity. As a Klinn scholar, you&apos;ll have access to these resources as long as you serve to be a point of contact at your school where we can setup our infrastructure and bring you the necessary tools. <strong>This is not a major time commitment!</strong></p>

                            </div>
                            <Button
                                className="w-full py-6 text-lg bg-green-600 hover:bg-green-700 transition-colors"
                                onClick={() => openAmbassadorPopup(true)}
                            >
                                Register as Scholar
                            </Button>
                        </div>
                        <div className="bg-indigo-800 p-8 rounded-lg shadow-xl">
                            <h2 className="text-2xl font-bold mb-4">I&apos;d rather be a bum.</h2>
                            <ul className="text-left list-disc list-inside space-y-2 mb-6">
                                <li className="flex">
                                    <span className="mr-2 text-green-500">✔</span>
                                    Save 1 minute of your time
                                </li>
                                <li className="flex">
                                    <span className="mr-2 text-green-500">✔</span>
                                    Miss out on opportunities
                                </li>
                            </ul>

                            <Button
                                className="w-full py-6 text-lg bg-neutral-600 hover:bg-neutral-700 transition-colors"
                                onClick={() => openAmbassadorPopup(false)}
                            >
                                Finish Registering
                            </Button>
                        </div>
                    </div>

                    <p className="text-sm opacity-75">
                        Already registered but not seeing access? Try refreshing the page or contact support.
                    </p>
                </div>
            </div>
        );
    }

    // User is an ambassador, render the children
    return <>{children}</>;
}