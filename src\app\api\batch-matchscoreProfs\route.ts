import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import professorsData from "../../../../public/professors_super_context.json";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

export async function POST(request: Request): Promise<Response> {
  try {
    // Expect payload with userId and an array of professorIds
    const { userId, professorIds } = await request.json();
    if (!userId || !professorIds || !Array.isArray(professorIds)) {
      return NextResponse.json({ error: "Missing userId or professorIds" }, { status: 400 });
    }
    
    // Fetch the user's resume text from the profiles table
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("default_resume_url")
      .eq("id", userId)
      .maybeSingle();

    if (profileError || !profile) {
      return NextResponse.json({ error: "User profile not found" }, { status: 500 });
    }
    const userResumeText = profile.default_resume_url;

    // Filter the professor JSON data based on professorIds
    const filteredProfessors = professorsData.filter((prof) =>
      professorIds.includes(prof.openalex_id)
    );
    
    // Build concatenated text for each professor
    const professorTexts = filteredProfessors.map((prof) =>
      [
        prof.name,
        prof.institution,
        prof.topics ? prof.topics.join(" ") : "",
      ]
        .filter(Boolean)
        .join(" ")
    );

    // Call the Python backend batch endpoint
    const res = await fetch("http://localhost:8000/batch-matchscore", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        user_text: userResumeText,
        item_texts: professorTexts,
      }),
    });
    const { matchScores } = await res.json();

    // Map the scores back to the professor objects
    const responseData = filteredProfessors.map((prof, index) => ({
      ...prof,
      matchScore: matchScores[index] || 0,
    }));

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error computing professor batch match score:", error);
    return NextResponse.json({ error: "Failed to compute professor batch match score" }, { status: 500 });
  }
}
