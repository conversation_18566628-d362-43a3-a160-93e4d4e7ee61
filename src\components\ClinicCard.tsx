"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Phone, Star, Building, Mail, MapPin } from "lucide-react";
import { But<PERSON> } from "./ui/button";

interface ClinicCardProps {
  contact: any;
  unlocked: boolean;
  onUnlock: () => void;
  onSelect: () => void;
}

const ClinicCard: React.FC<ClinicCardProps> = ({ contact, unlocked, onUnlock, onSelect }) => {
  return (
    <Card className="overflow-hidden group border-0 shadow-md hover:shadow-lg transition-shadow duration-300">
      <div className="flex flex-col md:flex-row bg-gradient-to-br from-white to-blue-50">
        {/* Left Side - Clinic Avatar and Distance */}
        <div className="md:w-1/4 p-6 flex flex-col items-center justify-center space-y-3 bg-white border-r border-blue-100">
          <div className="h-20 w-20 rounded-xl bg-blue-50 flex items-center justify-center mb-2 border-2 border-blue-100 shadow-inner">
            <Building className="h-10 w-10 text-blue-600" />
          </div>
          
          <Badge variant="outline" className="border-blue-200 bg-blue-50 text-blue-600 px-6 py-2 rounded-full text-md font-thin">
           {contact.distance ? `${contact.distance.toFixed(1)} km` : "-- km"}
         </Badge>
        
         {/* <div className="flex items-center">
           {[...Array(5)].map((_, i) => (
             <Star
               key={i}
               className={`h-4 w-4 ${i < 4 ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
             />
           ))}
         </div> */}
       </div>

  
        {/* Right Side - Clinic Details */}
        <div className="md:w-3/4 p-6 relative">
          {/* Locked State */}
          {!unlocked && (
            <div className="flex flex-col items-center justify-center h-full">
              <div className="bg-blue-100/50 p-4 rounded-full mb-4">
                <svg className="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">Clinic Profile Locked</h3>
              <p className="text-sm text-gray-500 mb-4 text-center max-w-xs">
                Unlock to view full clinic details and contact information
              </p>
              <Button 
                onClick={onUnlock}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-full shadow-sm transition-all"
              >
                Unlock for 1 Token
              </Button>
            </div>
          )}
  
          {/* Unlocked State */}
          {unlocked && (
            <>
              <div className="pr-20">
                <h3 className="text-xl font-bold text-gray-800 mb-1">{contact.name}</h3>
                <p className="text-sm text-gray-600 mb-3">
                  <span className="inline-block bg-blue-50 text-blue-600 px-2 py-1 rounded-md text-xs mr-2">
                    {contact.specialty || "General Practice"}
                  </span>
                  {contact.address}, {contact.city}
                </p>
                
                {contact.description && (
                  <p className="text-gray-700 text-sm mb-4 line-clamp-2 bg-blue-50/50 p-3 rounded-lg">
                    {contact.description}
                  </p>
                )}

                <div className="flex items-center gap-4 mb-4">
                  {contact.matchScore !== undefined && (
                    <div className="flex items-center text-sm text-gray-600">
                      <Star className="h-4 w-4 mr-1.5 text-blue-600" />
                      Match Score: {(contact.matchScore * 100).toFixed(1)}%
                    </div>
                  )}
                  {contact.distance !== undefined && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-1.5 text-blue-600" />
                      {contact.distance.toFixed(1)} km away
                    </div>
                  )}
                </div>
              </div>
  
              <div className="flex flex-wrap gap-2 mt-4">
                {contact.phone && (
                  <div className="flex items-center text-sm text-gray-600">
                    <Phone className="h-4 w-4 mr-2 text-blue-600" />
                    {contact.phone}
                  </div>
                )}
                
                {contact.email && (
                  <Button 
                    size="sm" 
                    onClick={() => window.location.href = `mailto:${contact.email}`}
                    className="bg-blue-600 hover:bg-blue-700 text-white ml-auto shadow-sm"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                )}
              </div>
  
              <div className="absolute right-6 top-6">
                <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
                  Verified Contact
                </Badge>
              </div>
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

export default ClinicCard;
