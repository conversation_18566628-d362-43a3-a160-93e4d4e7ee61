import React from 'react';

interface ModalProps {
  children: React.ReactNode;
  onClose: () => void;
}

const Modal: React.FC<ModalProps> = ({ children, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gradient-to-bl from-[#6495ED] to-[#7DF9FF] p-4 sm:p-6 md:p-8 rounded-lg shadow-lg relative max-w-sm sm:max-w-md md:max-w-lg w-full">
        <button
          onClick={onClose}
          className="absolute top-0 right-0 mt-2 mr-2 text-gray-500 hover:text-black"
        >
          &#10005;
        </button>
        {children}
      </div>
    </div>
  );
};

export default Modal;
