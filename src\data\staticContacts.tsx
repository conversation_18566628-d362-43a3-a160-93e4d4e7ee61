// src/data/staticContacts.ts

export interface Contact {
    id: number;
    type: "research" | "startup" | "clinic";
    name: string;
    email: string;
    department?: string;
    university?: string;
    bio?: string;
    company?: string;
    description?: string;
    phone?: string;
    interests: string[];
    match_score: number;
  }
  
  export const staticContacts: Contact[] = [
    // Research Contacts
    {
      id: 1,
      type: "research",
      name: "Dr. <PERSON>",
      email: "s<PERSON><PERSON><PERSON>@university.edu",
      department: "Neuroscience",
      university: "Stanford University",
      bio: "Leading researcher in cognitive neuroscience with a focus on memory formation and recall mechanisms.",
      interests: ["neurology", "brain imaging", "cognitive science"],
      match_score: 95,
    },
    {
      id: 2,
      type: "research",
      name: "Dr. <PERSON>",
      email: "<EMAIL>",
      department: "Biomedical Engineering",
      university: "MIT",
      bio: "Pioneering work in biomedical devices and tissue engineering applications for regenerative medicine.",
      interests: ["medical devices", "tissue engineering", "biotech"],
      match_score: 88,
    },
    // Clinics (for example)
    {
      id: 5,
      type: "clinic",
      name: "City Health Clinic",
      email: "<EMAIL>",
      department: "Primary Care",
      description: "Providing top-notch primary care services with a focus on community health.",
      phone: "************",
      interests: ["community health", "primary care", "computer science"],
      match_score: 80,
    },
    {
      id: 6,
      type: "clinic",
      name: "Downtown Medical Center",
      email: "<EMAIL>",
      department: "Emergency & Critical Care",
      description: "A leading clinic offering comprehensive emergency and critical care services.",
      phone: "************",
      interests: ["emergency care", "critical care", "health services"],
      match_score: 78,
    },
  ];
  
  export const interestOptions = [
    { id: "neurology", label: "Neurology" },
    { id: "biotech", label: "Biotech" },
    { id: "startup", label: "Startup" },
    { id: "innovation", label: "Innovation" },
    { id: "community health", label: "Community Health" },
    { id: "health tech", label: "Health Tech" },
    { id: "medical devices", label: "Medical Devices" },
  ];
  