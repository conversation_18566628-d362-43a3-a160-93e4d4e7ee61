'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { auth, db } from '@/../firebase/clientApp';
import { doc, getDoc } from 'firebase/firestore';

export default function LinkingGuard({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    async function checkLinked() {
      const user = auth.currentUser;
      if (user) {
        const userDocRef = doc(db, 'users', user.uid);
        const docSnap = await getDoc(userDocRef);
        // If the user doc doesn't exist or the "linked" field is not true, redirect.
        if (!docSnap.exists() || docSnap.data()?.linked !== true) {
          router.push('/onboarding');
          return;
        }
      }
      setChecked(true);
    }
    checkLinked();
  }, [router, pathname]); // re-run when the URL (pathname) changes

  return <>{children}</>;
}