"use client";

import type React from "react";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "next/font/google";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter } from "next/navigation";
import { supabase } from "../../../supabase/supabaseClient";
import Script from "next/script";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { ArrowR<PERSON>, Shield, Sparkles, Stethoscope } from "lucide-react";

const inter = Inter({ subsets: ["latin"] });

// Typewriter sentences - moved outside component to avoid dependency issues
const sentences = [
  "Your all-in-one medical career platform.",
  "Search for top clinics near you.",
  "Get expert admissions consulting.",
  "Discover relevant extracurriculars.",
  "Advance your medical career with ease.",
];

const GoogleButton: React.FC<{ onClick: () => void; text: string }> = ({ onClick, text }) => {
  return (
    <Button
      onClick={onClick}
      variant="outline"
      className="w-full bg-white/95 backdrop-blur-sm border-white/20 text-gray-700 font-medium py-6 px-4 rounded-xl hover:bg-white hover:scale-[1.02] transition-all duration-300 shadow-lg group mb-4"
    >
      <svg className="mr-3 group-hover:scale-110 transition-transform duration-300" width="20" height="20" viewBox="0 0 18 18">
        <path
          d="M17.64 9.20455c0-.70414-.06364-1.26841-.18182-1.85455H9v3.50818h4.84a4.13182 4.13182 0 01-1.78 2.70636v2.24909h2.875c1.68-1.54818 2.64-3.82636 2.64-6.60909z"
          fill="#4285F4"
        />
        <path
          d="M9 18c2.43 0 4.46591-.80682 5.95455-2.18909l-2.875-2.24909c-.8.54-1.82.855-3.07955.855-2.36 0-4.34818-1.59-5.06545-3.72727H1.01818v2.33455A8.99455 8.99455 0 009 18z"
          fill="#34A853"
        />
        <path
          d="M3.93455 10.6364A5.39636 5.39636 0 013.6 9c0-.61636.21636-1.18545.6-1.63636V4.85364H1.01818A8.99364 8.99364 0 000 9c0 .99.23818 1.93.66818 2.74636L3.93455 10.6364z"
          fill="#FBBC05"
        />
        <path
          d="M9 3.57364c1.32273 0 2.44364.45455 3.35455 1.34818l2.51818-2.51818C13.46273 1.40636 11.43 0.6 9 0.6 5.02 0.6 2.49545 2.37455 1.01818 4.85364l2.91636 2.33455C4.65182 5.16364 6.64545 3.57364 9 3.57364z"
          fill="#EA4335"
        />
      </svg>
      <span className="text-base font-semibold">{text}</span>
    </Button>
  );
};

export default function LoginSignupPage() {
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();

  // Typewriter effect states
  const [currentLineIndex, setCurrentLineIndex] = useState(0);
  const [currentCharIndex, setCurrentCharIndex] = useState(0);
  const [displayedText, setDisplayedText] = useState("");
  const typeSpeed = 40;

  // Google sign-up extra info state
  const [googleExtra, setGoogleExtra] = useState({
    age: "",
    education: "",
  });
  const [googleSignup, setGoogleSignup] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [tosAccepted, setTosAccepted] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    document.title = "Klinn | Portal";
  }, []);

  // Enhanced typewriter effect
  useEffect(() => {
    if (currentLineIndex >= sentences.length) return;

    const currentSentence = sentences[currentLineIndex];
    
    if (currentCharIndex < currentSentence.length) {
      const timeout = setTimeout(() => {
        setCurrentCharIndex(currentCharIndex + 1);
        setDisplayedText(prev => prev + currentSentence[currentCharIndex]);
      }, typeSpeed);
      return () => clearTimeout(timeout);
    } else {
      const timeout = setTimeout(() => {
        setCurrentLineIndex(currentLineIndex + 1);
        setCurrentCharIndex(0);
        setDisplayedText(prev => prev + "\n");
      }, 800);
      return () => clearTimeout(timeout);
    }
  }, [currentLineIndex, currentCharIndex]);

  // Check localStorage for TOS acceptance
  useEffect(() => {
    const accepted = localStorage.getItem("tosAccepted");
    if (accepted) {
      setTosAccepted(true);
    }
  }, []);

  // Handle referral tracking
  useEffect(() => {
    if (typeof window !== "undefined") {
      const searchParams = new URLSearchParams(window.location.search);
      const referral = searchParams.get("referral");
      if (referral) {
        document.cookie = `referral=${referral}; path=/; max-age=${7 * 24 * 60 * 60}`;
        localStorage.setItem("referral", referral);
      }
    }
  }, []);

  // Auth state listener
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setCurrentUser(session.user);

          const { data: profile, error } = await supabase
            .from("profiles")
            .select("onboarding_complete")
            .eq("id", session.user.id)
            .single();

          if (error || !profile) {
            console.error("Error fetching profile:", error);
            router.push("/onboard-signup");
            return;
          }

          const isOnboardingComplete = profile.onboarding_complete;

          if (!isOnboardingComplete) {
            router.push("/onboard-signup");
          } else {
            router.push("/profile-dashboard");
          }
        } else {
          setCurrentUser(null);
        }
      }
    );

    return () => authListener.subscription?.unsubscribe();
  }, [router]);

  const handleAgree = () => {
    setTosAccepted(true);
    setShowModal(false);
    localStorage.setItem("tosAccepted", "true");
  };

  const handleGoogleExtraChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGoogleExtra(prev => ({ ...prev, [name]: value }));
  };

  const handleEducationChange = (value: string) => {
    setGoogleExtra(prev => ({ ...prev, education: value }));
  };

  const handleGoogleSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setErrorMessage("");

    if (!googleExtra.age || !googleExtra.education) {
      setErrorMessage("Please provide your age and education level.");
      return;
    }
    if (!currentUser) {
      setErrorMessage("Authentication still loading. Please wait a moment.");
      return;
    }

    try {
      const { error } = await supabase
        .from("profiles")
        .update({
          age: Number.parseInt(googleExtra.age) || null,
          education: googleExtra.education,
          updated_at: new Date().toISOString(),
        })
        .eq("id", currentUser.id);

      if (error) throw error;
      router.push("/onboard-signup");
    } catch (err: any) {
      setErrorMessage(err.message);
    }
  };

  const handleGoogleLogin = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: `${window.location.origin}/api/auth/google/callback`,
        scopes: "openid email profile https://www.googleapis.com/auth/gmail.send",
        queryParams: {
          access_type: "offline",
          prompt: "consent",
        },
      },
    });
    if (error) {
      console.error("Google OAuth sign-in error:", error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-[#4169E1] via-[#5B7CFA] to-[#93C5FD] font-inter relative overflow-hidden">
      {/* Enhanced background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-white/8 rounded-full blur-2xl"></div>
        
        {/* Floating medical icons */}
        <div className="absolute top-20 left-20 text-white/10 animate-bounce" style={{ animationDelay: '0s', animationDuration: '3s' }}>
          <Stethoscope size={24} />
        </div>
        <div className="absolute top-40 right-32 text-white/10 animate-bounce" style={{ animationDelay: '1s', animationDuration: '4s' }}>
          <Stethoscope size={20} />
        </div>
        <div className="absolute bottom-40 left-32 text-white/10 animate-bounce" style={{ animationDelay: '2s', animationDuration: '3.5s' }}>
          <Stethoscope size={18} />
        </div>
      </div>

      <Navbar />

      <main className="flex-grow flex items-center justify-center p-6 pt-12 min-h-screen relative z-10">
        <div className="container mx-auto max-w-7xl">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center relative">
            {/* Hero Section */}
            <motion.div
              className="text-center lg:text-left space-y-8"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="inline-flex items-center gap-2 bg-white/15 backdrop-blur-sm rounded-full px-5 py-2 text-white/95 text-sm font-semibold border border-white/20"
                >
                  <Sparkles className="w-4 h-4 animate-pulse" />
                  Medical Career Platform
                </motion.div>

                <motion.h1 
                  className="text-5xl lg:text-7xl font-medium text-white leading-tight"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.8 }}
                >
                  Welcome to{" "}
                  <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                    Klinn
                  </span>
                </motion.h1>

                <motion.div 
                  className="space-y-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  <div
                  className={`text-xl lg:text-2xl text-white/95 min-h-[160px] leading-relaxed`}
                >
                  <pre
                    className={`whitespace-pre-wrap font-light ${inter.className}`}
                  >
                    {displayedText}
                    {currentLineIndex < sentences.length && (
                      <motion.span
                        className="inline-block w-0.5 h-7 bg-white ml-1"
                        animate={{ opacity: [1, 0, 1] }}
                        transition={{ duration: 0.8, repeat: Infinity }}
                      />
                    )}
                  </pre>
                </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                  className="grid grid-cols-1 gap-6 pt-8 max-w-3xl mx-auto"
                >
                  {[
                    { 
                      text: "Medical Extracurriculars", 
                      desc: "Discover and engage in activities that enhance your application at all education levels." 
                    },
                    { 
                      text: "Job Board", 
                      desc: "Search through opportunities posted directly by clinics, professors, and startups. Upload your resume and apply to opportunities with the click of a button." 
                    },
                    { 
                      text: "Cold Outreach Dashboard", 
                      desc: "Automate your internship cold outreach. Search through our database of 11,000+ clinics, 1500+ professors, 1000+ startups, and get matched to the perfect fit." 
                    },
                  ].map((item, index) => (
                    <motion.div 
                      key={index} 
                      className="flex flex-col gap-3 bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:bg-white/15 transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                    >
                      <span className="text-white font-medium text-lg">{item.text}</span>
                      <span className="text-white/80 text-sm leading-relaxed font-light">{item.desc}</span>
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </motion.div>

            {/* Vertical Divider */}
            <div className="hidden lg:block absolute ml-12 left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-white/20 to-transparent -translate-x-1/2" />

            {/* Form Section */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="flex justify-center lg:justify-end"
            >
              <Card className="w-full max-w-md bg-white/15 backdrop-blur-xl border-white/25 shadow-2xl">
                <CardContent className="p-8">
                  <AnimatePresence mode="wait">
                    {googleSignup ? (
                      <motion.div
                        key="google-signup"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="text-center mb-6">
                          <h2 className="text-2xl font-bold text-white mb-2">Complete Your Profile</h2>
                          <p className="text-white/80">Just a few more details to get started</p>
                        </div>

                        <form onSubmit={handleGoogleSignUp} className="space-y-6">
                          <div className="space-y-2">
                            <Label htmlFor="age" className="text-white/95 font-medium">
                              Age
                            </Label>
                            <Input
                              type="text"
                              id="age"
                              name="age"
                              placeholder="25"
                              required
                              value={googleExtra.age}
                              onChange={handleGoogleExtraChange}
                              className="bg-white/15 border-white/30 text-white placeholder-white/60 focus:border-white/50 focus:ring-white/25 rounded-xl"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="education" className="text-white/95 font-medium">
                              Education Level
                            </Label>
                            <Select value={googleExtra.education} onValueChange={handleEducationChange}>
                              <SelectTrigger className="bg-white/15 border-white/30 text-white focus:border-white/50 focus:ring-white/25 rounded-xl">
                                <SelectValue placeholder="Select your education level" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="student">Student</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {errorMessage && (
                            <Alert className="bg-red-500/25 border-red-400/40 text-red-100">
                              <AlertDescription>{errorMessage}</AlertDescription>
                            </Alert>
                          )}

                          <Button
                            type="submit"
                            className="w-full bg-white text-[#4169E1] font-bold py-6 rounded-xl hover:bg-white/95 hover:scale-[1.02] transition-all duration-300 shadow-lg group"
                          >
                            Continue with Google
                            <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                          </Button>

                          <div className="text-center">
                            <Button
                              type="button"
                              variant="ghost"
                              onClick={() => setGoogleSignup(false)}
                              className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl"
                            >
                              ← Go back
                            </Button>
                          </div>
                        </form>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="login"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                        className="space-y-6"
                      >
                        <div className="text-center">
                          <h2 className="text-2xl font-bold text-white">Get Started</h2>
                          <p className="text-white/80">Sign in to access your medical career platform</p>
                        </div>

                        <div className="mb-6">
                          <GoogleButton onClick={handleGoogleLogin} text="Continue with Google" />
                        </div>

                        <div className="relative mt-8">
                          <Separator className="bg-white/25" />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="bg-white/15 backdrop-blur-sm px-4 py-2 rounded-full text-white/80 text-sm font-medium border border-white/20">
                              Secure & Fast
                            </span>
                          </div>
                        </div>

                        <div className="text-center space-y-4">
                          <div className="flex items-center justify-center gap-2 text-white/70 text-sm">
                            <Shield className="w-4 h-4" />
                            <span>Protected by industry-standard security</span>
                          </div>

                          <p className="text-white/80 text-sm leading-relaxed">
                            By signing in, you agree to our{" "}
                            <Button
                              type="button"
                              variant="link"
                              onClick={() => setShowModal(true)}
                              className="text-white font-semibold hover:text-white/90 p-0 h-auto underline underline-offset-2"
                            >
                              Terms of Service
                            </Button>
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </main>

      {/* Enhanced Terms of Service Modal */}
      <AnimatePresence>
        {showModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.9, opacity: 0, y: 20 }}
              className="bg-white rounded-2xl p-8 max-w-2xl w-full shadow-2xl border border-gray-100"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-[#4169E1]/10 rounded-full flex items-center justify-center">
                  <Shield className="w-5 h-5 text-[#4169E1]" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900">Terms of Service</h2>
              </div>
              
              <div className="max-h-96 overflow-y-auto mb-6 text-gray-700 space-y-4">
                <p className="leading-relaxed">
                  Welcome to Klinn&apos;s medical career platform. By using our services, you agree to these terms and conditions.
                </p>
                <p className="leading-relaxed">
                  Our platform provides access to medical career resources, clinic information, and consulting services to help advance your medical career.
                </p>
                <p className="leading-relaxed">
                  Your privacy and data security are important to us. We implement industry-standard security measures to protect your information.
                </p>
              </div>
              
              <div className="flex justify-end gap-3">
                <Button 
                  variant="outline" 
                  onClick={() => setShowModal(false)} 
                  className="px-6 rounded-xl"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleAgree} 
                  className="bg-[#4169E1] hover:bg-[#4169E1]/90 px-6 rounded-xl"
                >
                  I Agree
                </Button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <Footer className="bg-transparent text-white p-8 transition-all duration-300 ease-in-out mt-auto relative z-10" />
      {/* <Script 
        async 
        data-uid="397be323b5" 
        src="https://klinn.kit.com/397be323b5/index.js" 
        strategy="afterInteractive" 
      /> */}
    </div>
  );
}