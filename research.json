{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "institution": "Institute of Advanced Research in Artificial Intelligence", "openalex_id": "A5053148274", "topics": ["Computational Drug Discovery Methods", "Hydrology and Watershed Management Studies", "Hydrological Forecasting Using AI", "Flood Risk Assessment and Management", "Neural Networks and Applications", "Machine Learning in Materials Science", "Gene expression and cancer classification", "Machine Learning in Bioinformatics", "Reinforcement Learning in Robotics", "Domain Adaptation and Few-Shot Learning", "Protein Structure and Dynamics", "Adversarial Robustness in Machine Learning", "Cell Image Analysis Techniques", "Genomics and Phylogenetic Studies", "Bioinformatics and Genomic Networks", "Blind Source Separation Techniques", "Topic Modeling", "Explainable Artificial Intelligence (XAI)", "Multimodal Machine Learning Applications", "Genomic variations and chromosomal abnormalities", "Machine Learning and Data Classification", "Generative Adversarial Networks and Image Synthesis", "Anomaly Detection Techniques and Applications", "vaccines and immunoinformatics approaches", "Metabolomics and Mass Spectrometry Studies"], "papers": [{"title": "Long Short-Term Memory", "year": 1997, "doi": "https://doi.org/10.1162/neco.1997.9.8.1735"}, {"title": "GANs Trained by a Two Time-Scale Update Rule Converge to a Local Nash Equilibrium", "year": 2017, "doi": "https://doi.org/10.48550/arxiv.1706.08500"}, {"title": "GANs Trained by a Two Time-Scale Update Rule Converge to a Local Nash Equilibrium", "year": 2017, "doi": null}, {"title": "Fast and Accurate Deep Network Learning by Exponential Linear Units (ELUs)", "year": 2015, "doi": "https://doi.org/10.48550/arxiv.1511.07289"}, {"title": "The Vanishing Gradient Problem During Learning Recurrent Neural Nets and Problem Solutions", "year": 1998, "doi": "https://doi.org/10.1142/s0218488598000094"}, {"title": "Fast and Accurate Deep Network Learning by Exponential Linear Units (ELUs)", "year": 2015, "doi": null}, {"title": "Self-Normalizing Neural Networks", "year": 2017, "doi": "https://doi.org/10.48550/arxiv.1706.02515"}, {"title": "A comprehensive assessment of RNA-seq accuracy, reproducibility and information content by the Sequencing Quality Control Consortium", "year": 2014, "doi": "https://doi.org/10.1038/nbt.2957"}, {"title": "DeepTox: Toxicity Prediction using Deep Learning", "year": 2016, "doi": "https://doi.org/10.3389/fenvs.2015.00080"}, {"title": "LSTM can Solve Hard Long Time Lag Problems", "year": 1996, "doi": null}]}