// components/ProfileUI.tsx

import React, { ChangeEvent } from 'react';
import {
  Mail,
  Briefcase,
  GraduationCap,
  MapPin,
  Link as LinkIcon,
  Edit,
  Save,
  Upload,
  Award,
  School,
  Star,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

// A small helper function to validate URLs
function isValidUrl(url: string): boolean {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// Consultant badge component
const ConsultantBadge = () => (
  <motion.div
    initial={{ scale: 0 }}
    animate={{ scale: 1 }}
    transition={{ type: 'spring', stiffness: 260, damping: 20 }}
    className="absolute top-4 right-4 bg-yellow-400 text-blue-900 px-3 py-1 rounded-full flex items-center shadow-lg"
  >
    <Star className="w-4 h-4 mr-1" />
    <span className="font-semibold">Consultant</span>
  </motion.div>
);

// Updated ProfileContent component
export const ProfileContent = ({
  userData,
  editableFields,
  isEditing,
  handleFieldChange,
  handleEditToggle,
}: {
  userData: {
    education: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  };
  editableFields: {
    intendedMajor?: string;
    college?: string;
    location?: string;
    bio?: string;
    website?: string;
    education: string;
    acceptedColleges?: string;
    attendedSchool?: string;
    testScores?: string;
  };
  isEditing: boolean;
  handleFieldChange: (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleEditToggle: () => void;
}) => {
  // For displaying the LinkedIn/website field as a link if valid
  const linkedInValue = isValidUrl(editableFields.website || '')
    ? (
      <a
        href={editableFields.website}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 underline"
      >
        {editableFields.website}
      </a>
    )
    : (editableFields.website || 'Add your LinkedIn');

  return (
    <div className="pt-20 px-8 pb-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {userData.firstName} {userData.lastName}
          </h1>
          {!isEditing ? (
            <p className="text-xl text-blue-600">{editableFields.intendedMajor || 'Enter your major'}</p>
          ) : (
            <input
              type="text"
              name="intendedMajor"
              value={editableFields.intendedMajor}
              onChange={handleFieldChange}
              placeholder="Enter your intended major"
              className="border rounded px-2 py-1 text-xl"
            />
          )}
        </div>
        <Button
          onClick={handleEditToggle}
          className="mt-4 md:mt-0 bg-blue-500 hover:bg-blue-600 text-white"
        >
          {isEditing ? <Save className="mr-2 h-4 w-4" /> : <Edit className="mr-2 h-4 w-4" />}
          {isEditing ? 'Save Changes' : 'Edit Profile'}
        </Button>
      </div>
      {!isEditing ? (
        <p className="text-gray-700 mb-6">{editableFields.bio || 'Add a short bio'}</p>
      ) : (
        <textarea
          name="bio"
          value={editableFields.bio}
          onChange={handleFieldChange}
          placeholder="Add a short bio"
          className="border rounded w-full px-2 py-1 mb-6"
        />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <ProfileItem icon={Mail} label="Email" value={userData.email} />

        {/* Example of a direct read-only item */}
        {!isEditing ? (
          <ProfileItem
            icon={Briefcase}
            label="School"
            value={editableFields.college || 'Enter your school'}
          />
        ) : (
          <ProfileEditableItem
            icon={Briefcase}
            label="School"
            name="college"
            value={editableFields.college || ''}
            onChange={handleFieldChange}
            placeholder="Enter your school"
          />
        )}

        {/* Example of an editable item */}
        {!isEditing ? (
          <ProfileItem
            icon={GraduationCap}
            label="Education"
            value={editableFields.education || 'Enter your education'}
          />
        ) : (
          <ProfileEditableItem
            icon={GraduationCap}
            label="Education"
            name="education"
            value={editableFields.education || ''}
            onChange={handleFieldChange}
            placeholder="Enter your education"
          />
        )}

        {!isEditing ? (
          <ProfileItem
            icon={MapPin}
            label="Location"
            value={editableFields.location || 'Enter your location'}
          />
        ) : (
          <ProfileEditableItem
            icon={MapPin}
            label="Location"
            name="location"
            value={editableFields.location || ''}
            onChange={handleFieldChange}
            placeholder="Enter your city/state"
          />
        )}

        {/* LinkedIn (website) as link if valid */}
        {!isEditing ? (
          <ProfileItem
            icon={LinkIcon}
            label="LinkedIn"
            // Pass the clickable or plain text
            value={linkedInValue as string | JSX.Element}
          />
        ) : (
          <ProfileEditableItem
            icon={LinkIcon}
            label="LinkedIn"
            name="website"
            value={editableFields.website || ''}
            onChange={handleFieldChange}
            placeholder="Enter your LinkedIn profile"
          />
        )}

        {/* Consultant-specific fields */}
        {userData.role === 'consultant' && (
          <>
            {!isEditing ? (
              <ProfileItem
                icon={Award}
                label="Accepted Colleges/Med Schools"
                value={editableFields.acceptedColleges || 'Not specified'}
              />
            ) : (
              <ProfileEditableItem
                icon={Award}
                label="Accepted Colleges/Med Schools"
                name="acceptedColleges"
                value={editableFields.acceptedColleges || ''}
                onChange={handleFieldChange}
                placeholder="Enter accepted colleges/med schools"
              />
            )}
            {!isEditing ? (
              <ProfileItem
                icon={School}
                label="College/Med School Attended"
                value={editableFields.attendedSchool || 'Not specified'}
              />
            ) : (
              <ProfileEditableItem
                icon={School}
                label="College/Med School Attended"
                name="attendedSchool"
                value={editableFields.attendedSchool || ''}
                onChange={handleFieldChange}
                placeholder="Enter college/med school attended"
              />
            )}
            {!isEditing ? (
              <ProfileItem
                icon={GraduationCap}
                label="SAT/ACT Score"
                value={editableFields.testScores || 'Not specified'}
              />
            ) : (
              <ProfileEditableItem
                icon={GraduationCap}
                label="SAT/ACT Score"
                name="testScores"
                value={editableFields.testScores || ''}
                onChange={handleFieldChange}
                placeholder="Enter SAT/ACT score"
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

// Read-only item
interface ProfileItemProps {
  icon: React.ElementType;
  label: string;
  value: string | JSX.Element;
}

const ProfileItem: React.FC<ProfileItemProps> = ({ icon: Icon, label, value }) => (
  <div className="flex items-center space-x-3 text-gray-700">
    <Icon className="text-blue-500 h-5 w-5 flex-shrink-0" />
    <span className="font-semibold">{label}:</span>
    <span>{value}</span>
  </div>
);

// Editable item
interface ProfileEditableItemProps {
  icon: React.ElementType;
  label: string;
  name: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
}

const ProfileEditableItem: React.FC<ProfileEditableItemProps> = ({
  icon: Icon,
  label,
  name,
  value,
  onChange,
  placeholder,
}) => (
  <div className="flex items-center space-x-3 text-gray-700">
    <Icon className="text-blue-500 h-5 w-5 flex-shrink-0" />
    <span className="font-semibold">{label}:</span>
    <input
      type="text"
      name={name}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className="border rounded px-2 py-1"
    />
  </div>
);
