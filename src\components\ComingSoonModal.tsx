// ComingSoonModal.tsx

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import { addDoc, collection } from 'firebase/firestore';
import { db } from '../../firebase/clientApp';

interface ComingSoonModalProps {
  onClose: () => void;
}

const ComingSoonModal: React.FC<ComingSoonModalProps> = ({ onClose }) => {
  const [email, setEmail] = useState('');

  const handleEmailSubmit = async () => {
    if (!email) {
      alert('Please enter a valid email address.');
      return;
    }

    try {
      // Save the email to the 'notifyupdate' collection in Firestore
      await addDoc(collection(db, 'notifyupdate'), {
        email,
        createdAt: new Date(),
      });
      alert(`Thank you! We'll notify you at ${email} when we launch.`);
      setEmail('');
      onClose();
    } catch (error) {
      console.error('Error saving email:', error);
      alert('There was an error saving your email. Please try again.');
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
      style={{ pointerEvents: 'auto' }}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative text-center text-white px-4 max-w-md mx-auto"
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-white hover:text-gray-300 focus:outline-none"
        >
          <X className="w-6 h-6" />
        </button>

        {/* Modal Content */}
        <h1 className="text-5xl font-bold mb-4">Coming Soon</h1>
        <p className="text-xl mb-8">
          This feature is coming soon!
        </p>
        <div className="space-y-4">
          <p className="text-lg">Expected Launch: 2025</p>
          <p className="text-blue-400">Sign up to be notified when we launch!</p>
          <div className="flex justify-center gap-4">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className="px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleEmailSubmit}
              className="px-6 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition duration-300"
            >
              Notify Me
            </button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ComingSoonModal;
