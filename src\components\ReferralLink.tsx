"use client"

import { useState } from "react"
import { useAuth } from "@/context/AuthContext"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function ReferralLink() {
  const { user } = useAuth()
  const [copySuccess, setCopySuccess] = useState("")

  // Only generate the referral link client-side
  const referralLink =
    typeof window !== "undefined" && user ? `${window.location.origin}/auth?referral=${user.id}` : ""

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(referralLink)
      setCopySuccess("Copied!")
      setTimeout(() => setCopySuccess(""), 2000)
    } catch (err) {
      console.error("Failed to copy referral link:", err)
    }
  }

  return (
    <div className="mt-4 p-4 bg-blue-50 shadow-lg rounded">
      <h3 className="text-lg font-semibold text-blue-900">Invite a Friend</h3>
      <p className="text-blue-700 font-thin">Share your referral link and earn an unlock token when they sign up!</p>
      <div className="flex mt-2">
        <Input readOnly value={referralLink} className="mr-2 bg-white/80" />
        <Button onClick={handleCopy}>Copy Link</Button>
      </div>
      {copySuccess && <span className="text-green-600 text-sm">{copySuccess}</span>}
    </div>
  )
}
