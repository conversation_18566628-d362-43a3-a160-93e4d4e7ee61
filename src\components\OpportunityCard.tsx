'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import {
  MapPinIcon,
  CalendarIcon,
  TagIcon,
  UsersIcon,
  GraduationCapIcon,
  DollarSignIcon,
  XIcon,
  StarIcon,
  BuildingIcon,
  ExternalLinkIcon,
  SparklesIcon,
  Bookmark,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { motion } from 'framer-motion';

// Import Supabase client
import { useAuth } from '@/context/AuthContext';
import { supabase } from '../../supabase/supabaseClient';
import { MedOpportunity } from './types/MedOpportunity';

const OpportunityCard: React.FC<MedOpportunity> = ({ opportunity }) => {
  // Use the "Name" field as the bookmark ID
  const opportunityId = opportunity.Name;

  // Modal open/close state
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Track if user has bookmarked this item
  const [isBookmarked, setIsBookmarked] = useState(false);

  // Current logged-in user
  const { user } = useAuth();

  // ------------------------------------------
  // 1) Check if bookmarked on mount via Supabase
  // ------------------------------------------
  useEffect(() => {
    if (!user) return;
    const fetchBookmarks = async () => {
      const { data: bookmarkRows, error } = await supabase
        .from('bookmarks')
        .select('opportunity_id')
        .eq('user_id', user.id);
      if (error) {
        console.error('Error fetching bookmarks:', error);
      } else if (bookmarkRows) {
        setIsBookmarked(bookmarkRows.some((row: any) => row.opportunity_id === opportunityId));
      }
    };
    fetchBookmarks();
  }, [user, opportunityId]);

  // ------------------------------------------
  // 2) Toggle bookmark using Supabase
  // ------------------------------------------
  const handleToggleBookmark = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Don't open the modal

    if (!user) {
      alert('Please log in to bookmark items.');
      return;
    }

    try {
      // Check if a bookmark for this opportunity exists for the current user
      const { data: existingBookmark, error: fetchError } = await supabase
        .from('bookmarks')
        .select('id')
        .eq('user_id', user.id)
        .eq('opportunity_id', opportunityId)
        .maybeSingle();
      if (fetchError) throw fetchError;

      if (existingBookmark) {
        // Bookmark exists; remove it
        const { error: removeError } = await supabase
          .from('bookmarks')
          .delete()
          .eq('user_id', user.id)
          .eq('opportunity_id', opportunityId);
        if (removeError) throw removeError;
        setIsBookmarked(false);
      } else {
        // Bookmark does not exist; add it
        const { error: addError } = await supabase
          .from('bookmarks')
          .insert({
            user_id: user.id,
            opportunity_id: opportunityId,
          });
        if (addError) throw addError;
        setIsBookmarked(true);
      }
    } catch (err) {
      console.error('Error toggling bookmark:', err);
    }
  };

  // ------------------------------------------
  // 3) Helper functions and UI logic
  // ------------------------------------------
  const locationArray = React.useMemo(() => {
    if (!opportunity.Location) return [];
    return opportunity.Location.split(',').map((loc) => loc.trim());
  }, [opportunity.Location]);

  const getTypeColor = (typeString: string): string => {
    const colors: { [key: string]: string } = {
      Internship: 'bg-blue-100 text-blue-700',
      Fellowship: 'bg-indigo-100 text-indigo-700',
      Research: 'bg-green-100 text-green-700',
      Volunteer: 'bg-orange-100 text-orange-700',
      Job: 'bg-pink-100 text-pink-700',
      Competition: 'bg-red-100 text-red-700',
      'School Club': 'bg-yellow-100 text-yellow-700',
      'Summer Program': 'bg-teal-100 text-teal-700',
      'Nonprofit Organization': 'bg-purple-100 text-purple-700',
      'Educational Program': 'bg-cyan-100 text-cyan-700',
      STEM: 'bg-emerald-100 text-emerald-700',
    };

    const defaultColor = 'bg-blue-50 text-blue-700';
    const types = typeString.split(',').map((t) => t.trim());
    return colors[types[0]] || defaultColor;
  };

  const getCompetitivenessColor = (level: string): string => {
    const rating = parseInt(level.split('/')[0] || '0', 10);
    if (rating >= 4) return 'bg-red-500 text-white';
    if (rating >= 3) return 'bg-yellow-500 text-white';
    return 'bg-green-500 text-white';
  };

  const isEmail = (val: string) => val.includes('@');
  const isPhone = (val: string) => /^\+?[0-9\s\-\(\)]+$/.test(val.trim());
  const getContactHref = (contact: string) => {
    if (isEmail(contact)) return `mailto:${contact.trim()}`;
    if (isPhone(contact)) return `tel:${contact.trim()}`;
    return `${contact.trim()}`;
  };
  const getContactLabel = (contact: string) => {
    if (isEmail(contact)) return 'Email Program';
    if (isPhone(contact)) return 'Call Program';
    return 'Contact Program';
  };

  const Modal = ({ onClose }: { onClose: () => void }) => {
    return createPortal(
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="relative bg-white max-w-3xl w-full max-h-[90vh] overflow-y-auto rounded-xl shadow-xl"
        >
          {/* Header */}
          <div className="p-4 sm:p-6 flex justify-between items-start border-b border-blue-50">
            <div className="flex items-center gap-3">
              <div className="hidden sm:flex w-10 h-10 bg-blue-500 rounded-full items-center justify-center shadow-sm">
                <BuildingIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-light text-blue-800 mb-2">{opportunity.Name}</h2>
                <Badge
                  className={`px-2 py-1 rounded-full text-xs font-light ${getTypeColor(
                    opportunity['Opportunity Type']
                  )}`}
                >
                  <TagIcon className="w-3 h-3 mr-1" />
                  {opportunity['Opportunity Type']}
                </Badge>
              </div>
            </div>
            <button 
              onClick={onClose} 
              className="p-2 hover:bg-blue-50 rounded-full transition-colors"
            >
              <XIcon className="w-5 h-5 text-blue-400" />
            </button>
          </div>

          {/* Body */}
          <div className="px-4 sm:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6">
            {/* Two-column layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              {/* Left: Eligibility */}
              <div className="bg-blue-50/50 p-3 sm:p-4 rounded-lg">
                <h4 className="text-blue-600 font-light mb-3">Eligibility</h4>
                <div className="space-y-3 text-blue-700 text-sm font-light">
                  <div className="flex items-center gap-2">
                    <UsersIcon className="w-4 h-4 text-blue-400" />
                    <span>Ages: {opportunity.Ages || 'N/A'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <GraduationCapIcon className="w-4 h-4 text-blue-400" />
                    <span>Grades: {opportunity.Grades || 'N/A'}</span>
                  </div>
                </div>
              </div>
              {/* Right: Program Details */}
              <div className="bg-blue-50/50 p-3 sm:p-4 rounded-lg">
                <h4 className="text-blue-600 font-light mb-3">Program Details</h4>
                <div className="space-y-3 text-blue-700 text-sm font-light">
                  <div className="flex items-center gap-2">
                    <DollarSignIcon className="w-4 h-4 text-blue-400" />
                    <span>Compensation: {opportunity['Compensation'] || 'N/A'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSignIcon className="w-4 h-4 text-blue-400" />
                    <span>Program Fee: {opportunity['Fee ($)'] || 'N/A'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <StarIcon className="w-4 h-4 text-blue-400" />
                    <span>Competitiveness: {opportunity['Prestige/Competitiveness']}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CalendarIcon className="w-4 h-4 text-blue-400" />
                    <span>Opportunity Date: {opportunity['Opportunity Date'] || 'N/A'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* About */}
            {opportunity.Description && (
              <div className="bg-blue-50/50 p-4 rounded-lg">
                <h4 className="text-blue-600 font-light mb-3">About this Opportunity</h4>
                <p className="text-blue-700 text-sm font-light leading-relaxed">{opportunity.Description}</p>
              </div>
            )}

            {/* Locations */}
            {locationArray.length > 0 && (
              <div className="bg-blue-50/50 p-4 rounded-lg">
                <h4 className="text-blue-600 font-light mb-3">Locations</h4>
                <div className="flex flex-wrap gap-2">
                  {locationArray.map((loc, i) => (
                    <Badge
                      key={i}
                      className="flex items-center gap-1 bg-blue-100 text-blue-700 border-none px-2 py-1 rounded-full text-xs font-light"
                    >
                      <MapPinIcon className="w-3 h-3" />
                      {loc}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 pt-2">
              {opportunity.Website && (
                <a
                  href={opportunity.Website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-5 py-2.5 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors text-sm font-light"
                >
                  Visit Website
                  <ExternalLinkIcon className="w-4 h-4 ml-2" />
                </a>
              )}
              {opportunity.Contact && (
                <a
                  href={getContactHref(opportunity.Contact)}
                  className="inline-flex items-center px-5 py-2.5 bg-blue-50 text-blue-600 rounded-full hover:bg-blue-100 transition-colors text-sm font-light"
                >
                  {getContactLabel(opportunity.Contact)}
                </a>
              )}
            </div>
          </div>
        </motion.div>
      </div>,
      document.body
    );
  };

  // ------------------------------------------
  // 4) Render the card
  // ------------------------------------------
  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-blue-50"
        onClick={() => setIsModalOpen(true)}
      >
        <div className="p-4 sm:p-6">
          {/* Top section: Title + Location Badge */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-0 mb-4">
            <div className="flex items-start sm:items-center gap-3">
              <div className="hidden sm:flex w-10 h-10 bg-blue-500 rounded-full items-center justify-center shadow-sm">
                <BuildingIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="font-light text-lg sm:text-xl text-blue-800 mb-2">
                  {opportunity.Name}
                </h3>
                {locationArray.length > 0 && (
                  <div className="flex gap-1 flex-wrap">
                    <Badge
                      className="bg-blue-50 text-blue-700 border-none px-2 py-0.5 sm:py-1 rounded-full text-xs font-light flex items-center gap-1"
                    >
                      <MapPinIcon className="w-3 h-3" />
                      {locationArray[0]}
                    </Badge>
                    {locationArray.length > 1 && (
                      <span className="text-xs font-light text-blue-500">
                        +{locationArray.length - 1} more
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Right section: Competitiveness & Bookmark */}
            <div className="flex flex-row sm:flex-col items-center sm:items-end justify-between sm:justify-start w-full sm:w-auto gap-2">
              <Badge
                className={`px-2 py-0.5 sm:py-1 rounded-full text-xs font-light ${getCompetitivenessColor(
                  opportunity['Prestige/Competitiveness']
                )}`}
              >
                {opportunity['Prestige/Competitiveness']} Competitive
              </Badge>
              <button
                onClick={handleToggleBookmark}
                className="p-1.5 hover:bg-blue-50 rounded-full transition-colors"
                aria-label="Toggle bookmark"
              >
                <Bookmark
                  className={`w-4 h-4 ${
                    isBookmarked ? 'fill-current text-blue-500' : 'text-blue-300'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Quick Info Grid */}
          <div className="grid grid-cols-2 gap-2 mb-4">
            {[
              {
                icon: TagIcon,
                label: opportunity['Opportunity Type'],
                color: 'text-blue-500',
              },
              {
                icon: UsersIcon,
                label: opportunity.Ages || 'Any Age',
                color: 'text-blue-500',
              },
              {
                icon: CalendarIcon,
                label: opportunity['Opportunity Date'] || 'Flexible',
                color: 'text-blue-500',
              },
              {
                icon: DollarSignIcon,
                label: opportunity['Compensation'] || 'Not specified',
                color: 'text-blue-500',
              },
            ].map((item, i) => (
              <div
                key={i}
                className="bg-blue-50/50 p-2 rounded-lg flex items-center gap-2 hover:bg-blue-100/50 transition-colors"
              >
                <item.icon className={`w-4 h-4 ${item.color}`} />
                <span className="text-xs font-light text-blue-700 truncate">
                  {item.label}
                </span>
              </div>
            ))}
          </div>

          {/* Short Description */}
          {opportunity.Description && (
            <p className="text-blue-600 text-sm leading-relaxed mb-4 line-clamp-2 font-light px-0.5">
              {opportunity.Description}
            </p>
          )}

          {/* Action Button */}
          <div className="flex justify-between items-center">
            <button
              className="w-full sm:w-auto px-4 py-2 text-white rounded-full bg-blue-500 hover:bg-blue-600 transition-all text-sm font-light flex items-center justify-center sm:justify-start gap-1.5"
              onClick={(e) => {
                e.stopPropagation();
                setIsModalOpen(true);
              }}
            >
              <SparklesIcon className="w-3.5 h-3.5" />
              Learn More
            </button>
          </div>
        </div>
      </motion.div>

      {/* Modal */}
      {isModalOpen && <Modal onClose={() => setIsModalOpen(false)} />}
    </>
  );
};

export default OpportunityCard;