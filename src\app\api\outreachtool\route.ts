// File: /app/api/outreachtool/route.ts
import { NextResponse } from "next/server";
import { google } from "googleapis";
import { createClient } from "@supabase/supabase-js";

interface EmailAttachment {
  filename: string;
  mimeType: string;
  content: string; // base64
}

interface EmailRequestBody {
  recipient: string;
  subject: string;
  message: string;
  clinicId?: string;         // ← optional listing ID
  googleAccessToken?: string;
  attachments?: EmailAttachment[];
}

/* plain anon client (used only for auth.getUser) */
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export async function POST(request: Request) {
  let userJwt = "";
  let userId  = "";
  let clinicId = "";

  try {
    /*──────────────────────────────
     * 1️⃣  Parse & validate request
     *──────────────────────────────*/
    const body: EmailRequestBody = await request.json();
    const {
      recipient,
      subject,
      message,
      clinicId: bodyClinicId,
      googleAccessToken,
      attachments,
    } = body;
    clinicId = bodyClinicId ?? "";

    if (!recipient || !subject || !message)
      return NextResponse.json({ error: "Missing fields" }, { status: 400 });

    if (!googleAccessToken)
      return NextResponse.json({ error: "No Google token provided" }, { status: 400 });

    const authHeader = request.headers.get("Authorization") ?? "";
    if (!authHeader.startsWith("Bearer "))
      return NextResponse.json({ error: "Missing Supabase token" }, { status: 401 });

    userJwt = authHeader.slice(7);                        // strip "Bearer "
    const {
      data: { user },
      error: userErr,
    } = await supabase.auth.getUser(userJwt);
    if (userErr || !user)
      return NextResponse.json({ error: "Invalid Supabase auth" }, { status: 401 });

    userId = user.id;

    /*──────────────────────────────
     * 2️⃣  Send e-mail via Gmail API
     *──────────────────────────────*/
    const oauth2Client = new google.auth.OAuth2(
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI
    );
    oauth2Client.setCredentials({ access_token: googleAccessToken });
    const gmail = google.gmail({ version: "v1", auth: oauth2Client });

    const boundary = "klinn_boundary";
    const attachmentParts =
      attachments
        ?.map(
          (a) => `
--${boundary}
Content-Type: ${a.mimeType}; name="${a.filename}"
Content-Disposition: attachment; filename="${a.filename}"
Content-Transfer-Encoding: base64

${a.content}`
        )
        .join("") || "";

    const emailLines = [
      `To: ${recipient}`,
      `Subject: ${subject}`,
      `MIME-Version: 1.0`,
      `Content-Type: multipart/mixed; boundary="${boundary}"`,
      "",
      `--${boundary}`,
      `Content-Type: text/html; charset="UTF-8"`,
      `Content-Transfer-Encoding: 7bit`,
      "",
      message,
      attachmentParts,
      `--${boundary}--`,
    ];
    const encodedEmail = Buffer.from(emailLines.join("\r\n"))
      .toString("base64")
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=+$/, "");

    const gmailRes = await gmail.users.messages.send({
      userId: "me",
      requestBody: { raw: encodedEmail },
    });

    /*──────────────────────────────
     * 3️⃣  Log success (JWT + RLS)
     *──────────────────────────────*/
    const supabaseUser = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      { global: { headers: { Authorization: `Bearer ${userJwt}` } } }
    );
    await supabaseUser.from("email_logs").insert({
      user_id: userId,
      clinic_id: clinicId || null,
      scheduled: false,          // true if this API is the scheduler
      status: "success",
    });

    return NextResponse.json({ success: true, messageId: gmailRes.data.id });
  } catch (err: any) {
    console.error("Outreach error:", err);

    /* optional: log failure as well */
    if (userJwt && userId) {
      const supabaseUser = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        { global: { headers: { Authorization: `Bearer ${userJwt}` } } }
      );
      await supabaseUser.from("email_logs").insert({
        user_id: userId,
        clinic_id: clinicId || null,
        scheduled: false,
        status: "fail",
        error: { message: err.message, code: err.code },
      });
    }

    if (err.code === 401) {
      return NextResponse.json(
        { error: "Google auth expired", code: "auth_expired" },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { error: err.message || "Server error" },
      { status: 500 }
    );
  }
}
