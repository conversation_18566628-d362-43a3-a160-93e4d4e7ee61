import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, XCircle, MapPin, Calendar, X } from 'lucide-react';

export interface ClinicOpening {
  id: string;
  clinicName: string;
  address: string;
  city: string;
  state: string;
  positionTitle: string;
  timeCommitment: string;
  availability: string;
  compensation: string;
  description: string;
  createdAt: any; // Adjust to your proper date type if needed
}

interface ClinicOpeningCardProps {
  opening: ClinicOpening;
  onApprove: (id: string) => void;
  onDeny: (id: string) => void;
}

const ClinicOpeningCard: React.FC<ClinicOpeningCardProps> = ({ opening, onApprove, onDeny }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleCardClick = () => {
    setIsExpanded(true);
  };

  const renderField = (icon: React.ReactNode, label: string, value: string, isLarge: boolean) => (
    <div className="flex items-center text-gray-600 mb-2">
      {icon}
      <span className="text-sm font-medium mr-2">{label}:</span>
      <span className={`text-sm ${!isLarge ? 'truncate' : ''} flex-grow`} title={value}>
        {value || 'N/A'}
      </span>
    </div>
  );

  const ActionButtons = () => (
    <div className="flex justify-end items-center gap-2 mt-2">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onApprove(opening.id);
        }}
        className="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-full transition-colors duration-200 text-sm"
      >
        <CheckCircle className="w-4 h-4 mr-1" />
        Approve
      </button>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDeny(opening.id);
        }}
        className="flex items-center justify-center bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-full transition-colors duration-200 text-sm"
      >
        <XCircle className="w-4 h-4 mr-1" />
        Deny
      </button>
    </div>
  );

  const CardContent = ({ isLarge = false }: { isLarge?: boolean }) => (
    <>
      <div className={`bg-gradient-to-r from-green-500 to-teal-500 p-4 text-white ${isLarge ? 'rounded-t-lg' : 'rounded-t'}`}>
        <div className="flex items-center space-x-4">
          <div className="flex-grow min-w-0">
            <h2 className={`font-bold ${isLarge ? 'text-2xl' : 'text-xl'} truncate`} title={opening.clinicName}>
              {opening.clinicName}
            </h2>
            <span className="bg-white/20 text-white text-xs font-medium px-2 py-1 rounded-full">
              {opening.positionTitle}
            </span>
          </div>
        </div>
      </div>

      <div className={`p-4 space-y-3 ${isLarge ? 'text-base' : 'text-sm'}`}>
        {renderField(
          <MapPin className={`${isLarge ? 'w-6 h-6' : 'w-5 h-5'} mr-2 text-green-500 flex-shrink-0`} />,
          "Location",
          `${opening.city}, ${opening.state}`,
          isLarge
        )}
        {renderField(
          <Calendar className={`${isLarge ? 'w-6 h-6' : 'w-5 h-5'} mr-2 text-green-500 flex-shrink-0`} />,
          "Posted",
          new Date(
            typeof opening.createdAt === 'object' && opening.createdAt.seconds
              ? opening.createdAt.seconds * 1000
              : opening.createdAt
          ).toLocaleDateString(),
          isLarge
        )}
        {renderField(null, "Time Commitment", opening.timeCommitment, isLarge)}
        {renderField(null, "Compensation", opening.compensation, isLarge)}
        {renderField(null, "Availability", opening.availability, isLarge)}
        <div className="text-gray-600">
          <p>{opening.description}</p>
        </div>
        <ActionButtons />
      </div>
    </>
  );

  return (
    <>
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-lg shadow-md overflow-hidden w-full max-w-md mx-auto cursor-pointer relative"
        onClick={handleCardClick}
      >
        <CardContent />
      </motion.div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setIsExpanded(false)}
          >
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0.9 }}
              className="bg-white rounded-lg overflow-hidden max-w-2xl w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                <CardContent isLarge />
                <button
                  onClick={() => setIsExpanded(false)}
                  className="absolute top-2 right-2 text-white hover:text-gray-200 transition-colors duration-200"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ClinicOpeningCard;
