"use client";

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import { ChevronDown } from 'lucide-react';
import clsx from 'clsx';

interface NavDropdownProps {
  title: string;
  icon: React.ComponentType<any>;
  items: Array<{
    href?: string;
    label: string;
    icon?: React.ComponentType<any>;
    disabled?: boolean;
    onClick?: () => void;
  }>;
  isMobile?: boolean;
  isScrolled?: boolean;
  closeMenu?: () => void;
  align?: 'center' | 'start' | 'end';
  width?: string;
}

const CustomNavDropdown: React.FC<NavDropdownProps> = ({
  title,
  icon: Icon,
  items,
  isMobile = false,
  isScrolled = false,
  closeMenu,
  align = 'center',
  width = 'w-44'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleOpen = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsOpen(!isOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // For mobile mode
  if (isMobile) {
    return (
      <div className="w-full" ref={dropdownRef}>
        <Button
          variant="link"
          className={clsx(
            "flex items-center justify-between w-full transition-all duration-200 font-light px-2 py-1",
            "text-white hover:text-white/80"
          )}
          onClick={toggleOpen}
        >
          <div className="flex items-center gap-1">
            <Icon className="flex-shrink-0 h-3.5 w-3.5 opacity-70" />
            <span className="text-sm">{title}</span>
          </div>
          <ChevronDown
            className={clsx(
              "h-3 w-3 transition-transform duration-300",
              isOpen && "transform rotate-180"
            )}
          />
        </Button>

        {isOpen && (
          <div 
            className="pl-6 mt-1 space-y-1 overflow-hidden bg-blue-800/50 rounded-md py-1"
            style={{
              animation: 'fadeIn 0.2s ease-out forwards',
            }}
          >
            {items.map((item, index) => (
              <div key={index} className="overflow-hidden">
                {item.onClick ? (
                  <button
                    className="block w-full text-left text-xs font-light tracking-wide py-1 px-2 transition-all duration-200 text-white/90 hover:text-white hover:bg-blue-700/30"
                    onClick={() => {
                      item.onClick?.();
                      setIsOpen(false);
                      closeMenu?.();
                    }}
                  >
                    <div className="flex items-center gap-1">
                      {item.icon && <item.icon className="h-3 w-3 opacity-70" />}
                      {item.label}
                    </div>
                  </button>
                ) : (
                  <Link
                    href={item.href || '#'}
                    className="block w-full text-xs font-light tracking-wide py-1 px-2 transition-all duration-200 text-white/90 hover:text-white hover:bg-blue-700/30"
                    onClick={() => {
                      setIsOpen(false);
                      closeMenu?.();
                    }}
                  >
                    <div className="flex items-center gap-1">
                      {item.icon && <item.icon className="h-3 w-3 opacity-70" />}
                      {item.label}
                    </div>
                  </Link>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Desktop version
  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <Button
        variant="link"
        className={clsx(
          "flex items-center gap-1 font-light tracking-wide text-sm transition-all duration-200 h-8 px-2",
          "group relative",
          isScrolled
            ? "text-blue-900 hover:text-blue-700"
            : "text-white hover:text-white/80"
        )}
        onClick={toggleOpen}
      >
        <Icon className="flex-shrink-0 h-3.5 w-3.5 opacity-70 group-hover:opacity-100 transition-opacity duration-200" />
        <span className="relative">
          {title}
          <span className={clsx(
            "absolute inset-x-0 bottom-0 h-px transform scale-x-0 transition-transform duration-200 ease-out",
            isScrolled ? "bg-blue-700" : "bg-white",
            "group-hover:scale-x-100 origin-left"
          )}></span>
        </span>
        <ChevronDown 
          className={clsx(
            "h-3 w-3 transition-transform duration-300",
            isOpen && "transform rotate-180"
          )} 
        />
      </Button>

      <div 
        className={clsx(
          "absolute z-50",
          width,
          "mt-1 rounded-md shadow-md",
          "transition-all duration-200",
          !isOpen && "invisible opacity-0 translate-y-2",
          isOpen && "visible opacity-100 translate-y-0",
          align === 'center' && "left-1/2 transform -translate-x-1/2",
          align === 'start' && "left-0",
          align === 'end' && "right-0"
        )}
        style={{
          transformOrigin: align === 'center' ? 'center top' : align === 'start' ? 'left top' : 'right top',
        }}
      >
        {/* Content */}
        <div className="relative bg-white/95 backdrop-blur-lg rounded-md shadow-lg border border-gray-100/50 py-1 overflow-hidden">
          <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-2 w-2 h-2 rotate-45 bg-white/95"></div>
          
          {items.map((item, index) => (
            <div key={index} className="overflow-hidden">
              {item.onClick ? (
                <button
                  className={clsx(
                    "flex w-full items-center px-3 py-1.5 text-xs font-light text-gray-800 hover:text-black hover:bg-gray-50/80 transition-all duration-200",
                    "relative",
                    item.disabled && "opacity-50 pointer-events-none"
                  )}
                  onClick={(e) => {
                    e.stopPropagation();
                    item.onClick?.();
                    setIsOpen(false);
                    closeMenu?.();
                  }}
                >
                  {item.icon && <item.icon className="mr-2 h-3.5 w-3.5 text-gray-500" />}
                  <span>{item.label}</span>
                </button>
              ) : (
                <Link
                  href={item.href!}
                  className={clsx(
                    "flex w-full items-center px-3 py-1.5 text-xs font-light text-gray-800 hover:text-black hover:bg-gray-50/80 transition-all duration-200",
                    "relative",
                    item.disabled && "opacity-50 pointer-events-none"
                  )}
                  onClick={() => {
                    setIsOpen(false);
                    closeMenu?.();
                  }}
                >
                  {item.icon && <item.icon className="mr-2 h-3.5 w-3.5 text-gray-500" />}
                  <span>{item.label}</span>
                </Link>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CustomNavDropdown;