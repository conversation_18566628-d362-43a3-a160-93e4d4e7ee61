# main.py  (replace model load)
from fastapi import FastAPI, UploadFile
from sentence_transformers import SentenceTransformer
import pdfminer.high_level as high_level
import numpy as np

app = FastAPI()
model = SentenceTransformer("sentence-transformers/all-MiniLM-L6-v2")   # 384-dim

def pdf_to_text(file_path: str) -> str:
    return high_level.extract_text(file_path)[:40_000]   # keep ≤40 k chars

@app.post("/embed-pdf")
async def embed_pdf(file: UploadFile):
    contents = await file.read()
    with open("/tmp/tmp.pdf", "wb") as f:
        f.write(contents)
    text = pdf_to_text("/tmp/tmp.pdf")
    vec  = model.encode(text, normalize_embeddings=True).tolist()
    return {"embedding": vec}
