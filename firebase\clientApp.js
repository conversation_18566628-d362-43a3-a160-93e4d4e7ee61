import { initializeApp, getApps, getApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getAuth } from "firebase/auth"; // Import Firebase Auth
import { getStorage } from "firebase/storage"; // Import Firebase Storage
import { initializeAppCheck, ReCaptchaV3Provider } from "firebase/app-check";

// Your Firebase config
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Ensure Firebase is only initialized once
const firebase_app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
const db = getFirestore(firebase_app);
const auth = getAuth(firebase_app); // Initialize Auth
const storage = getStorage(firebase_app); // Initialize Firebase Storage

// TODO: ADD THE RECAPTCHA BACK
// Setup app check with reCAPTCHA v3 by initializing with PUBLIC reCAPTCHA site key

var appCheck = null;
// CLIENT SIDE ONLY RENDERING. Do not run during SSR
if (typeof window !== 'undefined') {
  appCheck = initializeAppCheck(firebase_app, {
    provider: new ReCaptchaV3Provider(process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY),
    isTokenAutoRefreshEnabled: true
  });
}

export { db, auth, storage, appCheck }; // Export Firebase Storage along with Firestore and Auth

// export { db, auth, storage}; 
