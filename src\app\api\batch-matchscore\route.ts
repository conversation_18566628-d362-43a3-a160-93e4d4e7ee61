import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

export async function POST(request: Request) {
  try {
    // Parse the request body
    const { userId, startupIds } = await request.json();
    
    if (!userId || !startupIds || !Array.isArray(startupIds)) {
      return NextResponse.json({ error: "Missing userId or startupIds" }, { status: 400 });
    }
    
    // Fetch user resume from the profiles table
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("default_resume_url")
      .eq("id", userId)
      .maybeSingle();

    if (profileError || !profile) {
      return NextResponse.json({ error: "User profile not found" }, { status: 500 });
    }
    
    const userResumeText = profile.default_resume_url;

    // Fetch startup contacts from Supabase based on startupIds
    const { data: startupContacts, error: startupError } = await supabase
      .from("startup_contacts")
      .select("*")
      .in("id", startupIds);

    if (startupError || !startupContacts) {
      return NextResponse.json({ error: "Startup contacts not found" }, { status: 404 });
    }

    // Concatenate each startup's details into a single text string
    const startupTexts = startupContacts.map((contact) =>
      [
        contact.startup,
        contact.website,
        contact.hq,
        contact.tags,
        contact.company_description,
        contact.first_contact_role,
        contact.first_contact_first_name,
        contact.first_contact_last_name,
        contact.second_contact_role,
        contact.second_contact_first_name,
        contact.second_contact_last_name,
      ]
        .filter(Boolean)
        .join(" ")
    );

    // Call your Python backend batch endpoint to compute match scores
    const res = await fetch("http://localhost:8000/batch-matchscore", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        user_text: userResumeText,
        item_texts: startupTexts,
      }),
    });

    const { matchScores } = await res.json();

    // Map the scores back to the startup contacts
    const responseData = startupContacts.map((contact, index) => ({
      ...contact,
      matchScore: matchScores[index] || 0,
    }));

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error computing batch match score:", error);
    return NextResponse.json({ error: "Failed to compute batch match score" }, { status: 500 });
  }
}
