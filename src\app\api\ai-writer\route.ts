import { Groq } from 'groq-sdk';
import { NextResponse } from 'next/server';

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

interface ScriptwriterInput {
  name: string;
  email: string;
  experience: string;
  education: string;
  strengths: string;
  previousProjects: string;
  targetClinic: string;
}

export async function POST(request: Request) {
  try {
    const inputData: ScriptwriterInput = await request.json();

    const prompt = `
        Create a natural, conversational cold call script for a medical internship opportunity.

        Personal Details:
        - Name: ${inputData.name}
        - Target Clinic: ${inputData.targetClinic}
        - Background: ${inputData.education}
        - Experience: ${inputData.experience}
        - Unique Qualities: ${inputData.strengths}
        - Projects: ${inputData.previousProjects}

        Script Requirements:
        1. Start with: "Hi, is this [${inputData.targetClinic}]?" and include a pause for their response
        2. After their confirmation, continue with "Great..." and the rest of the script
        3. Keep the tone professional yet conversational
        4. Focus on real results and specific experiences
        5. End with a clear next step
        6. Use natural, conversational language
        7. Show respect for the medical professional's time

        Exclude these words: honed, keen, enthusiasm, analytical, valuable, positive impact, asset, objectives, 
        align, strong foundation, contribution, drive, detail-oriented, math-driven, candidate, eagerness, 
        dynamic, enthusiastic, innovative, explore, countless, fresh perspective, showcases, demonstrates, 
        prospect, notable, innovative solutions, esteemed institution, under my belt

        EXCLUDE "Here's a script that meets the requirements:" or anything similar to this at the start.

        Create a script that sounds natural when spoken and focuses on building a genuine connection.
      `;

    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: 'system',
          content: 'You are an expert in creating natural, conversational cold call scripts that build real connections.',
        },
        {
          role: 'user',
          content: prompt,
        },
      ],
      model: 'llama-3.3-70b-versatile',
      temperature: 0.7,
      max_tokens: 500,
      top_p: 1,
    });

    const generatedScript = chatCompletion.choices[0]?.message?.content || 'Unable to generate script. Please try again.';

    return NextResponse.json({
      script: generatedScript,
    });
  } catch (error) {
    console.error('Script Generation Error:', error);
    return NextResponse.json(
      { error: 'Failed to generate script' },
      { status: 500 },
    );
  }
}