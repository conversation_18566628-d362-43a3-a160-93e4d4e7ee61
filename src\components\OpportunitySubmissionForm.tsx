import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';

const OpportunitySubmissionForm = () => {
  return (
    <Card className="w-full max-w-4xl mx-auto bg-white bg-opacity-90 backdrop-filter backdrop-blur-lg shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-2xl font-bold text-blue-800">Submit an Opportunity</CardTitle>
        <Button
          onClick={() => window.open("https://docs.google.com/forms/d/e/1FAIpQLSfTBQCfm-GWsvUWNRT13N-bLDdP2Qw_8l-kFlWK6VyFAZ7QQA/viewform", '_blank')}
          variant="outline"
          className="flex items-center"
        >
          <ExternalLink className="w-4 h-4 mr-2" />
          Open in New Tab
        </Button>
      </CardHeader>
      <CardContent>
        <div className="w-full h-[700px]">
          <iframe
            src={`https://docs.google.com/forms/d/e/1FAIpQLSfTBQCfm-GWsvUWNRT13N-bLDdP2Qw_8l-kFlWK6VyFAZ7QQA/viewform?embedded=true`}
            width="100%"
            height="100%"
            className="rounded-lg shadow-inner"
          >
            Loading…
          </iframe>
        </div>
      </CardContent>
    </Card>
  );
};

export default OpportunitySubmissionForm;