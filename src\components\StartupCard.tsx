"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Building, Globe, Lock, MapPin, Star, Sparkles } from "lucide-react";
import StartupContactCard from "./StartupContactCard";
import { Progress } from "@/components/ui/progress";

export interface StartupContact {
  id: string;
  startup: string;
  website?: string;
  hq?: string;
  tags?: string;
  company_description?: string;
  first_contact_role?: string;
  first_contact_email?: string | null;
  first_contact_first_name?: string;
  first_contact_last_name?: string;
  second_contact_role?: string;
  second_contact_email?: string | null;
  second_contact_first_name?: string;
  second_contact_last_name?: string;
}

interface StartupContactDetails {
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  startup: string;
  website?: string;
  hq?: string;
  startupId: string;
}

interface StartupCardProps {
  contact: StartupContact;
  unlocked: boolean;
  onUnlock: (contact: StartupContact) => void;
  onSelect: (selectedContact: StartupContactDetails) => void;
  freeSelectionUsed?: boolean;
  matchScore?: number; // value between 0 and 1
  selectedContactEmail?: string | null;
}

export const StartupCard: React.FC<StartupCardProps> = ({
  contact,
  unlocked,
  onUnlock,
  onSelect,
  freeSelectionUsed = false,
  matchScore,
  selectedContactEmail: initialSelectedContactEmail = null,
}) => {
  const [selectedContactEmail, setSelectedContactEmail] = useState<string | null>(initialSelectedContactEmail);
  const hasUsedFreeSelection = freeSelectionUsed;

  const handleContactSelect = (selectedContact: StartupContactDetails) => {
    setSelectedContactEmail(selectedContact.email);
    onSelect(selectedContact);
  };

  const handleUnlock = () => {
    onUnlock(contact);
  };

  const isSecondContactSelected = selectedContactEmail === contact.second_contact_email;

  // Helper functions to determine contact states
  const isFirstContactUnlocked = unlocked;
  const isSecondContactUnlocked = unlocked;
  const firstContactRequiresToken = false;
  const secondContactRequiresToken = false;

  return (
    <Card className="border-blue-200">
      <CardContent className="p-6">
        <div className="flex flex-col space-y-4">
          {/* Startup Info */}
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold text-blue-900">{contact.startup}</h3>
              {contact.website && (
                <a
                  href={contact.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  {contact.website}
                </a>
              )}
              {contact.hq && (
                <div className="flex items-center text-sm text-gray-600 mt-1">
                  <MapPin className="h-4 w-4 mr-1" />
                  {contact.hq}
                </div>
              )}
            </div>
            {matchScore !== undefined && (
              <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                <Sparkles className="h-3.5 w-3.5 mr-1" />
                {Math.round(matchScore * 100)}% Match
              </Badge>
            )}
          </div>

          {/* Company Description */}
          {contact.company_description && (
            <p className="text-gray-600 text-sm">{contact.company_description}</p>
          )}

          {/* Tags */}
          {contact.tags && (
            <div className="flex flex-wrap gap-2">
              {contact.tags.split(",").map((tag, index) => (
                <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  {tag.trim()}
                </Badge>
              ))}
            </div>
          )}

          {/* Contacts Section */}
          <div className="mt-4">
            {unlocked ? (
              <div className="space-y-3">
                {/* First Contact */}
                {contact.first_contact_email && (
                  <StartupContactCard
                    contact={{
                      email: contact.first_contact_email,
                      firstName: contact.first_contact_first_name,
                      lastName: contact.first_contact_last_name,
                      role: contact.first_contact_role,
                      startup: contact.startup,
                      website: contact.website,
                      hq: contact.hq,
                      startupId: contact.id,
                    }}
                    onSelect={handleContactSelect}
                    requiresToken={firstContactRequiresToken}
                    showTokenCost={firstContactRequiresToken}
                    isSelected={selectedContactEmail === contact.first_contact_email}
                    isUnlocked={isFirstContactUnlocked}
                    firstContactSelected={hasUsedFreeSelection}
                  />
                )}

                {/* Second Contact */}
                {contact.second_contact_email && (
                  <StartupContactCard
                    contact={{
                      email: contact.second_contact_email,
                      firstName: contact.second_contact_first_name,
                      lastName: contact.second_contact_last_name,
                      role: contact.second_contact_role,
                      startup: contact.startup,
                      website: contact.website,
                      hq: contact.hq,
                      startupId: contact.id,
                    }}
                    onSelect={handleContactSelect}
                    requiresToken={secondContactRequiresToken}
                    showTokenCost={secondContactRequiresToken}
                    isSelected={isSecondContactSelected}
                    isUnlocked={isSecondContactUnlocked}
                    firstContactSelected={hasUsedFreeSelection}
                  />
                )}
              </div>
            ) : (
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <Lock className="h-4 w-4 text-blue-600 mr-2" />
                  <span className="text-sm text-gray-700">Unlock to view contacts</span>
                </div>
                <Button onClick={handleUnlock} size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                  Unlock (1 Token)
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StartupCard;
