// src/app/api/award-referrer/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Get environment variables and ensure they exist.
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Missing required Supabase environment variables.');
}

// Create the Supabase admin client (bypasses RLS)
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey);

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { referrerId } = body;
    if (!referrerId) {
      return NextResponse.json({ error: 'Missing referrerId' }, { status: 400 });
    }

    // Query the referrer's profile
    const { data: referrerProfile, error: fetchError } = await supabaseAdmin
      .from('profiles')
      .select('id, tokens')
      .eq('id', referrerId)
      .maybeSingle();

    if (fetchError) {
      console.error('Error fetching referrer profile:', fetchError);
      return NextResponse.json({ error: 'Error fetching referrer profile' }, { status: 500 });
    }

    if (!referrerProfile) {
      console.error('Referrer profile not found. Referrer ID:', referrerId);
      return NextResponse.json({ error: 'Referrer profile not found' }, { status: 400 });
    }

    const newTokenCount = (referrerProfile.tokens || 0) + 1;

    // Update the referrer's token count
    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({ tokens: newTokenCount, updated_at: new Date().toISOString() })
      .eq('id', referrerId);

    if (updateError) {
      console.error('Could not update referrer token count:', updateError);
      return NextResponse.json({ error: 'Could not update token count' }, { status: 500 });
    }

    return NextResponse.json({ success: true, newTokenCount }, { status: 200 });
  } catch (error: any) {
    console.error('Unexpected error:', error);
    return NextResponse.json({ error: 'Unexpected error' }, { status: 500 });
  }
}
