export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          updated_at?: string
          created_at?: string
          first_name?: string
          last_name?: string
          avatar_url?: string
          username?: string
          website?: string
          school_attended?: string
          expected_graduation?: string
          school_attended_id?: string
          activities_completed?: string[]
          resume?: string
          stripe_customer_id?: string
          stripe_subscription_id?: string
          plan_type?: string
          subscription_status?: string
          subscription_end_date?: string
          email?: string
          current_school?: string
          anticipated_major?: string
          anticipated_degree?: string
          tokens_used?: number
          tokens_earned?: number
          onboarding_completed?: boolean
          referred_by?: string
          stripe_price_id?: string
          is_ambassador?: boolean
          ambassador_code?: string
          projects?: string[]
          skills?: string[]
          organizations?: string[]
          disciplines?: string[]
          education?: string[]
          recommendations?: string[]
          cover_letter?: string
          card?: string
          card_number?: string
          biography?: string
        }
        Insert: {
          id: string
          updated_at?: string
          created_at?: string
          first_name?: string
          last_name?: string
          avatar_url?: string
          username?: string
          website?: string
          school_attended?: string
          expected_graduation?: string
          school_attended_id?: string
          activities_completed?: string[]
          resume?: string
          stripe_customer_id?: string
          stripe_subscription_id?: string
          plan_type?: string
          subscription_status?: string
          subscription_end_date?: string
          email?: string
          current_school?: string
          anticipated_major?: string
          anticipated_degree?: string
          tokens_used?: number
          tokens_earned?: number
          onboarding_completed?: boolean
          referred_by?: string
          stripe_price_id?: string
          is_ambassador?: boolean
          ambassador_code?: string
          projects?: string[]
          skills?: string[]
          organizations?: string[]
          disciplines?: string[]
          education?: string[]
          recommendations?: string[]
          cover_letter?: string
          card?: string
          card_number?: string
          biography?: string
        }
        Update: {
          id?: string
          updated_at?: string
          created_at?: string
          first_name?: string
          last_name?: string
          avatar_url?: string
          username?: string
          website?: string
          school_attended?: string
          expected_graduation?: string
          school_attended_id?: string
          activities_completed?: string[]
          resume?: string
          stripe_customer_id?: string
          stripe_subscription_id?: string
          plan_type?: string
          subscription_status?: string
          subscription_end_date?: string
          email?: string
          current_school?: string
          anticipated_major?: string
          anticipated_degree?: string
          tokens_used?: number
          tokens_earned?: number
          onboarding_completed?: boolean
          referred_by?: string
          stripe_price_id?: string
          is_ambassador?: boolean
          ambassador_code?: string
          projects?: string[]
          skills?: string[]
          organizations?: string[]
          disciplines?: string[]
          education?: string[]
          recommendations?: string[]
          cover_letter?: string
          card?: string
          card_number?: string
          biography?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
} 