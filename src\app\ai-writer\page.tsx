"use client";
import React, { useState, useEffect, FormEvent, ChangeEvent } from 'react';
import { motion } from 'framer-motion';
import { Check, UserPlus } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { MultiStepLoader as Loader } from '@/components/ui/multi-step-loader';
import { TextGenerateEffect } from '@/components/ui/text-generate-effect';

interface ScriptwriterFormData {
  name: string;
  email: string;
  experience: string;
  education: string;
  strengths: string;
  previousProjects: string;
  targetClinic: string;
}

const AIScriptwriter = () => {
  const [scriptwriterFormData, setScriptwriterFormData] = useState<ScriptwriterFormData>({
    name: '',
    email: '',
    experience: '',
    education: '',
    strengths: '',
    previousProjects: '',
    targetClinic: ''
  });

  const [generatedScript, setGeneratedScript] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    document.title = 'Klinn | AI Scriptwriter';
  }, []);

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setScriptwriterFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/ai-writer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scriptwriterFormData)
      });

      if (!response.ok) {
        throw new Error('Script generation failed');
      }

      const data = await response.json();
      await new Promise((resolve) => setTimeout(resolve, 5000));
      setGeneratedScript(data.script);
    } catch (error) {
      console.error('Error generating script:', error);
      alert('There was an error generating the script. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadingStates = [
    {
      text: "Tokenizing your data...",
    },
    {
      text: "Creating embeddings for tokens...",
    },
    {
      text: "Generating query, key, and value matricies...",
    },
    {
      text: "Appending value matricies to embeddings...",
    },
    {
      text: "Generating script using sampler...",
    },
  ];

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-blue-600 to-blue-200 font-sans">
      <Navbar />
      <Loader loadingStates={loadingStates} loading={isLoading} duration={1200} />

      <main className="flex-grow container mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-12"
        >
          <h1 className="text-6xl font-bold text-white mb-4 pt-24">
            AI Scriptwriter
          </h1>
          <p className="text-xl text-white opacity-80">
            Create your personalized 30-second cold call script for medical clinics
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12 pt-12">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white rounded-lg shadow-xl p-8 mt-0 my-auto"
          >
            <h2 className="text-3xl font-semibold mb-6 text-blue-900">
              Why Use Our AI Cold Call Script Generator?
            </h2>
            <ul className="space-y-4">
              {[
                'Create scripts that start real conversations',
                'Highlight what makes you stand out',
                'Generate natural, flowing dialogue',
                'Build connections with medical professionals',
                'Get responses that lead to opportunities',
              ].map((item, index) => (
                <motion.li
                  key={index}
                  className="flex items-center text-blue-700"
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 + index * 0.1 }}
                >
                  <Check className="w-5 h-5 mr-3 text-blue-500 flex-shrink-0" />
                  {item}
                </motion.li>
              ))}
            </ul>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-white rounded-lg shadow-xl p-8"
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              <h2 className="text-3xl font-semibold mb-6 text-blue-900">
                Generate Your Cold Call Script
              </h2>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-blue-700 mb-1"
                  >
                    Your Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={scriptwriterFormData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label
                    htmlFor="targetClinic"
                    className="block text-sm font-medium text-blue-700 mb-1"
                  >
                    Target Clinic
                  </label>
                  <input
                    type="text"
                    id="targetClinic"
                    name="targetClinic"
                    value={scriptwriterFormData.targetClinic}
                    onChange={handleInputChange}
                    required
                    placeholder="Enter clinic name"
                    className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="experience"
                  className="block text-sm font-medium text-blue-700 mb-1"
                >
                  Real-World Experience
                </label>
                <textarea
                  id="experience"
                  name="experience"
                  value={scriptwriterFormData.experience}
                  onChange={handleInputChange}
                  required
                  placeholder="Share your hands-on experience and achievements"
                  className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>

              <div>
                <label
                  htmlFor="education"
                  className="block text-sm font-medium text-blue-700 mb-1"
                >
                  Education Background
                </label>
                <textarea
                  id="education"
                  name="education"
                  value={scriptwriterFormData.education}
                  onChange={handleInputChange}
                  required
                  placeholder="Share your relevant education and training"
                  className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>

              <div>
                <label
                  htmlFor="strengths"
                  className="block text-sm font-medium text-blue-700 mb-1"
                >
                  What Makes You Different
                </label>
                <textarea
                  id="strengths"
                  name="strengths"
                  value={scriptwriterFormData.strengths}
                  onChange={handleInputChange}
                  required
                  placeholder="What sets you apart from others?"
                  className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>

              <div>
                <label
                  htmlFor="previousProjects"
                  className="block text-sm font-medium text-blue-700 mb-1"
                >
                  Notable Projects
                </label>
                <textarea
                  id="previousProjects"
                  name="previousProjects"
                  value={scriptwriterFormData.previousProjects}
                  onChange={handleInputChange}
                  required
                  placeholder="Share your most impactful work"
                  className="w-full px-3 py-2 border border-blue-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 rounded-md focus:outline-none focus:ring-4 focus:ring-blue-400 disabled:opacity-50"
              >
                <UserPlus className="inline-block w-5 h-5 mr-2" />
                {isLoading ? 'Creating Your Script...' : 'Generate Cold Call Script'}
              </button>
            </form>
          </motion.div>
        </div>

        {generatedScript && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mt-12 bg-white rounded-lg shadow-xl p-8"
          >
            <h2 className="text-3xl font-semibold mb-6 text-blue-900">
              Your Personalized Cold Call Script
            </h2>
            <div className="bg-blue-50 p-6 rounded-lg">
              <p className="text-blue-800 whitespace-pre-wrap">
                {generatedScript}
              </p>
              {/* <TextGenerateEffect words={generatedScript} /> */}

            </div>
          </motion.div>
        )}
      </main>
      <Footer className="bg-transparent text-white p-8 transition-all duration-300 ease-in-out mt-auto pt-24" />
    </div>
  );
};

export default AIScriptwriter;