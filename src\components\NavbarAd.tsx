import React, { useEffect, useState } from "react";
import Image from "next/image";
import { ChevronRight } from "lucide-react";
import Navbar from "./Navbar";

interface AdBannerProps {
  title: string;
  subtitle: string;
  logoSrc: string;
  bgColor: string;
  hoverColor: string;
  logoWidth: number;
  logoHeight: number;
  onBannerClick: React.MouseEventHandler<HTMLDivElement>;
  onArrowClick: React.MouseEventHandler<SVGElement>;
}

const AdBanner: React.FC<AdBannerProps> = ({
  title,
  subtitle,
  logoSrc,
  bgColor,
  hoverColor,
  logoWidth,
  logoHeight,
  onBannerClick,
  onArrowClick,
}) => {
  return (
    <div
      className={`relative block w-full ${bgColor} hover:${hoverColor} transition-colors duration-300`}
    >
      <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
        {/* LEFT: Logo + text, opens a link or popup */}
        <div className="flex items-center gap-6 cursor-pointer" onClick={onBannerClick}>
          <Image
            src={logoSrc}
            alt="Logo"
            width={logoWidth}
            height={logoHeight}
            className="object-contain"
          />
          <div className="flex flex-col sm:gap-1">
            <p className="text-white text-base sm:text-lg font-semibold">
              {title}
            </p>
            <p className="text-white/90 text-sm sm:text-base">{subtitle}</p>
          </div>
        </div>

        {/* RIGHT ARROW: flips to next ad */}
        <ChevronRight
          onClick={(e) => {
            e.stopPropagation(); // prevent also opening the link/popup
            onArrowClick(e);
          }}
          className="hidden sm:block text-white/80 h-6 w-6 cursor-pointer"
        />
      </div>
    </div>
  );
};

const FilloutPopup: React.FC<{ popupAdId: string; onClose: () => void }> = ({
  popupAdId,
  onClose,
}) => {
  useEffect(() => {
    const script = document.createElement("script");
    script.src = "https://server.fillout.com/embed/v1/";
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white p-6 rounded-lg w-full max-w-2xl shadow-lg">
        <div
          style={{ width: "100%", height: "500px" }}
          data-fillout-id={popupAdId}
          data-fillout-embed-type="standard"
          data-fillout-inherit-parameters
          data-fillout-dynamic-resize
        ></div>
        <div className="flex justify-end mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

const NavbarWithAd: React.FC = () => {
  const [currentAd, setCurrentAd] = useState(0);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [popupAdId, setPopupAdId] = useState<string | null>(null);

  const ads = [
    {
      title: "Guarantee Yourself A Final Interview For Harvard's Summer Tech Program",
      subtitle: "Sign Up To Receive An Exclusive Access Code",
      logoSrc: "/images/Harvard-logo.png",
      bgColor: "bg-[#A51C30]",
      hoverColor: "hover:bg-[#8B1829]",
      logoWidth: 80,
      logoHeight: 80,
      formId: "vbG3CBwjG2us", // Fillout popup
    },
  ];

  // Rotate ads automatically every 10s
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentAd((prevAd) => (prevAd + 1) % ads.length);
    }, 10000);
    return () => clearInterval(interval);
  }, [ads.length]);

  // Manually flip to the next ad
  const handleNextAd = () => {
    setCurrentAd((prevAd) => (prevAd + 1) % ads.length);
  };

  // If the ad has a `link`, open it in a new tab; otherwise, open the Fillout popup.
  const handleAdClick = () => {
    const ad = ads[currentAd];
    if ("link" in ad && ad.link) {
      // window.open(ad.link, "_blank");
    } else if ("formId" in ad && ad.formId) {
      setPopupAdId(ad.formId);
      setIsPopupOpen(true);
    }
  };

  return (
    <>
      {/* The banner + navbar are fixed at the top */}
      <div id="ad-banner-nav" className="fixed top-0 w-full z-50">
        <div className="relative">
          {/* Ad banner */}
          <AdBanner
            title={ads[currentAd].title}
            subtitle={ads[currentAd].subtitle}
            logoSrc={ads[currentAd].logoSrc}
            bgColor={ads[currentAd].bgColor}
            hoverColor={ads[currentAd].hoverColor}
            logoWidth={ads[currentAd].logoWidth}
            logoHeight={ads[currentAd].logoHeight}
            onBannerClick={handleAdClick}
            onArrowClick={handleNextAd}
          />

          {/* Dot indicators: show which ad is active */}
          <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
            {ads.map((_, index) => (
              <span
                key={index}
                className={`inline-block h-2 w-2 rounded-full ${
                  index === currentAd ? "bg-white" : "bg-white/50"
                }`}
              />
            ))}
          </div>
        </div>

        {/* Your Navbar component below the banner */}
        <Navbar />
      </div>

      {/* Push page content down below the fixed banner */}
      <div style={{ height: "80px" }} />

      {/* Popup for fillout form (only shows for ads with formId) */}
      {isPopupOpen && popupAdId && (
        <FilloutPopup
          popupAdId={popupAdId}
          onClose={() => {
            setIsPopupOpen(false);
            setPopupAdId(null);
          }}
        />
      )}
    </>
  );
};

export default NavbarWithAd;
