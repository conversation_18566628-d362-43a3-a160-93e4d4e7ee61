/// <reference lib="deno.ns" />

import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { differenceInDays, isAfter } from "https://esm.sh/date-fns@2.30.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";

/* monthly allowances */
const ALLOWANCE = {
  free: 5,
  basic_monthly: 15,
  premium_monthly: 50,
} as const;

serve(async () => {
  const supabase = createClient(
    Deno.env.get("SUPABASE_URL")!,
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
  );

  const now     = new Date();
  const nowISO  = now.toISOString();

  try {
    /* STEP 1 — pull only rows the SQL function says are relevant */
    const { data: users, error } =
      await supabase.rpc("profiles_due_for_token_grant");

    if (error) throw error;
    if (!users?.length) return new Response("No eligible profiles found");

    const updates: { id: string; tokens: number; last_token_refresh: string }[] = [];
    const downgrades: string[] = [];

    /* STEP 2 — process each user */
    for (const u of users) {
      /* auto-downgrade if sub expired (branch C) */
      if (
        u.plan_type !== "free" &&
        u.subscription_end_date &&
        new Date(u.subscription_end_date) < now
      ) {
        const { error: dgErr } = await supabase
          .from("profiles")
          .update({ plan_type: "free", subscription_status: "canceled" })
          .eq("id", u.id);
        if (dgErr) throw dgErr;
        downgrades.push(u.id);
        u.plan_type = "free";
      }

      /* decide whether to grant */
      const allowance = ALLOWANCE[u.plan_type as keyof typeof ALLOWANCE] ?? 0;
      let grant = 0;

      if (u.plan_type === "free") {
        const last = u.last_token_refresh
          ? new Date(u.last_token_refresh)
          : new Date(u.created_at);
        if (differenceInDays(now, last) >= 30) grant = allowance;
      } else if (
        u.subscription_status === "active" &&
        u.subscription_end_date &&
        (
          !u.last_token_refresh ||
          isAfter(new Date(u.subscription_end_date), new Date(u.last_token_refresh))
        )
      ) {
        grant = allowance;
      }

      if (grant > 0) {
        updates.push({
          id: u.id,
          tokens: (u.tokens ?? 0) + grant,
          last_token_refresh: nowISO,
        });
      }
    }

    /* STEP 3 — batch the grants */
    for (const row of updates) {
      const { error: updErr } = await supabase
        .from("profiles")
        .update({ tokens: row.tokens, last_token_refresh: row.last_token_refresh })
        .eq("id", row.id);
      if (updErr) throw updErr;
    }

    return new Response(
      `🎉 Granted tokens to ${updates.length} profiles. ` +
      (downgrades.length ? `Downgraded ${downgrades.length} expired subs.` : "")
    );

  } catch (err) {
    return new Response(`Error: ${err.message}`, { status: 500 });
  }
});
