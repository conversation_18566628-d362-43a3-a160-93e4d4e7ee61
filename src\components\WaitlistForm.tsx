'use client';
import React, { useEffect } from 'react';

export default function KlinnForm() {
  useEffect(() => {
    // Load the Fillout embed script after the component mounts
    const script = document.createElement('script');
    script.src = 'https://server.fillout.com/embed/v1/';
    script.async = true;
    document.body.appendChild(script);

    // Clean up the script when the component is unmounted
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div className="w-full h-full"> {/* Removed the max width */}
      {/* Embed the Fillout form here */}
      <div
        className="w-full h-[500px]" 
        data-fillout-id="tkwE7JJ2nkus"
        data-fillout-embed-type="standard"
        data-fillout-inherit-parameters
        data-fillout-dynamic-resize
      ></div>
    </div>
  );
}
