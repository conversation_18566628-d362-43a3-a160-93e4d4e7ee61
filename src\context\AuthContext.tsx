'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../../supabase/supabaseClient';
import { useRouter } from 'next/navigation';

interface AuthContextType {
  user: any;
  loading: boolean;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  loginRedirectPath: string;
  getReturnUrl: () => string | null;
  fetchGmailToken: () => Promise<string>;
}

const DEFAULT_LOGIN_PATH = '/auth';

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  logout: async () => {},
  isAuthenticated: false,
  loginRedirectPath: DEFAULT_LOGIN_PATH,
  getReturnUrl: () => '/',
  fetchGmailToken: async () => '',
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<any>(() => {
    if (typeof window !== 'undefined') {
      const savedUser = localStorage.getItem('authUser');
      return savedUser ? JSON.parse(savedUser) : null;
    }
    return null;
  });
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Helper to get the URL we want to redirect to after a successful login
  const getReturnUrl = () => {
    if (typeof window === 'undefined') return '/profile-dashboard';
    const returnUrl = localStorage.getItem('redirectAfterAuth');
    localStorage.removeItem('redirectAfterAuth');
    return returnUrl || '/profile-dashboard';
  };

  const gmailAccessTokenRef = React.useRef<string | null>(null);

  /** ----------------------------------------------------------------
   **  fetchGmailToken   – always returns a usable Gmail access-token
   ** ---------------------------------------------------------------- */
  const fetchGmailToken = React.useCallback(async (): Promise<string> => {
    // return cached value if it’s still around
    if (gmailAccessTokenRef.current) return gmailAccessTokenRef.current;

    const res  = await fetch("/api/auth/google/refresh");
    const data = await res.json();

    if (data.code === "reauth") {
      // pop the Google consent screen
      await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${window.location.origin}/api/auth/google/callback`,
          scopes: "openid email profile https://www.googleapis.com/auth/gmail.send",
          queryParams: { access_type: "offline", prompt: "consent" },
        },
      });
      throw new Error("reauth");     // caller decides how to show a toast
    }

    if (!res.ok) throw new Error(data.error || "Unknown Gmail token error");

    gmailAccessTokenRef.current = data.access_token as string;
    return gmailAccessTokenRef.current;
  }, []);

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        setLoading(false);
  
        // console.log('Auth state changed =>', event, session);
  
        // When a user is authenticated and email is confirmed
        if (session?.user && session.user.email_confirmed_at) {
          try {
            // Extract metadata (if needed)
            const firstName = session.user.user_metadata?.firstName || '';
            const lastName = session.user.user_metadata?.lastName || '';
            const education = session.user.user_metadata?.education || 'other';
            console.log("Session id: " + session.user.id);
  
            // Check if a profile already exists for this user
            const { data: existingProfile, error: selectProfileError } = await supabase
              .from('profiles')
              .select('id, onboarding_complete')
              .eq('id', session.user.id)
              .maybeSingle();
  
            if (selectProfileError) {
              console.error('Error checking profile:', selectProfileError);
            }
  
            // Determine current path
            const currentPath = window.location.pathname;
  
            // If no profile exists, and we're not already on the onboarding page, redirect
            if (!existingProfile && currentPath !== "/onboard-signup") {
              console.log("No profile found. Redirecting to onboarding...");
              router.push("/onboard-signup");
              return;
            }
  
            // If a profile exists but onboarding is not complete, and we’re not already on the onboarding page
            if (existingProfile && !existingProfile.onboarding_complete && currentPath !== "/onboard-signup") {
              console.log("Onboarding not complete. Redirecting to onboarding...");
              router.push("/onboard-signup");
            } else if (existingProfile && existingProfile.onboarding_complete && currentPath === "/auth") {
              // If the user is fully onboarded and they're on the default auth route, send them to the dashboard
              router.push("/profile-dashboard");
            }
            
            // Save user info in localStorage
            const userToStore = {
              id: session.user.id,
              email: session.user.email,
              user_metadata: session.user.user_metadata,
            };
            localStorage.setItem('authUser', JSON.stringify(userToStore));
  
            // Optionally, set return URL if needed:
            const returnUrl = getReturnUrl();
            if (currentPath === DEFAULT_LOGIN_PATH && returnUrl) {
              router.push(returnUrl);
            }
          } catch (dbErr) {
            console.error('DB error:', dbErr);
          }
        } else if (!session?.user) {
          // If the user signs out, remove stored auth info
          localStorage.removeItem('authUser');
        }
      }
    );
  
    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [router]);
  
  

  const logout = async () => {
    try {
      // First, clear the local state before making the Supabase call
      setUser(null);
      localStorage.removeItem('authUser');
      
      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Redirect after both local state and Supabase are updated
      router.push(DEFAULT_LOGIN_PATH);
    } catch (error: any) {
      console.error('Failed to log out:', error.message);
      throw error;
    }
  };

  const value = {
    user,
    loading,
    logout,
    isAuthenticated: !!user,
    loginRedirectPath: DEFAULT_LOGIN_PATH,
    getReturnUrl,
    fetchGmailToken,
  };

  if (loading) return null;

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within an AuthProvider');
  return context;
}

const setRedirectPath = (path: string) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('redirectAfterAuth', path);
  }
};

export function withAuth<P extends object>(Component: React.ComponentType<P>): React.FC<P> {
  return function WrappedComponent(props: P) {
    const { user, loading, loginRedirectPath } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!loading && !user) {
        setRedirectPath(window.location.pathname);
        router.push(loginRedirectPath);
      }
    }, [user, loading, router, loginRedirectPath]);

    if (loading) return null;
    return user ? <Component {...props} /> : null;
  };
}
