import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';
import { geocodeZipcode } from './zipcode';

interface Clinic {
  id: number;
  name: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  phone: string;
  website: string;
  latitude: number;
  longitude: number;
  distance?: number;
}

// ------------- /app/api/clinics/route.ts ---------------------
export async function GET(req: NextRequest) {
  console.log("API /clinics called");
  const { searchParams } = new URL(req.url);

  /* ------------------------------------------------------------------
   * 1) id list  ?ids=1,7,42
   * ------------------------------------------------------------------ */
  const idsParam = searchParams.get("ids");
  if (idsParam) {
    const ids = idsParam.split(",").map((s) => Number(s.trim()));
    try {
      const clinics = await getClinicsFromCSV();
      return NextResponse.json(clinics.filter((c) => ids.includes(c.id)));
    } catch (err) {
      console.error("ID filter failed:", err);
      return NextResponse.json({ error: "Server error" }, { status: 500 });
    }
  }

  /* ------------------------------------------------------------------
   * 2) default list  ?default=1   → alphabetical (or rating)
   * ------------------------------------------------------------------ */
  if (searchParams.get("default")) {
    try {
      const clinics = await getClinicsFromCSV();
      clinics.sort((a, b) => a.name.localeCompare(b.name));  // alphabetic
      return NextResponse.json(clinics.slice(0, 100));       // cap to 100
    } catch (err) {
      console.error("Default list failed:", err);
      return NextResponse.json({ error: "Server error" }, { status: 500 });
    }
  }

  /* ------------------------------------------------------------------
   * 3) lat/lng proximity  ?lat=..&lng=..&radius=25
   * ------------------------------------------------------------------ */
  const lat  = parseFloat(searchParams.get("lat")  || "");
  const lng  = parseFloat(searchParams.get("lng")  || "");
  const rad  = parseFloat(searchParams.get("radius") || "25"); // km

  if (!isNaN(lat) && !isNaN(lng)) {
    try {
      const clinics = await getClinicsFromCSV();
      const withDist = clinics.map((c) => ({
        ...c,
        distance: calculateDistance(lat, lng, c.latitude, c.longitude),
      }));
      const nearby = withDist
        .filter((c) => c.distance! <= rad)
        .sort((a, b) => a.distance! - b.distance!);

      return NextResponse.json(nearby.slice(0, 100));
    } catch (err) {
      console.error("Lat/Lng search failed:", err);
      return NextResponse.json({ error: "Server error" }, { status: 500 });
    }
  }

  /* ------------------------------------------------------------------
   * 4) zipcode search (original)
   * ------------------------------------------------------------------ */
  const zipcode = searchParams.get("zipcode");
  if (zipcode) {
    try {
      const clinics = await getClinicsFromCSV();
      const sorted  = await filterAndSortClinicsByProximity(clinics, zipcode);
      return NextResponse.json(sorted.slice(0, 20));
    } catch (err) {
      console.error("Zipcode search failed:", err);
      return NextResponse.json({ error: "Server error" }, { status: 500 });
    }
  }

  // no recognised params
  return NextResponse.json({ error: "Bad request" }, { status: 400 });
}


async function getClinicsFromCSV(): Promise<Clinic[]> {
  const filePath = path.join(process.cwd(), 'data', 'data.csv');
  console.log('Attempting to read CSV file from:', filePath);

  try {
    const fileContent = await fs.readFile(filePath, 'utf-8');
    console.log('CSV file read successfully');

    const records = parse(fileContent, { columns: true, skip_empty_lines: true });
    console.log(`Parsed ${records.length} records from CSV`);

    return records.map((record: any, index: number) => ({
      id: index + 1,
      name: record['Site Name'],
      address: record['Site Address'],
      city: record['Site City'],
      state: record['Site State Abbreviation'],
      zipcode: record['Site Postal Code'],
      phone: record['Site Telephone Number'],
      website: record['Site Web Address'] || 'N/A',
      latitude: parseFloat(record['Geocoding Artifact Address Primary Y Coordinate']),
      longitude: parseFloat(record['Geocoding Artifact Address Primary X Coordinate']),
    }));
  } catch (error) {
    console.error('Error reading or parsing CSV:', error);
    throw error;
  }
}

async function filterAndSortClinicsByProximity(clinics: Clinic[], targetZipcode: string): Promise<Clinic[]> {
  let targetCoordinates: { latitude: number, longitude: number } | null = null;
  const targetClinic = clinics.find(clinic => clinic.zipcode === targetZipcode);

  if (targetClinic) {
    targetCoordinates = { latitude: targetClinic.latitude, longitude: targetClinic.longitude };
  } else {
    console.log(`No clinic found with zipcode ${targetZipcode}, fetching coordinates from LocationIQ`);
    targetCoordinates = await geocodeZipcode(targetZipcode); 
    if (!targetCoordinates) {
      console.error('Failed to retrieve coordinates for the provided zipcode');
      return clinics;
    }
  }

  return clinics.map(clinic => ({
    ...clinic,
    distance: calculateDistance(
      targetCoordinates!.latitude,
      targetCoordinates!.longitude,
      clinic.latitude,
      clinic.longitude
    ),
  })).sort((a, b) => a.distance! - b.distance!);
}

function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371;
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) *
    Math.sin(dLon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function deg2rad(deg: number): number {
  return deg * (Math.PI / 180);
}
