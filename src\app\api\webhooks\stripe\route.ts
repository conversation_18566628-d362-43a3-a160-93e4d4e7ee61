import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { headers } from "next/headers";
import { SubscriptionPlan, updateUserSubscription, PRICE_ID_TO_PLAN } from "@/lib/subscription";
import { createClient } from '@supabase/supabase-js';

// For admin operations that bypass RLS
const getSupabaseAdminClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase credentials');
    throw new Error('Missing Supabase credentials');
  }

  return createClient(supabaseUrl, supabaseKey);
};

const getStripeClient = () => {
  const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

  if (!stripeSecretKey) {
    console.error('Missing Stripe secret key');
    throw new Error('Missing Stripe secret key');
  }

  return new Stripe(stripeSecretKey, {
    apiVersion: '2023-10-16' as Stripe.LatestApiVersion,
  });
};

export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  const stripe = getStripeClient();

  try {
    const body = await req.text();
    const headersList = await headers();
    const signature = headersList.get("stripe-signature");

    if (!signature) {
      console.error("No Stripe signature found in webhook request");
      return NextResponse.json({ error: "No Stripe signature found" }, { status: 400 });
    }

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET || ""
      );
    } catch (error: any) {
      console.error(`Webhook signature verification failed: ${error.message}`);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    console.log(`Processing Stripe webhook event: ${event.type}`, {
      eventId: event.id,
      eventType: event.type,
      timestamp: new Date().toISOString(),
    });

    switch (event.type) {
      case "checkout.session.completed":
        await handleCheckoutSessionCompleted(event, stripe);
        break;

      case "invoice.payment_succeeded":
        await handleInvoicePaymentSucceeded(event, stripe);
        break;

      case "customer.subscription.updated":
        await handleSubscriptionUpdated(event, stripe);
        break;

      case "customer.subscription.deleted":
        await handleSubscriptionDeleted(event, stripe);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true, event: event.type });
  } catch (error) {
    console.error(`Error processing webhook: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return NextResponse.json(
      { error: "Failed to process webhook", details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

async function handleCheckoutSessionCompleted(event: Stripe.Event, stripe: Stripe) {
  const session = event.data.object as Stripe.Checkout.Session;

  if (!session.customer || !session.subscription) {
    console.error("Checkout session missing customer or subscription ID", { session_id: session.id });
    return;
  }

  try {
    const userId = session.client_reference_id;
    if (!userId) {
      console.error("No user ID provided in the session", { session_id: session.id });
      throw new Error("No user ID provided in session");
    }

    console.log("Processing checkout session completion", {
      userId,
      sessionId: session.id,
      customerId: session.customer,
      subscriptionId: session.subscription
    });

    const subscriptionData = await stripe.subscriptions.retrieve(
      session.subscription as string
    );

    const endTimestamp = (subscriptionData as any).current_period_end || 0;
    const currentPeriodEnd = new Date(endTimestamp * 1000);

    const priceId = subscriptionData.items.data[0].price.id;

    // Determine the plan type based on the price's product name
    // This is more reliable than using the price ID mapping
    let planType: SubscriptionPlan = "free";

    try {
      const price = await stripe.prices.retrieve(priceId, {
        expand: ['product']
      });

      const productName = (price.product as Stripe.Product).name.toLowerCase();

      if (productName.includes('basic') && productName.includes('monthly')) {
        planType = "basic_monthly";
      } else if (productName.includes('basic') && productName.includes('yearly')) {
        planType = "basic_yearly";
      } else if (productName.includes('premium') && productName.includes('monthly')) {
        planType = "premium_monthly";
      } else if (productName.includes('premium') && productName.includes('yearly')) {
        planType = "premium_yearly";
      }

      console.log('Determined plan type from product name:', planType, 'for product:', productName);
    } catch (error) {
      console.error('Error retrieving price product:', error);
      // Fallback to the price ID mapping
      planType = PRICE_ID_TO_PLAN[priceId] || "free";
    }

    console.log("Retrieved subscription data", {
      priceId,
      planType,
      status: subscriptionData.status,
      endDate: currentPeriodEnd.toISOString()
    });

    await stripe.subscriptions.update(subscriptionData.id, {
      metadata: { userId }
    });

    console.log("About to update user subscription in Supabase", {
      userId,
      planType,
      status: subscriptionData.status
    });

    // Use admin client to directly update the database
    const supabase = getSupabaseAdminClient();

    // First, check if the user exists in the database
    const { data: existingUser, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error checking if user exists:', userError);
      throw new Error(`Failed to check if user exists: ${userError.message}`);
    }

    if (!existingUser) {
      console.error('User not found in database:', userId);
      throw new Error(`User not found in database: ${userId}`);
    }

    console.log('Updating user profile with subscription data:', {
      userId,
      planType,
      status: subscriptionData.status,
      endDate: currentPeriodEnd.toISOString(),
      subscriptionId: subscriptionData.id,
      customerId: session.customer as string
    });

    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        stripe_customer_id: session.customer as string,
        stripe_subscription_id: subscriptionData.id,
        plan_type: planType,
        subscription_status: subscriptionData.status,
        subscription_end_date: currentPeriodEnd.toISOString(),
      })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user profile with subscription:', updateError);
      throw new Error(`Failed to update user profile: ${updateError.message}`);
    }

    // Verify the update was successful
    const { data: updatedProfile, error: verifyError } = await supabase
      .from('profiles')
      .select('plan_type, subscription_status, subscription_end_date')
      .eq('id', userId)
      .single();

    if (verifyError) {
      console.error('Error verifying profile update:', verifyError);
    } else {
      console.log('Successfully updated user profile:', {
        userId,
        planType: updatedProfile.plan_type,
        status: updatedProfile.subscription_status,
        endDate: updatedProfile.subscription_end_date
      });
    }

    // Also use the helper function as a backup
    await updateUserSubscription(userId, {
      stripeCustomerId: session.customer as string,
      stripeSubscriptionId: subscriptionData.id,
      planType,
      status: subscriptionData.status,
      currentPeriodEnd,
    });

    console.log(`Subscription created for user ${userId}`, {
      plan: planType,
      status: subscriptionData.status,
      endDate: currentPeriodEnd.toISOString(),
      subscriptionId: subscriptionData.id,
    });
  } catch (error) {
    console.error("Error handling checkout session:", error);
    if (error instanceof Error) {
      console.error({
        error_message: error.message,
        error_stack: error.stack,
        session_id: session.id,
        customer_id: session.customer,
        subscription_id: session.subscription
      });
    }
    throw error;
  }
}

async function handleInvoicePaymentSucceeded(event: Stripe.Event, stripe: Stripe) {
  const invoice = event.data.object as Stripe.Invoice;
  const subscriptionId = (invoice as any).subscription as string | null;

  if (!subscriptionId) {
    console.error("Invoice missing subscription ID");
    return;
  }

  try {
    const subscriptionData = await stripe.subscriptions.retrieve(subscriptionId);

    const endTimestamp = (subscriptionData as any).current_period_end || 0;
    const currentPeriodEnd = new Date(endTimestamp * 1000);

    let userId: string | undefined = subscriptionData.metadata?.userId;

    const customerId = invoice.customer;
    if (!userId && customerId && typeof customerId === 'string') {
      const supabase = getSupabaseAdminClient();

      const { data: userData, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .single();

      if (error) {
        console.error(`No user found with customer ID: ${customerId}`, error);
      } else if (userData) {
        userId = userData.id;

        if (userId) {
          await stripe.subscriptions.update(subscriptionId, {
            metadata: { userId }
          });
        }
      }
    }

    if (!userId) {
      console.error('Could not determine user ID for subscription', subscriptionId);
      return;
    }

    const priceId = subscriptionData.items.data[0].price.id;

    // Determine the plan type based on the price's product name
    // This is more reliable than using the price ID mapping
    let planType: SubscriptionPlan = "free";

    try {
      const price = await stripe.prices.retrieve(priceId, {
        expand: ['product']
      });

      const productName = (price.product as Stripe.Product).name.toLowerCase();

      if (productName.includes('basic') && productName.includes('monthly')) {
        planType = "basic_monthly";
      } else if (productName.includes('basic') && productName.includes('yearly')) {
        planType = "basic_yearly";
      } else if (productName.includes('premium') && productName.includes('monthly')) {
        planType = "premium_monthly";
      } else if (productName.includes('premium') && productName.includes('yearly')) {
        planType = "premium_yearly";
      }

      console.log('Determined plan type from product name:', planType, 'for product:', productName);
    } catch (error) {
      console.error('Error retrieving price product:', error);
      // Fallback to the price ID mapping
      planType = PRICE_ID_TO_PLAN[priceId] || "free";
    }

    await updateUserSubscription(userId, {
      planType,
      status: subscriptionData.status,
      currentPeriodEnd,
    });

    console.log(`Payment processed for user ${userId}`, {
      plan: planType,
      status: subscriptionData.status,
      endDate: currentPeriodEnd.toISOString(),
      subscriptionId: subscriptionData.id,
      invoiceId: invoice.id,
    });
  } catch (error) {
    console.error("Error handling invoice payment:", error);
    throw error;
  }
}

async function handleSubscriptionUpdated(event: Stripe.Event, stripe: Stripe) {
  const subscription = event.data.object as Stripe.Subscription;

  try {
    let userId = subscription.metadata?.userId;

    const customerId = subscription.customer;
    if (!userId && customerId && typeof customerId === 'string') {
      const supabase = getSupabaseAdminClient();

      const { data: userData, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .single();

      if (error) {
        console.error(`No user found with customer ID: ${customerId}`, error);
      } else if (userData) {
        userId = userData.id;

        if (userId) {
          await stripe.subscriptions.update(subscription.id, {
            metadata: { userId }
          });
        }
      }
    }

    if (!userId) {
      console.error('Could not determine user ID for subscription', subscription.id);
      return;
    }

    const priceId = subscription.items.data[0].price.id;

    // Determine the plan type based on the price's product name
    // This is more reliable than using the price ID mapping
    let planType: SubscriptionPlan = "free";

    try {
      const price = await stripe.prices.retrieve(priceId, {
        expand: ['product']
      });

      const productName = (price.product as Stripe.Product).name.toLowerCase();

      if (productName.includes('basic') && productName.includes('monthly')) {
        planType = "basic_monthly";
      } else if (productName.includes('basic') && productName.includes('yearly')) {
        planType = "basic_yearly";
      } else if (productName.includes('premium') && productName.includes('monthly')) {
        planType = "premium_monthly";
      } else if (productName.includes('premium') && productName.includes('yearly')) {
        planType = "premium_yearly";
      }

      console.log('Determined plan type from product name:', planType, 'for product:', productName);
    } catch (error) {
      console.error('Error retrieving price product:', error);
      // Fallback to the price ID mapping
      planType = PRICE_ID_TO_PLAN[priceId] || "free";
    }

    const endTimestamp = (subscription as any).current_period_end || 0;
    const currentPeriodEnd = new Date(endTimestamp * 1000);

    await updateUserSubscription(userId, {
      planType,
      status: subscription.status,
      currentPeriodEnd,
    });

    console.log(`Subscription updated for user ${userId}`, {
      plan: planType,
      status: subscription.status,
      endDate: currentPeriodEnd.toISOString(),
      subscriptionId: subscription.id,
    });
  } catch (error) {
    console.error("Error handling subscription update:", error);
    throw error;
  }
}

async function handleSubscriptionDeleted(event: Stripe.Event, stripe: Stripe) {
  const subscription = event.data.object as Stripe.Subscription;

  try {
    let userId = subscription.metadata?.userId;

    const customerId = subscription.customer;
    if (!userId && customerId && typeof customerId === 'string') {
      const supabase = getSupabaseAdminClient();

      const { data: userData, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('stripe_customer_id', customerId)
        .single();

      if (error) {
        console.error(`No user found with customer ID: ${customerId}`, error);
      } else if (userData) {
        userId = userData.id;
      }
    }

    if (!userId) {
      console.error('Could not determine user ID for subscription', subscription.id);
      return;
    }

    const endTimestamp = (subscription as any).current_period_end || 0;
    const currentPeriodEnd = new Date(endTimestamp * 1000);

    await updateUserSubscription(userId, {
      planType: "free",
      status: "canceled",
      currentPeriodEnd,
    });

    console.log(`Subscription canceled for user ${userId}`, {
      endDate: currentPeriodEnd.toISOString(),
      subscriptionId: subscription.id,
    });
  } catch (error) {
    console.error("Error handling subscription cancellation:", error);
    throw error;
  }
}