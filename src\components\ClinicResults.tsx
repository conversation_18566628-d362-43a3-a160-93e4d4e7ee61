"use client";
import React, { Suspense, useEffect, useState } from "react";
import { Clinic } from "@/lib/types";
import {
  <PERSON>,
  Card<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
} from "@/components/ui/card";
import { <PERSON>lt<PERSON>, TooltipTrigger, TooltipContent, TooltipProvider } from "@/components/ui/tooltip";
import { ExternalLink, Globe, MapPin, Navigation, Phone, Search } from "lucide-react";
import { AnimatePresence, motion } from "framer-motion";
import { Input } from "./ui/input";

interface ClinicResultsProps {
  clinics: Clinic[];
}

const ClinicResults: React.FC<ClinicResultsProps> = ({ clinics }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredClinics, setFilteredClinics] = useState(clinics);

  useEffect(() => {
    const filtered = clinics.filter((clinic) =>
      clinic.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      clinic.city.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredClinics(filtered);
  }, [searchTerm, clinics]);

  const formatWebsiteUrl = (url: string) => {
    if (url.startsWith("http://") || url.startsWith("https://")) {
      return url;
    }
    return `http://${url}`;
  };

  return (
    <Suspense fallback={<div>Loading clinic results...</div>}>
    <div className="space-y-6 w-full max-w-7xl mx-auto px-4">
      <div className="relative">
        <Input
          type="text"
          placeholder="Search clinics by name or city..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 pr-4 py-3 w-full text-lg rounded-full border-2 border-blue-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
        />
        <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-500 w-5 h-5" />
      </div>

      {/* Clinic List */}
      <div className="space-y-10">
        <AnimatePresence>
          {filteredClinics.map((clinic, index) => (
            <motion.div
              key={clinic.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Card className="bg-white shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden border-l-8 border-blue-500 rounded-lg w-full">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100 p-6">
                  <CardTitle className="text-blue-900 text-3xl font-bold">{clinic.name}</CardTitle>
                </CardHeader>
                <CardContent className="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Clinic Address and Details */}
                  <div className="space-y-4">
                    <div className="flex items-start space-x-4 text-gray-700">
                      <MapPin className="w-6 h-6 text-blue-500 flex-shrink-0 mt-1" />
                      <p className="leading-tight text-lg">{`${clinic.address}, ${clinic.city}, ${clinic.state} ${clinic.zipcode}`}</p>
                    </div>
                    <div className="flex items-center space-x-4 text-gray-700">
                      <Phone className="w-6 h-6 text-blue-500" />
                      <p className="text-lg">{clinic.phone ? clinic.phone : "N/A"}</p>
                    </div>
                  </div>

                  {/* Clinic Website and Distance */}
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4 text-gray-700">
                      <Globe className="w-6 h-6 text-blue-500" />
                      {clinic.website !== "N/A" ? (
                        <a
                          href={formatWebsiteUrl(clinic.website)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 transition-colors duration-200 text-lg underline"
                        >
                          Visit Website
                        </a>
                      ) : (
                        <span className="text-lg">N/A</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-gray-700">
                      <Navigation className="w-6 h-6 text-blue-500" />
                      <p className="text-lg">
                        {clinic.distance !== undefined
                          ? `${clinic.distance.toFixed(2)} km away`
                          : "Distance N/A"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
    </ Suspense>
  );
};

export default ClinicResults;
