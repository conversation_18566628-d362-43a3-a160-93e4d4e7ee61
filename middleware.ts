// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(req: NextRequest) {
  // Create a response object that we’ll return at the end
  const res = NextResponse.next();
  // Create the Supabase client, passing the request/response
  // so it can load/save the auth session.
  const supabase = createMiddlewareClient({ req, res });

  // “Touch” the session to force-load it if present:
  await supabase.auth.getSession();

  // Return response to continue the chain
  return res;
}
