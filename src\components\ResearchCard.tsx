import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Mail, Phone, Star, ExternalLink, BookOpen, MapPin } from "lucide-react";
import { <PERSON><PERSON> } from "./ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface ResearchCardProps {
  contact: {
    name: string;
    email: string;
    institution?: string;
    openalex_id?: string;
    topics?: string[] | string;           // JSON or array
    papers?: { title: string | null; year?: number; doi?: string | null }[] | string; // JSON or array
    phone?: string;
  };
  unlocked: boolean;
  onUnlock: () => void;
  onSelect: () => void;
  matchScore?: number; // between 0 and 1
}

const ResearchCard: React.FC<ResearchCardProps> = ({
  contact,
  unlocked,
  onUnlock,
  onSelect,
  matchScore,
}) => {
  const [tagDialogOpen, setTagDialogOpen] = useState(false);

  // Normalize topics to an array
  let topicsArray: string[] = [];
  try {
    if (Array.isArray(contact.topics)) {
      topicsArray = contact.topics;
    } else if (typeof contact.topics === 'string') {
      topicsArray = JSON.parse(contact.topics) as string[];
    }
  } catch {
    topicsArray = [];
  }

  // Normalize papers to an array
  let papersArray: { title: string | null; year?: number; doi?: string | null }[] = [];
  try {
    if (Array.isArray(contact.papers)) {
      papersArray = contact.papers;
    } else if (typeof contact.papers === 'string') {
      papersArray = JSON.parse(contact.papers) as typeof papersArray;
    }
  } catch {
    papersArray = [];
  }

  const displayScore = matchScore !== undefined ? (matchScore * 100).toFixed(1) : "N/A";
  const numericMatchScore = matchScore ? matchScore * 100 : 0;

  const renderTopics = () => {
    if (topicsArray.length === 0) return null;
    return (
      <div className="flex flex-wrap gap-2 mb-3">
        {topicsArray.slice(0, 3).map((topic, i) => (
          <Badge key={i} variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
            {topic}
          </Badge>
        ))}
        {topicsArray.length > 3 && (
          <Badge
            variant="outline"
            className="text-gray-500 cursor-pointer hover:bg-gray-100"
            onClick={() => setTagDialogOpen(true)}
          >
            +{topicsArray.length - 3} more
          </Badge>
        )}
      </div>
    );
  };

  const renderAllTopicsInline = () => {
    if (topicsArray.length === 0) return null;
    return (
      <div className="flex items-center flex-wrap gap-2">
        {topicsArray.map((topic, i) => (
          <Badge key={i} variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
            {topic}
          </Badge>
        ))}
      </div>
    );
  };

  const renderPapers = () => {
    if (papersArray.length === 0) return null;
    return (
      <div className="mt-4 space-y-2">
        <h4 className="text-sm font-medium text-gray-800 flex items-center gap-1.5">
          <BookOpen className="h-4 w-4 text-blue-600" />
          Selected Publications
        </h4>
        <ul className="space-y-2">
          {papersArray.slice(0, 3).map((paper, i) => (
            <li key={i} className="bg-blue-50 p-2 rounded-md text-sm">
              <div className="flex items-start">
                <div className="flex-1">
                  {paper.title}
                  {paper.year && <span className="text-gray-500 ml-1">({paper.year})</span>}
                </div>
                {paper.doi && (
                  <a
                    href={paper.doi}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-700 ml-2 flex-shrink-0"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <>
      <Card className="overflow-hidden group hover:shadow-md transition-shadow duration-200 border-blue-100">
        <div className="flex flex-col md:flex-row">
          {/* Match Score */}
          <div className="md:w-1/5 bg-gradient-to-b from-blue-50 to-blue-100 p-4 flex flex-col items-center justify-center border-b md:border-b-0 md:border-r border-blue-200">
            <Avatar className="h-20 w-20 mb-4 ring-2 ring-white ring-offset-2 ring-offset-blue-100">
              <AvatarImage src="" alt={unlocked ? contact.name : "Researcher"} />
              <AvatarFallback className="bg-blue-600 text-white text-lg">
                {unlocked ? contact.name.split(' ').map(n => n[0]).join('') : 'R'}
              </AvatarFallback>
            </Avatar>
            <div className="text-center">
              <div className="text-sm font-medium text-blue-600 mb-1">Match Score</div>
              <div className="flex flex-col items-center">
                <div className="flex items-center justify-center mb-1">
                  <span className="text-xl font-semibold text-blue-700">
                    {displayScore}{typeof displayScore === 'string' ? '' : '%'}
                  </span>
                  <Star className="h-4 w-4 text-yellow-400 ml-1" fill="currentColor" />
                </div>
                <Progress value={numericMatchScore} className="h-1.5 w-16 bg-blue-200" />
              </div>
            </div>
          </div>
          {/* Details */}
          <div className="md:w-4/5 p-5 relative bg-white">
            <div className="pr-24">
              <h3 className="text-xl font-semibold text-gray-800 mb-1">
                {unlocked ? contact.name : 'Researcher Profile'}
              </h3>
              {contact.institution && (
                <p className="text-gray-600 flex items-center gap-1.5 mb-2">
                  <MapPin className="h-3.5 w-3.5 text-blue-400" />
                  {contact.institution}
                </p>
              )}
            </div>
            {unlocked ? (
              <>
                <div className="flex flex-wrap gap-4 mb-3">
                  <div className="flex items-center text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-md">
                    <Mail className="h-4 w-4 mr-1.5 text-blue-600" />
                    <span>{contact.email}</span>
                  </div>
                  {contact.phone && (
                    <div className="flex items-center text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-md">
                      <Phone className="h-4 w-4 mr-1.5 text-blue-600" />
                      <span>{contact.phone}</span>
                    </div>
                  )}
                </div>
                {renderTopics()}
                {renderPapers()}
                <Button className="absolute right-5 top-5 bg-blue-600 hover:bg-blue-700" size="sm" onClick={onSelect}>
                  <Mail className="h-4 w-4 mr-2" />Contact
                </Button>
              </>
            ) : (
              <div className="mt-4">
                {renderTopics()}
                <div className="mt-4 flex items-center">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="default" size="sm" onClick={onUnlock} className="bg-blue-600 hover:bg-blue-700">
                          Unlock Info (1 Token)
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="bg-blue-600 text-white">
                        <p>Unlock to view contact details and full profile</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Topics Dialog */}
      <Dialog open={tagDialogOpen} onOpenChange={setTagDialogOpen}>
        <DialogContent className="sm:max-w-[900px] rounded-lg">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-blue-600" />
              Research Topics
            </DialogTitle>
          </DialogHeader>
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            {renderAllTopicsInline()}
          </div>
          <DialogFooter className="mt-4">
            <Button variant="outline" size="sm" onClick={() => setTagDialogOpen(false)} className="border-blue-200 text-blue-700 hover:bg-blue-50">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ResearchCard;
