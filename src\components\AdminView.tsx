import React, { useEffect, useState } from 'react';
import { supabase } from '../../supabase/supabaseClient'; // Ensure your Supabase client is initialized correctly
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Footer from './Footer';
import ClinicOpeningCard, { ClinicOpening } from './ClinicOpeningCard';
import { RefreshCw, Briefcase } from 'lucide-react';

export const AdminView = () => {
  const [clinicOpenings, setClinicOpenings] = useState<ClinicOpening[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchClinicOpenings();
  }, []);

  const fetchClinicOpenings = async () => {
    setIsLoading(true);
    const { data, error } = await supabase
      .from('clinic_openings') // Use your actual table name
      .select('*')
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching clinic openings:', error);
    } else if (data) {
      // Map snake_case fields from Supabase to camelCase expected by the component.
      const transformed: ClinicOpening[] = data.map((item: any) => ({
        id: item.id,
        clinicName: item.clinic_name, // mapping field
        address: item.address,
        city: item.city,
        state: item.state,
        positionTitle: item.position_title,
        timeCommitment: item.time_commitment,
        availability: item.availability,
        compensation: item.compensation,
        description: item.description,
        createdAt: item.created_at,
      }));
      setClinicOpenings(transformed);
    }
    setIsLoading(false);
  };

  const approveClinicOpening = async (openingId: string) => {
    const { error } = await supabase
      .from('clinic_openings')
      .update({ status: 'approved' })
      .eq('id', openingId);

    if (error) {
      console.error('Error approving clinic opening:', error);
      alert('Error approving clinic opening.');
    } else {
      alert('Clinic opening approved.');
      fetchClinicOpenings();
    }
  };

  const denyClinicOpening = async (openingId: string) => {
    const { error } = await supabase
      .from('clinic_openings')
      .update({ status: 'rejected' })
      .eq('id', openingId);

    if (error) {
      console.error('Error denying clinic opening:', error);
      alert('Error denying clinic opening.');
    } else {
      alert('Clinic opening denied.');
      fetchClinicOpenings();
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-b from-[#00A4E4] to-[#FEFEFE]">
      <Navbar />
      <section className="flex-grow flex items-start justify-center px-4 py-8 min-h-screen">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-xl shadow-xl overflow-hidden w-full max-w-7xl mt-24"
        >
          <div className="p-8">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-4xl font-extrabold text-gray-900 flex items-center">
                <Briefcase className="w-10 h-10 mr-4 text-blue-600" />
                Clinic Openings
              </h1>
              <button
                onClick={fetchClinicOpenings}
                className="flex items-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-full shadow-md transition-colors duration-200"
              >
                <RefreshCw className="w-5 h-5 mr-2" />
                Refresh
              </button>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : clinicOpenings.length === 0 ? (
              <div className="text-center py-16">
                <p className="text-2xl font-semibold text-gray-700">
                  No pending clinic openings.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                {clinicOpenings.map((opening) => (
                  <ClinicOpeningCard
                    key={opening.id}
                    opening={opening}
                    onApprove={approveClinicOpening}
                    onDeny={denyClinicOpening}
                  />
                ))}
              </div>
            )}
          </div>
        </motion.div>
      </section>
      <Footer className="bg-transparent text-white mt-auto" />
    </div>
  );
};
