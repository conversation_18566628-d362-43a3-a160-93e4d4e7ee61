import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { checkSubscriptionLimits } from "@/lib/middleware/check-subscription-limits";
import { createClient } from '@supabase/supabase-js';
// import OpenAI from "openai";

// // For admin operations that bypass RLS
// const getSupabaseAdminClient = () => {
//   const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
//   const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

//   if (!supabaseUrl || !supabaseKey) {
//     console.error('Missing Supabase credentials');
//     throw new Error('Missing Supabase credentials');
//   }

//   return createClient(supabaseUrl, supabaseKey);
// };

// // Initialize OpenAI client
// const openai = new OpenAI({
//   apiKey: process.env.OPENAI_API_KEY,
// });

// export async function POST(req: NextRequest) {
//   try {
//     // Check subscription limits
//     const { allowed, response, userId, currentUsage, newUsage } = await checkSubscriptionLimits(
//       req,
//       "coldCallScripts",
//       1
//     );

//     if (!allowed || !userId) {
//       return response || NextResponse.json({ error: "Access denied" }, { status: 403 });
//     }

//     // Parse request body
//     const { company, position, background } = await req.json();

//     if (!company || !position) {
//       return NextResponse.json(
//         { error: "Missing required fields" },
//         { status: 400 }
//       );
//     }

//     // Generate cold call script using OpenAI
//     const prompt = `
//       Generate a professional cold call script for reaching out to ${company} about a ${position} position.

//       ${background ? `My background: ${background}` : ""}

//       The script should:
//       1. Be concise and professional
//       2. Include a brief introduction
//       3. Express interest in the position
//       4. Highlight 1-2 relevant skills or experiences
//       5. Request a conversation or interview
//       6. Include a polite closing

//       Format the script as a complete phone call dialogue that I can use directly.
//     `;

//     const completion = await openai.chat.completions.create({
//       model: "gpt-3.5-turbo",
//       messages: [
//         {
//           role: "system",
//           content: "You are a professional career coach helping create effective cold call scripts for job seekers.",
//         },
//         {
//           role: "user",
//           content: prompt,
//         },
//       ],
//       max_tokens: 1000,
//     });

//     const generatedScript = completion.choices[0].message.content;

//     // Increment usage directly in the database
//     const adminClient = getSupabaseAdminClient();

//     // Get the current month and year
//     const now = new Date();
//     const currentMonth = now.getMonth() + 1;
//     const currentYear = now.getFullYear();

//     // Check if a record exists for the current month
//     const { data, error } = await adminClient
//       .from('user_usage')
//       .select('id')
//       .eq('user_id', userId)
//       .eq('month', currentMonth)
//       .eq('year', currentYear)
//       .single();

//     if (error) {
//       // If no record exists, create one
//       if (error.code === 'PGRST116') {
//         await adminClient
//           .from('user_usage')
//           .insert([{
//             user_id: userId,
//             month: currentMonth,
//             year: currentYear,
//             cold_call_scripts: 1
//           }]);
//       }
//     } else {
//       // If a record exists, increment the usage
//       const { data: currentData } = await adminClient
//         .from('user_usage')
//         .select('cold_call_scripts')
//         .eq('id', data.id)
//         .single();

//       const currentCount = currentData?.cold_call_scripts || 0;
//       await adminClient
//         .from('user_usage')
//         .update({ cold_call_scripts: currentCount + 1 })
//         .eq('id', data.id);
//     }

//     // Return the generated script
//     return NextResponse.json({
//       script: generatedScript,
//       usageInfo: {
//         feature: "coldCallScripts",
//         current: currentUsage,
//         new: newUsage,
//       },
//     });
//   } catch (error) {
//     console.error("Error generating cold call script:", error);
//     return NextResponse.json(
//       { error: "Failed to generate cold call script" },
//       { status: 500 }
//     );
//   }
// }

// export async function GET(req: NextRequest) {
//   try {
//     // Get the current user from the session
//     const supabase = createRouteHandlerClient({ cookies });
//     const { data: { session }, error: sessionError } = await supabase.auth.getSession();

//     if (sessionError || !session) {
//       return NextResponse.json({ error: "Unauthorized - Please log in" }, { status: 401 });
//     }

//     const userId = session.user.id;

//     // Get saved scripts from the database
//     const { data: scripts, error } = await supabase
//       .from('cold_call_scripts')
//       .select('*')
//       .eq('user_id', userId)
//       .order('created_at', { ascending: false });

//     if (error) {
//       console.error('Error fetching cold call scripts:', error);
//       return NextResponse.json({ error: "Failed to fetch cold call scripts" }, { status: 500 });
//     }

//     return NextResponse.json({ scripts });
//   } catch (error) {
//     console.error('Error in GET cold call scripts:', error);
//     return NextResponse.json({ error: "Server error" }, { status: 500 });
//   }
// }
