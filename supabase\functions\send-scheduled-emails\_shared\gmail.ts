// supabase/functions/send-scheduled-emails/_shared/gmail.ts
import {
  encodeBase64 as stdB64,        // standard alphabet (for attachments)
} from "https://deno.land/std@0.224.0/encoding/base64.ts";
import {
  encodeBase64Url,              // URL-safe alphabet (for raw Gmail API message)
} from "https://deno.land/std@0.224.0/encoding/base64url.ts";

// ─── Refresh-token → fresh access-token ────────────────────
export async function getAccessToken(refresh: string): Promise<string> {
  try {
    const res = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      body: new URLSearchParams({
        client_id: Deno.env.get("GOOGLE_CLIENT_ID")!,
        client_secret: Deno.env.get("GOOGLE_CLIENT_SECRET")!,
        refresh_token: refresh,
        grant_type: "refresh_token",
      }),
    });

    if (!res.ok) {
      const errorData = await res.text();
      throw new Error(`OAuth refresh failed: HTTP ${res.status} – ${errorData}`);
    }

    const data = await res.json();
    if (!data.access_token) {
      throw new Error(`OAuth refresh returned no access_token: ${JSON.stringify(data)}`);
    }

    return data.access_token as string;
  } catch (err: any) {
    console.error("Access token refresh failed:", err);
    throw new Error(`Failed to refresh access token: ${err.message}`);
  }
}

// ─── Helpers for building MIME ────────────────────────────
/**
 * Encodes a header string using RFC 2047 for non-ASCII characters.
 * If the string contains non-ASCII characters, the entire string is Base64 encoded.
 * Otherwise, the string is returned as is.
 */
function encodeHeader(value: string): string {
  if (/[^\x00-\x7F]/.test(value)) { // Test for any non-ASCII characters
    const te = new TextEncoder();
    const encodedValue = stdB64(te.encode(value)); // Use standard Base64 from std/encoding/base64.ts
    return `=?UTF-8?B?${encodedValue}?=`;
  }
  return value;
}

/**
 * Encodes the entire MIME message string into a URL-safe Base64 string
 * as required by the Gmail API.
 */
function encodeMimeForGmailApi(mimeStr: string): string {
  return encodeBase64Url(new TextEncoder().encode(mimeStr));
}

async function fetchWithRetry(
  url: string,
  options: RequestInit,
  maxRetries = 3
): Promise<Response> {
  let attempt = 0;
  while (attempt < maxRetries) {
    try {
      const response = await fetch(url, options);
      if (response.ok || response.status < 500) {
        return response;
      }
      // If server error, log and retry
      console.warn(`Request attempt ${attempt + 1} to ${url} returned ${response.status}. Retrying...`);
    } catch (err) {
      console.warn(`Request attempt ${attempt + 1} to ${url} failed:`, err);
    }
    const delay = Math.pow(2, attempt) * 1000 + Math.random() * 500;
    await new Promise((r) => setTimeout(r, delay));
    attempt++;
  }
  // Final attempt
  return fetch(url, options);
}

// ─── Build + send MIME e-mail via Gmail API ────────────────
export async function gmailSend({
  to_addresses,
  cc_addresses = [],
  bcc_addresses = [],
  subject,
  body,
  attachments = [],
  access,
}: {
  to_addresses: string[];
  cc_addresses?: string[];
  bcc_addresses?: string[];
  subject: string;
  body: string;
  attachments?: Array<{ filename: string; mimeType: string; content: string }>; // content is stdB64 string
  access: string;
}): Promise<string> {
  try {
    if (!to_addresses?.length) throw new Error("No recipients specified");
    if (!subject) throw new Error("Subject is required");
    if (!body) throw new Error("Body is required");

    const hasAttachments = attachments.length > 0;
    let mime: string;

    if (!hasAttachments) {
      // Simple HTML email without attachments
      const headerLines = [
        `To: ${to_addresses.join(", ")}`, // Don't encode email addresses
        cc_addresses.length ? `Cc: ${cc_addresses.join(", ")}` : "",
        bcc_addresses.length ? `Bcc: ${bcc_addresses.join(", ")}` : "",
        `Subject: ${encodeHeader(subject)}`,
        "MIME-Version: 1.0",
        "Content-Type: text/html; charset=UTF-8",
        "Content-Transfer-Encoding: 7bit",
      ].filter(Boolean);
      
      mime = headerLines.join("\r\n") + "\r\n\r\n" + body;
    } else {
      // Multipart email with attachments
      const boundary = "boundary_" + Math.random().toString(36).substring(2, 15);
      
      const headerLines = [
        `To: ${to_addresses.join(", ")}`, // Don't encode email addresses
        cc_addresses.length ? `Cc: ${cc_addresses.join(", ")}` : "",
        bcc_addresses.length ? `Bcc: ${bcc_addresses.join(", ")}` : "",
        `Subject: ${encodeHeader(subject)}`,
        "MIME-Version: 1.0",
        `Content-Type: multipart/mixed; boundary="${boundary}"`,
      ].filter(Boolean);
      
      // Build HTML body part
      const htmlPart = [
        `--${boundary}`,
        "Content-Type: text/html; charset=UTF-8",
        "Content-Transfer-Encoding: 7bit",
        "",
        body,
      ].join("\r\n");
      
      // Build attachment parts
      const attachmentParts = attachments.map(att => [
        `--${boundary}`,
        `Content-Type: ${att.mimeType}; name="${encodeHeader(att.filename)}"`,
        `Content-Disposition: attachment; filename="${encodeHeader(att.filename)}"`,
        "Content-Transfer-Encoding: base64",
        "",
        att.content,
      ].join("\r\n"));
      
      // Combine all parts
      const allParts = [
        headerLines.join("\r\n"),
        "", // Blank line after headers
        htmlPart,
        ...attachmentParts,
        `--${boundary}--`, // Final boundary
      ];
      
      mime = allParts.join("\r\n");
    }

    // Debug logging
    console.log("MIME message preview:", mime.substring(0, 500) + "...");
    console.log("Body content:", body);
    console.log("Attachments count:", attachments.length);

    const raw = encodeMimeForGmailApi(mime);

    const response = await fetchWithRetry(
      "https://gmail.googleapis.com/gmail/v1/users/me/messages/send",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${access}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ raw }),
      }
    );

    if (!response.ok) {
      const text = await response.text();
      throw new Error(`Gmail API error ${response.status}: ${text}`);
    }

    const result = await response.json();
    if (!result.id) {
      throw new Error(`Gmail send succeeded with no message ID: ${JSON.stringify(result)}`);
    }

    return result.id as string;
  } catch (err: any) {
    console.error("Gmail send error:", err);
    throw new Error(`Failed to send email: ${err.message}`);
  }
}